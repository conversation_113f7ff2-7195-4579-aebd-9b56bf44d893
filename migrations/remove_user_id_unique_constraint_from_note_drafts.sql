-- 移除笔记草稿表的用户ID唯一约束
-- 执行时间: 2025-06-30
-- 目的: 允许同一用户有多个草稿（针对不同的笔记），但同一笔记只能有一个草稿

-- 检查唯一约束是否存在
SET @constraint_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'note_drafts'
    AND INDEX_NAME = 'idx_user_id'
    AND NON_UNIQUE = 0
);

-- 如果唯一约束存在则删除
SET @sql = IF(@constraint_exists > 0,
    'ALTER TABLE note_drafts DROP INDEX idx_user_id',
    'SELECT ''Unique constraint idx_user_id does not exist'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查普通索引是否存在
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'note_drafts'
    AND INDEX_NAME = 'idx_user_id'
);

-- 如果没有普通索引则添加（用于查询优化）
SET @sql = IF(@index_exists = 0,
    'ALTER TABLE note_drafts ADD KEY idx_user_id (user_id)',
    'SELECT ''Index idx_user_id already exists'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
