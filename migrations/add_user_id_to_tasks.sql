-- 为任务表添加用户ID字段
-- 执行时间: 2024-12-19

-- 添加 user_id 字段
ALTER TABLE tasks 
ADD COLUMN user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID（外键）' 
AFTER id;

-- 添加索引
ALTER TABLE tasks 
ADD KEY idx_user_id (user_id);

-- 添加复合索引
ALTER TABLE tasks 
ADD KEY idx_user_task_type (user_id, task_type);

ALTER TABLE tasks 
ADD KEY idx_user_status (user_id, status);

ALTER TABLE tasks 
ADD KEY idx_user_platform (user_id, platform);

-- 添加外键约束
ALTER TABLE tasks 
ADD CONSTRAINT fk_tasks_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
