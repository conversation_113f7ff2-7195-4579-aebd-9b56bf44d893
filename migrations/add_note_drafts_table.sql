-- 添加笔记草稿表
-- 执行时间: 2024-12-27

-- 创建笔记草稿表
CREATE TABLE IF NOT EXISTS note_drafts (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    note_id BIGINT UNSIGNED NOT NULL COMMENT '笔记ID（外键）',
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID（外键）',
    content LONGTEXT NULL COMMENT '草稿内容',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY idx_note_id (note_id),
    UNIQUE KEY idx_user_id (user_id),
    KEY idx_user_note (user_id, note_id),
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
    FOR<PERSON><PERSON>N KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
