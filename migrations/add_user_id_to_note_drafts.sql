-- 为笔记草稿表添加用户ID字段和相关约束
-- 执行时间: 2024-12-28

-- 添加用户ID字段
ALTER TABLE note_drafts 
ADD COLUMN user_id BIGINT UNSIGNED NULL COMMENT '用户ID（外键）' AFTER note_id;

-- 更新现有记录的user_id（从notes表获取）
UPDATE note_drafts nd 
JOIN notes n ON nd.note_id = n.id 
SET nd.user_id = n.user_id;

-- 将user_id字段设为NOT NULL
ALTER TABLE note_drafts 
MODIFY COLUMN user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID（外键）';

-- 添加外键约束
ALTER TABLE note_drafts 
ADD CONSTRAINT fk_note_drafts_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 添加用户唯一约束（确保每个用户只能有一个草稿）
ALTER TABLE note_drafts 
ADD UNIQUE KEY idx_user_id (user_id);

-- 添加复合索引
ALTER TABLE note_drafts 
ADD KEY idx_user_note (user_id, note_id);
