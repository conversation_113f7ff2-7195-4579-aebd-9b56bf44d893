-- 为任务表添加笔记ID字段
-- 执行时间: 2024-12-28

-- 检查字段是否已存在
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'tasks'
    AND COLUMN_NAME = 'note_id'
);

-- 如果字段不存在则添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE tasks ADD COLUMN note_id BIGINT UNSIGNED NULL COMMENT ''笔记ID（外键，可选，当task_type为4时存在）'' AFTER result',
    'SELECT ''Column note_id already exists'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查索引是否已存在
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'tasks'
    AND INDEX_NAME = 'idx_note_id'
);

-- 如果索引不存在则添加
SET @sql = IF(@index_exists = 0,
    'ALTER TABLE tasks ADD KEY idx_note_id (note_id)',
    'SELECT ''Index idx_note_id already exists'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查外键约束是否已存在
SET @fk_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'tasks'
    AND COLUMN_NAME = 'note_id'
    AND REFERENCED_TABLE_NAME = 'notes'
);

-- 检查notes表是否存在
SET @notes_table_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'notes'
);

-- 如果外键约束不存在且notes表存在则添加
SET @sql = IF(@fk_exists = 0 AND @notes_table_exists > 0,
    'ALTER TABLE tasks ADD CONSTRAINT fk_tasks_note_id FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE SET NULL',
    'SELECT ''Foreign key constraint already exists or notes table not found'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示表结构确认
DESCRIBE tasks;

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'tasks' 
AND COLUMN_NAME = 'note_id';
