#!/bin/bash

# 为notes表添加 status 字段的脚本

# 数据库配置
DB_HOST="rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"
DB_PORT="3306"
DB_USER="aishoucang_mysql"
DB_PASSWORD="Qtt\$123456"
DB_NAME="aishoucang_develop"

echo "🚀 开始为notes表添加 status 字段..."

# 执行SQL命令
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" << 'EOF'

-- 检查字段是否已存在
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'notes'
    AND COLUMN_NAME = 'status'
);

-- 如果字段不存在则添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE notes ADD COLUMN status TINYINT NULL DEFAULT 1 COMMENT ''笔记状态：1正常 2已删除'' AFTER html',
    'SELECT ''Column status already exists'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查索引是否已存在
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'notes'
    AND INDEX_NAME = 'idx_user_status'
);

-- 如果索引不存在则添加
SET @index_sql = IF(@index_exists = 0,
    'ALTER TABLE notes ADD INDEX idx_user_status (user_id, status)',
    'SELECT ''Index idx_user_status already exists'' as message'
);

PREPARE index_stmt FROM @index_sql;
EXECUTE index_stmt;
DEALLOCATE PREPARE index_stmt;

-- 显示表结构确认
DESCRIBE notes;

EOF

if [ $? -eq 0 ]; then
    echo "✅ status 字段和索引添加成功！"
else
    echo "❌ 字段添加失败，请检查数据库连接和权限"
    exit 1
fi

echo "🎉 数据库更新完成！"
