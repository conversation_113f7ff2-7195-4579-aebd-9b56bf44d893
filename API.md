# API 文档
## 用户接口
### 注册用户
```
POST /users/regist

请求示例
{
    "phone": "13800138000",
    "password": "password123"
}

响应示例
{
    "code": 0,
    "message": "成功",
    "data": {
        "user_id": "60f7b0b9e6b57f001c9b9f1a",
        "token": "550e8400-e29b-41d4-a716-446655440000-1714392123"
    }
}

失败示例
{
    "code": 200,
    "message": "用户已存在",
    "data": null
}
```

### 登录用户
```
POST /users/login

请求示例
{
    "phone": "13800138000",
    "password": "password123"
}

响应示例
{
    "code": 0,
    "message": "成功",
    "data": {
        "user_id": "60f7b0b9e6b57f001c9b9f1a",
        "phone": "13800138000",
        "token": "550e8400-e29b-41d4-a716-446655440000-1714392123"
    }
}

失败示例
{
    "code": 201,
    "message": "用户不存在",
    "data": null
}
```

## 收藏夹接口
### 创建收藏夹
```
POST /favorites/create

请求头
Authorization: Bearer your_token_here

请求示例
{
    "name": "我的收藏",
    "cover": "https://example.com/cover.jpg"
}

响应示例
{
    "code": 0,
    "message": "成功",
    "data": {
        "user_id": "60f7b0b9e6b57f001c9b9f1a",
        "name": "我的收藏",
        "cover": "https://example.com/cover.jpg",
        "create_time": "2025-04-19T02:24:00Z",
        "update_time": "2025-04-19T02:24:00Z"
    }
}

失败示例
{
    "code": 203,
    "message": "Token无效",
    "data": null
}

{
    "code": 204,
    "message": "Token已过期",
    "data": null
}

{
    "code": 300,
    "message": "收藏夹已存在",
    "data": null
}
```

### 获取收藏夹列表
```
GET /favorites/list?page=1&page_size=10

请求头
Authorization: Bearer your_token_here

请求参数
page: 页码，从1开始，默认为1
page_size: 每页数量，默认为10

响应示例
{
    "code": 0,
    "message": "成功",
    "data": {
        "favorites": [
            {
                "id": "60f7b0b9e6b57f001c9b9f1a",
                "name": "我的收藏",
                "cover": "https://example.com/cover.jpg",
                "create_time": "2025-04-19T02:24:00Z",
                "update_time": "2025-04-19T02:24:00Z"
            },
            {
                "id": "60f7b0b9e6b57f001c9b9f1b",
                "name": "工作收藏",
                "cover": "https://example.com/work.jpg",
                "create_time": "2025-04-18T10:30:00Z",
                "update_time": "2025-04-18T10:30:00Z"
            }
        ],
        "total": 25,
        "page": 1,
        "page_size": 10
    }
}

失败示例
{
    "code": 203,
    "message": "Token无效",
    "data": null
}

{
    "code": 204,
    "message": "Token已过期",
    "data": null
}
```

### 更新收藏夹
```
POST /favorites/update

请求头
Authorization: Bearer your_token_here

请求示例
{
    "id": "60f7b0b9e6b57f001c9b9f1a",
    "name": "我的新收藏",
    "cover": "https://example.com/new-cover.jpg"
}

响应示例
{
    "code": 0,
    "message": "成功",
    "data": {
        "id": "60f7b0b9e6b57f001c9b9f1a"
    }
}

失败示例
{
    "code": 203,
    "message": "Token无效",
    "data": null
}

{
    "code": 204,
    "message": "Token已过期",
    "data": null
}

{
    "code": 300,
    "message": "收藏夹名称不能为空",
    "data": null
}

{
    "code": 301,
    "message": "收藏夹名称不能超过12个字符",
    "data": null
}

{
    "code": 304,
    "message": "收藏夹名称已存在",
    "data": null
}

{
    "code": 403,
    "message": "参数无效",
    "data": null
}
```

## 书签接口
### 获取书签列表
```
GET /bookmark/list

请求头
Authorization: Bearer your_token_here

请求参数（所有参数都是可选的）
parent_id: 收藏夹ID，字符串类型，如果提供则获取指定收藏夹的书签
tag_names: 标签名称列表，数组类型，如果提供则按标签搜索书签
page: 页码，从1开始，默认为1
page_size: 每页数量，默认为10

使用场景说明：
1. 只传 parent_id：获取指定收藏夹的书签列表（原有功能）
2. 只传 tag_names：在用户全量书签中按标签搜索
3. 同时传 parent_id 和 tag_names：在指定收藏夹内按标签搜索
4. 都不传：返回用户的所有书签

请求示例1：获取指定收藏夹的书签
GET /bookmark/list?parent_id=123&page=1&page_size=10

请求示例2：按标签搜索全量书签
GET /bookmark/list?tag_names=工作&tag_names=重要&page=1&page_size=10

请求示例3：在指定收藏夹内按标签搜索
GET /bookmark/list?parent_id=123&tag_names=工作&page=1&page_size=10

请求示例4：获取用户所有书签
GET /bookmark/list?page=1&page_size=10

响应示例
{
    "code": 0,
    "message": "成功",
    "data": {
        "bookmarks": [
            {
                "id": "1",
                "influencer_name": "技术博主",
                "influencer_avatar": "https://example.com/avatar.jpg",
                "cover": "https://example.com/cover.jpg",
                "title": "React 最佳实践",
                "desc": "这是一篇关于React开发的文章",
                "scheme_url": "https://example.com/article/123",
                "parent_id": "123",
                "create_time": 1714392123000,
                "update_time": 1714392123000,
                "tags": [
                    {
                        "id": "1",
                        "name": "工作",
                        "background_color": "#1890ff",
                        "text_color": "#ffffff"
                    },
                    {
                        "id": "2",
                        "name": "重要",
                        "background_color": "#f5222d",
                        "text_color": "#ffffff"
                    }
                ]
            }
        ],
        "total": 25,
        "page": 1,
        "page_size": 10
    }
}

失败示例
{
    "code": 203,
    "message": "未登录或身份验证失败",
    "data": null
}

{
    "code": 403,
    "message": "无效的收藏夹ID格式",
    "data": null
}

{
    "code": 101,
    "message": "服务不可用",
    "data": null
}
```

### 添加书签
```
POST /bookmark/add

请求头
Authorization: Bearer your_token_here

请求示例
{
    "influencer_name": "技术博主",
    "influencer_avatar": "https://example.com/avatar.jpg",
    "cover": "https://example.com/cover.jpg",
    "title": "React 最佳实践",
    "desc": "这是一篇关于React开发的文章",
    "scheme_url": "https://example.com/article/123",
    "parent_id": "123",
    "tags": ["工作", "重要", "学习"]
}

请求参数说明
influencer_name: 博主名称，可选，字符串类型
influencer_avatar: 博主头像，可选，字符串类型
cover: 封面，必填，字符串类型
title: 标题，必填，字符串类型
desc: 简介，可选，字符串类型
scheme_url: 原生跳转链接，必填，字符串类型
parent_id: 收藏夹ID，必填，字符串类型
tags: 标签列表，可选，字符串数组类型

响应示例
{
    "code": 0,
    "message": "添加书签成功",
    "data": {
        "id": "1",
        "influencer_name": "技术博主",
        "influencer_avatar": "https://example.com/avatar.jpg",
        "cover": "https://example.com/cover.jpg",
        "title": "React 最佳实践",
        "desc": "这是一篇关于React开发的文章",
        "scheme_url": "https://example.com/article/123",
        "parent_id": "123",
        "create_time": 1714392123000,
        "update_time": 1714392123000,
        "tags": [
            {
                "id": "1",
                "name": "工作",
                "background_color": "#1890ff",
                "text_color": "#ffffff"
            },
            {
                "id": "2",
                "name": "重要",
                "background_color": "#f5222d",
                "text_color": "#ffffff"
            }
        ]
    }
}

失败示例
{
    "code": 203,
    "message": "未登录或身份验证失败",
    "data": null
}

{
    "code": 403,
    "message": "无效的收藏夹ID格式",
    "data": null
}

{
    "code": 101,
    "message": "服务不可用",
    "data": null
}
```

### 搜索书签
```
GET /bookmark/search

请求头
Authorization: Bearer your_token_here

请求参数
keyword: 搜索关键词，必填，字符串类型
page: 页码，从1开始，默认为1
page_size: 每页数量，默认为10

请求示例
GET /bookmark/search?keyword=React&page=1&page_size=10

响应示例
{
    "code": 0,
    "message": "成功",
    "data": {
        "bookmarks": [
            {
                "id": "1",
                "influencer_name": "技术博主",
                "influencer_avatar": "https://example.com/avatar.jpg",
                "cover": "https://example.com/cover.jpg",
                "title": "React 最佳实践",
                "desc": "这是一篇关于React开发的文章",
                "scheme_url": "https://example.com/article/123",
                "parent_id": "123",
                "create_time": 1714392123000,
                "update_time": 1714392123000
            }
        ],
        "total": 5,
        "page": 1,
        "page_size": 10
    }
}

失败示例
{
    "code": 203,
    "message": "未登录或身份验证失败",
    "data": null
}

{
    "code": 101,
    "message": "服务不可用",
    "data": null
}
```

## 书签标签管理接口
### 给书签添加标签
```
POST /bookmark/tags/add

请求头
Authorization: Bearer your_token_here

请求示例
{
    "bookmark_id": "123",
    "tag_names": ["工作", "重要", "学习"]
}

响应示例
{
    "code": 0,
    "message": "添加标签成功",
    "data": {
        "bookmark_id": "123",
        "added_count": 3
    }
}

失败示例
{
    "code": 203,
    "message": "未登录或身份验证失败",
    "data": null
}

{
    "code": 403,
    "message": "书签ID格式无效",
    "data": null
}

{
    "code": 403,
    "message": "无权限操作此书签",
    "data": null
}

{
    "code": 310,
    "message": "标签名称不能为空",
    "data": null
}

{
    "code": 311,
    "message": "标签名称不能超过50个字符",
    "data": null
}
```

### 从书签移除标签
```
POST /bookmark/tags/remove

请求头
Authorization: Bearer your_token_here

请求示例
{
    "bookmark_id": "123",
    "tag_ids": ["1", "2", "3"]
}

响应示例
{
    "code": 0,
    "message": "移除标签成功",
    "data": {
        "bookmark_id": "123",
        "removed_count": 3
    }
}

失败示例
{
    "code": 203,
    "message": "未登录或身份验证失败",
    "data": null
}

{
    "code": 403,
    "message": "书签ID格式无效",
    "data": null
}

{
    "code": 403,
    "message": "无权限操作此书签",
    "data": null
}

{
    "code": 403,
    "message": "标签ID格式无效: abc",
    "data": null
}
```

### 获取书签标签
```
GET /bookmark/tags?bookmark_id=123

请求头
Authorization: Bearer your_token_here

响应示例
{
    "code": 0,
    "message": "获取书签标签成功",
    "data": {
        "bookmark_id": "123",
        "tags": [
            {
                "id": "1",
                "name": "工作",
                "background_color": "#1890ff",
                "text_color": "#ffffff"
            },
            {
                "id": "2",
                "name": "重要",
                "background_color": "#f5222d",
                "text_color": "#ffffff"
            },
            {
                "id": "3",
                "name": "学习",
                "background_color": "#52c41a",
                "text_color": "#ffffff"
            }
        ]
    }
}

失败示例
{
    "code": 203,
    "message": "未登录或身份验证失败",
    "data": null
}

{
    "code": 403,
    "message": "书签ID格式无效",
    "data": null
}

{
    "code": 403,
    "message": "无权限查看此书签",
    "data": null
}

{
    "code": 403,
    "message": "书签不存在",
    "data": null
}
```

## 标签接口
### 获取标签列表
```
GET /tags/list

请求头
Authorization: Bearer your_token_here

响应示例
{
    "code": 0,
    "message": "获取标签列表成功",
    "data": {
        "tags": [
            {
                "id": "1",
                "name": "工作",
                "background_color": "#1890ff",
                "text_color": "#ffffff",
                "create_time": 1714392123000,
                "update_time": 1714392123000,
                "count": 5
            },
            {
                "id": "2",
                "name": "学习",
                "background_color": "#52c41a",
                "text_color": "#ffffff",
                "create_time": 1714392124000,
                "update_time": 1714392124000,
                "count": 3
            }
        ]
    }
}

失败示例
{
    "code": 203,
    "message": "未登录或身份验证失败",
    "data": null
}

{
    "code": 101,
    "message": "服务不可用",
    "data": null
}
```

### 创建标签
```
POST /tags/create

请求头
Authorization: Bearer your_token_here

请求示例
{
    "name": "工作",
    "background_color": "#1890ff",
    "text_color": "#ffffff"
}

请求参数说明
name: 标签名称，必填，不能为空，最大长度50个字符
background_color: 标签背景颜色，可选，十六进制颜色值，默认为 #1890ff
text_color: 标签文字颜色，可选，十六进制颜色值，默认为 #ffffff

响应示例
{
    "code": 0,
    "message": "创建标签成功",
    "data": {
        "id": "1",
        "name": "工作",
        "background_color": "#1890ff",
        "text_color": "#ffffff",
        "create_time": 1714392123000,
        "update_time": 1714392123000
    }
}

失败示例
{
    "code": 203,
    "message": "未登录或身份验证失败",
    "data": null
}

{
    "code": 310,
    "message": "标签名称不能为空",
    "data": null
}

{
    "code": 311,
    "message": "标签名称不能超过50个字符",
    "data": null
}

{
    "code": 312,
    "message": "标签名称已存在",
    "data": null
}

{
    "code": 314,
    "message": "标签颜色格式无效",
    "data": null
}

{
    "code": 315,
    "message": "标签数量已达上限，每个用户最多创建50个标签",
    "data": null
}

{
    "code": 101,
    "message": "服务不可用",
    "data": null
}

{
    "code": 102,
    "message": "数据库错误",
    "data": null
}
```

### 删除标签
```
POST /tags/delete

请求头
Authorization: Bearer your_token_here

请求示例
{
    "id": "1"
}

请求参数说明
id: 标签ID，必填，字符串类型

响应示例
{
    "code": 0,
    "message": "删除标签成功",
    "data": null
}

失败示例
{
    "code": 203,
    "message": "未登录或身份验证失败",
    "data": null
}

{
    "code": 313,
    "message": "标签不存在",
    "data": null
}

{
    "code": 403,
    "message": "无权限删除此标签",
    "data": null
}

{
    "code": 101,
    "message": "服务错误，请稍后再试",
    "data": null
}
```

## 笔记接口
### 创建笔记
```
POST /note/create

请求头
Authorization: Bearer your_token_here
Content-Type: application/json

请求体
{
    "parent_id": "123",  // 合集ID（可选）
    "title": "我的笔记标题",  // 笔记标题（必填）
    "cover": "https://example.com/cover.jpg",  // 笔记封面（可选）
    "desc": "这是笔记的描述",  // 笔记描述（可选）
    "content": "这是笔记的详细内容...",  // 笔记内容（可选）
    "html": "<h1>HTML格式的内容</h1>"  // HTML内容（可选）
}

响应示例
{
    "code": 0,
    "message": "笔记创建成功",
    "data": {
        "id": "456"
    }
}

失败示例
{
    "code": 203,
    "message": "未登录或身份验证失败",
    "data": null
}

{
    "code": 403,
    "message": "笔记标题不能为空",
    "data": null
}

{
    "code": 403,
    "message": "笔记标题不能超过255个字符",
    "data": null
}

{
    "code": 403,
    "message": "无效的合集ID格式",
    "data": null
}

{
    "code": 101,
    "message": "服务暂时不可用",
    "data": null
}
```

### 获取笔记详情
```
GET /note/detail?id={note_id}

请求头
Authorization: Bearer your_token_here

请求参数
- id: 笔记ID（必填）

请求示例
GET /note/detail?id=123

响应示例
{
    "code": 0,
    "message": "获取笔记详情成功",
    "data": {
        "id": "123",
        "parent_id": "456",
        "user_id": "789",
        "title": "我的笔记标题",
        "cover": "https://example.com/cover.jpg",
        "desc": "这是笔记的描述",
        "content": "这是笔记的详细内容...",
        "create_time": "2023-12-01T10:00:00Z",
        "update_time": "2023-12-01T12:00:00Z"
    }
}

失败示例
{
    "code": 203,
    "message": "未登录或身份验证失败",
    "data": null
}

{
    "code": 403,
    "message": "笔记不存在或无权限访问",
    "data": null
}

{
    "code": 403,
    "message": "笔记ID格式错误",
    "data": null
}

{
    "code": 101,
    "message": "服务暂时不可用",
    "data": null
}
```

## 腾讯云语音识别接口
### 腾讯云语音识别回调接口
```
POST /tencent_cloud/asr/callback

说明：
此接口用于接收腾讯云语音识别服务的回调通知。
腾讯云会在语音识别任务完成后，通过HTTP POST方法调用此接口。
Content-Type: application/x-www-form-urlencoded

回调参数说明：
code: 任务状态码，0为成功，其他值表示失败
message: 失败原因文字描述，成功时此值为空
requestId: 任务唯一标识，与录音识别请求中返回的 TaskId 一致
appid: 腾讯云应用 ID
projectid: 腾讯云项目 ID
audioUrl: 语音 url（可选）
text: 识别出的结果文本（可选）
resultDetail: 详细识别结果（可选）
audioTime: 语音总时长（可选）

成功回调示例：
code=0&requestId=4000048858&appid=1251600000&projectid=0&text=%5B0%3A1.640%2C0%3A4.600%5D++%E6%88%91%E5%9C%A8%E9%A9%AC%E4%B8%8A%E4%B8%8A%E5%A4%9C%E7%8F%AD%E3%80%82%0A%5B0%3A5.420%2C0%3A7.820%5D++%E6%98%8E%E5%A4%A9%E6%97%A9%E4%B8%8A%E8%AF%B4%E5%93%88%E3%80%82%0A&audioTime=8.420000&message=&resultDetail=

失败回调示例：
code=4001&requestId=4000048859&appid=1251600000&projectid=0&message=音频格式不支持&audioTime=0

响应示例（成功）：
{
    "code": 0,
    "message": "回调处理成功"
}

响应示例（失败）：
{
    "code": 403,
    "message": "解析回调数据失败: 缺少requestId字段"
}

{
    "code": 500,
    "message": "处理回调数据失败: 数据库连接错误"
}
```