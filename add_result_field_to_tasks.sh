#!/bin/bash

# 为tasks表添加 result 字段的脚本

# 数据库配置
DB_HOST="rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"
DB_PORT="3306"
DB_USER="aishoucang_mysql"
DB_PASSWORD="Qtt\$123456"
DB_NAME="aishoucang_develop"

echo "🚀 开始为tasks表添加 result 字段..."

# 执行SQL命令
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" << 'EOF'

-- 检查字段是否已存在
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'tasks'
    AND COLUMN_NAME = 'result'
);

-- 如果字段不存在则添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE tasks ADD COLUMN result TEXT NULL COMMENT ''任务结果'' AFTER status',
    'SELECT ''Column result already exists'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示表结构确认
DESCRIBE tasks;

EOF

if [ $? -eq 0 ]; then
    echo "✅ result 字段添加成功！"
else
    echo "❌ 字段添加失败，请检查数据库连接和权限"
    exit 1
fi

echo "🎉 数据库更新完成！"
