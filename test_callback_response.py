#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time

# 服务器地址
SERVER_URL = "http://127.0.0.1:8080"

def test_callback_response_time():
    """测试回调响应时间"""
    print("\n🎯 测试: 腾讯云语音识别回调响应时间")
    
    # 模拟腾讯云回调数据
    callback_data = {
        "code": "0",
        "message": "",
        "requestId": "12345678901234567890",
        "appid": "1234567890",
        "projectid": "0",
        "audioUrl": "https://example.com/test.mp3",
        "text": "这是一段测试语音识别结果",
        "resultDetail": "",
        "audioTime": "10.5"
    }
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{SERVER_URL}/tencent_cloud/asr/callback", 
            data=callback_data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000  # 转换为毫秒
        
        print(f"状态码: {response.status_code}")
        print(f"响应时间: {response_time:.2f}ms")
        print(f"响应体长度: {len(response.content)} 字节")
        print(f"响应内容: {response.text}")
        
        # 检查是否快速响应（应该在100ms内）
        if response_time < 100:
            print("✅ 响应时间符合预期（< 100ms）")
        else:
            print("⚠️  响应时间较慢（> 100ms）")
            
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 回调测试失败: {e}")
        return False

def test_callback_with_error():
    """测试回调错误情况"""
    print("\n🎯 测试: 腾讯云语音识别回调（错误情况）")
    
    # 模拟腾讯云回调错误数据
    callback_data = {
        "code": "4001",
        "message": "语音识别失败",
        "requestId": "12345678901234567891",
        "appid": "1234567890",
        "projectid": "0"
    }
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{SERVER_URL}/tencent_cloud/asr/callback", 
            data=callback_data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000
        
        print(f"状态码: {response.status_code}")
        print(f"响应时间: {response_time:.2f}ms")
        print(f"响应体长度: {len(response.content)} 字节")
        print(f"响应内容: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 回调测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试腾讯云语音识别回调响应优化")
    
    # 测试成功回调
    success1 = test_callback_response_time()
    
    # 测试错误回调
    success2 = test_callback_with_error()
    
    if success1 and success2:
        print("\n✅ 所有测试通过！回调响应已优化为立即返回")
    else:
        print("\n❌ 部分测试失败！")
