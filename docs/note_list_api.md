# 笔记列表接口文档

## 接口概述

获取用户的笔记列表，支持分页和按合集过滤。

## 接口信息

- **URL**: `/note/list`
- **方法**: `GET`
- **需要认证**: 是（需要在Header中提供有效的Token）

## 请求参数

### Query Parameters

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| parent_id | string | 否 | - | 合集ID，如果指定则只返回该合集下的笔记 |
| page | number | 否 | 1 | 页码，从1开始 |
| page_size | number | 否 | 10 | 每页数量，范围1-100 |

### Headers

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 是 | 用户认证Token |

## 请求示例

### 获取所有笔记（第一页）
```http
GET /note/list?page=1&page_size=10
Authorization: Bearer your_token_here
```

### 获取指定合集下的笔记
```http
GET /note/list?parent_id=123&page=1&page_size=20
Authorization: Bearer your_token_here
```

### 获取第二页笔记
```http
GET /note/list?page=2&page_size=15
Authorization: Bearer your_token_here
```

## 响应格式

### 成功响应

```json
{
  "code": 0,
  "message": "获取笔记列表成功",
  "data": {
    "notes": [
      {
        "id": "123",
        "parent_id": "456",
        "user_id": "789",
        "title": "我的第一篇笔记",
        "cover": "https://example.com/cover.jpg",
        "desc": "这是笔记的描述",
        "content": "笔记的详细内容",
        "html": "<p>笔记的HTML内容</p>",
        "create_time": "2025-06-24T08:00:00Z",
        "update_time": "2025-06-24T09:00:00Z"
      }
    ],
    "total": 25,
    "page": 1,
    "page_size": 10
  }
}
```

### 错误响应

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 203 | 未登录或身份验证失败 | 检查Token是否有效 |
| 403 | 参数无效 | 检查请求参数格式和范围 |
| 101 | 服务暂时不可用 | 检查服务器状态和数据库连接 |
| 102 | 数据库错误 | 联系管理员检查数据库 |

#### 参数错误示例
```json
{
  "code": 403,
  "message": "页码必须大于0",
  "data": null
}
```

#### 认证失败示例
```json
{
  "code": 203,
  "message": "未登录或身份验证失败",
  "data": null
}
```

## 业务规则

1. **分页限制**: 每页数量必须在1-100之间
2. **页码验证**: 页码必须大于0
3. **用户权限**: 只能获取当前登录用户的笔记
4. **合集过滤**: 如果指定了parent_id，只返回该合集下的笔记
5. **排序规则**: 按创建时间倒序排列（最新的在前）

## 响应字段说明

### 笔记对象字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | string | 笔记ID |
| parent_id | string | 合集ID（可为空） |
| user_id | string | 用户ID |
| title | string | 笔记标题 |
| cover | string | 笔记封面URL |
| desc | string | 笔记描述 |
| content | string | 笔记内容（可为空） |
| html | string | 笔记HTML内容 |
| create_time | string | 创建时间（ISO 8601格式） |
| update_time | string | 更新时间（ISO 8601格式） |

### 分页信息字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| notes | array | 笔记列表 |
| total | number | 总笔记数量 |
| page | number | 当前页码 |
| page_size | number | 每页数量 |

## 使用场景

1. **笔记管理**: 在笔记管理界面展示用户的所有笔记
2. **合集浏览**: 查看特定合集下的笔记列表
3. **分页浏览**: 当笔记数量较多时进行分页展示
4. **搜索结果**: 配合搜索功能展示搜索结果

## 注意事项

1. 接口返回的时间格式为UTC时间，前端需要根据用户时区进行转换
2. 如果用户没有笔记，返回空数组但total为0
3. parent_id为空字符串时会被忽略，等同于不传该参数
4. 建议前端实现合理的分页大小，避免一次性加载过多数据
