# 收藏夹位置交换API

## 接口说明

此接口用于交换两个收藏夹的位置，通过交换它们的排序值（order）实现。

## 请求

- **URL**: `/favorites/swap`
- **方法**: `POST`
- **认证**: 需要用户登录（Bearer <PERSON>ken）

### 请求头

```
Content-Type: application/json
Authorization: Bearer your_token_here
```

### 请求体

```json
{
  "source_id": "65f2a7b8c9d0e1f2a3b4c5d6",
  "target_id": "65f2a7b8c9d0e1f2a3b4c5d7"
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| source_id | String | 是 | 源收藏夹ID |
| target_id | String | 是 | 目标收藏夹ID |

## 响应

### 成功响应

```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "success": true
  }
}
```

### 错误响应

#### 未登录

```json
{
  "code": 203,
  "message": "登录信息缺失",
  "data": null
}
```

#### 收藏夹不存在

```json
{
  "code": 302,
  "message": "收藏夹不存在",
  "data": null
}
```

#### 无效的收藏夹ID

```json
{
  "code": 306,
  "message": "收藏夹ID格式无效",
  "data": null
}
```

#### 数据库错误

```json
{
  "code": 102,
  "message": "数据库错误",
  "data": null
}
```

## 使用示例

### cURL

```bash
curl -X POST \
  http://your-api-domain/favorites/swap \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer your_token_here' \
  -d '{
    "source_id": "65f2a7b8c9d0e1f2a3b4c5d6",
    "target_id": "65f2a7b8c9d0e1f2a3b4c5d7"
  }'
```

### JavaScript

```javascript
const swapFavorites = async (sourceId, targetId) => {
  const response = await fetch('http://your-api-domain/favorites/swap', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer your_token_here'
    },
    body: JSON.stringify({
      source_id: sourceId,
      target_id: targetId
    })
  });
  
  const result = await response.json();
  return result;
};
```

## 注意事项

1. 此接口只交换两个收藏夹的位置，不会影响其他收藏夹的排序
2. 两个收藏夹必须属于同一个用户
3. 如果任一收藏夹不存在，将返回错误
4. 交换位置后，收藏夹列表的排序将立即更新
