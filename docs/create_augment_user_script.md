# Augment用户创建脚本使用说明

## 概述

`create_augment_user.sh` 是一个交互式shell脚本，用于快速创建Augment用户。该脚本提供了友好的用户界面，自动化了用户创建过程。

## 功能特性

- ✅ **环境选择**: 支持开发、预发布、生产环境
- ✅ **用户名验证**: 自动验证用户名格式和长度
- ✅ **密码生成**: 自动生成12位强密码
- ✅ **会员设置**: 自动设置为会员，默认30天有效期
- ✅ **安全确认**: 多重确认机制，防止误操作
- ✅ **信息保存**: 可选择将用户信息保存到文件
- ✅ **彩色输出**: 友好的彩色终端输出

## 使用方法

### 1. 基本使用

```bash
# 进入项目目录
cd /path/to/aishoucang_rust

# 运行脚本
./scripts/create_augment_user.sh
```

### 2. 操作流程

#### 步骤1: 选择环境
```
🚀 Augment用户创建工具
========================
ℹ️  请选择要创建用户的环境：
1) develop (开发环境)
2) pre (预发布环境)
3) production (生产环境)

请输入选项 (1-3): 1
✅ 已选择环境: develop
```

#### 步骤2: 输入用户名
```
请输入用户名 (3-50个字符): test_user_001
```

**用户名规则**:
- 长度: 3-50个字符
- 格式: 只能包含字母、数字、下划线(_)、中划线(-)
- 示例: `admin`, `test_user`, `user-001`, `demo123`

#### 步骤3: 自动生成信息
```
✅ 已生成密码: Kx9mP2nQ8vR1
✅ 会员到期时间: 2024-02-15 14:30:25
```

#### 步骤4: 确认创建
```
ℹ️  准备创建用户...
ℹ️  环境: develop
ℹ️  数据库: aishoucang_develop
ℹ️  用户名: test_user_001
ℹ️  密码: Kx9mP2nQ8vR1
ℹ️  会员状态: 是
ℹ️  会员到期: 2024-02-15 14:30:25

确认创建用户吗？(y/N): y
```

#### 步骤5: 创建成功
```
✅ 🎉 用户创建成功！

📋 用户信息：
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
环境:     develop
用户名:   test_user_001
密码:     Kx9mP2nQ8vR1
会员状态: 是
到期时间: 2024-02-15 14:30:25
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

⚠️  请妥善保存用户名和密码信息！

是否将用户信息保存到文件？(y/N): y
✅ 用户信息已保存到: augment_user_test_user_001_20240116_143025.txt
```

## 环境配置

### 数据库连接配置

脚本使用以下环境变量（如果未设置则使用默认值）：

```bash
export MYSQL_HOST="your-mysql-host"
export MYSQL_PORT="3306"
export MYSQL_USERNAME="your-username"
export MYSQL_PASSWORD="your-password"
```

### 默认配置
```bash
MYSQL_HOST="rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"
MYSQL_PORT="3306"
MYSQL_USERNAME="aishoucang_mysql"
MYSQL_PASSWORD="Qtt$123456"
```

## 环境对应的数据库

| 环境 | 数据库名称 |
|------|------------|
| develop | aishoucang_develop |
| pre | aishoucang_pre |
| production | aishoucang |

## 生成的密码特性

- **长度**: 12位字符
- **字符集**: 大小写字母、数字
- **安全性**: 使用OpenSSL随机生成
- **示例**: `Kx9mP2nQ8vR1`, `Mn7kL4pX9wE2`

## 会员设置

- **默认状态**: 自动设置为会员
- **有效期**: 30天
- **到期时间**: 从创建时间开始计算

## 错误处理

### 常见错误及解决方案

1. **用户名已存在**
   ```
   ❌ 用户创建失败！
   可能的原因：
     - 用户名已存在
   ```
   **解决**: 使用不同的用户名

2. **数据库连接失败**
   ```
   ❌ 用户创建失败！
   可能的原因：
     - 数据库连接失败
   ```
   **解决**: 检查网络连接和数据库配置

3. **用户名格式错误**
   ```
   ❌ 用户名只能包含字母、数字、下划线和中划线
   ```
   **解决**: 使用符合规则的用户名

## 安全注意事项

1. **生产环境**: 在生产环境创建用户时会有额外确认
2. **密码保存**: 建议将生成的密码保存到安全的地方
3. **权限控制**: 确保只有授权人员可以执行此脚本
4. **日志记录**: 脚本执行会在数据库中留下记录

## 文件输出

如果选择保存用户信息到文件，会生成如下格式的文件：

**文件名格式**: `augment_user_{用户名}_{时间戳}.txt`

**文件内容**:
```
Augment用户信息
================
创建时间: 2024-01-16 14:30:25
环境:     develop
用户名:   test_user_001
密码:     Kx9mP2nQ8vR1
会员状态: 是
到期时间: 2024-02-15 14:30:25
================
```

## 测试登录

创建用户后，可以使用以下方式测试登录：

### cURL测试
```bash
curl -X POST "http://your-domain.com/augment/users/login" \
  -H "Content-Type: application/json" \
  -d '{
    "account": "test_user_001",
    "password": "Kx9mP2nQ8vR1"
  }'
```

### 预期响应
```json
{
    "code": 0,
    "message": "操作成功",
    "data": {
        "user_id": "123",
        "account": "test_user_001",
        "is_member": true,
        "member_expire_time": "2024-02-15T14:30:25Z",
        "token": "augment-550e8400-e29b-41d4-a716-************-**********"
    }
}
```

## 系统要求

- **操作系统**: Linux/macOS
- **依赖工具**: 
  - `mysql` 客户端
  - `openssl`
  - `bash` (版本 4.0+)
- **网络**: 能够连接到MySQL数据库

## 故障排除

如果脚本无法运行，请检查：

1. **执行权限**: `chmod +x scripts/create_augment_user.sh`
2. **MySQL客户端**: `which mysql`
3. **OpenSSL**: `which openssl`
4. **网络连接**: `ping your-mysql-host`

## 更新日志

- **v1.0**: 初始版本，支持基本用户创建功能
- 支持环境选择、密码生成、会员设置
- 添加彩色输出和错误处理
