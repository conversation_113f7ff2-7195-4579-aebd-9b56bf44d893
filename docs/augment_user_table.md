# Augment用户表功能说明

## 概述

本次更新新增了 `augment_users` 表，用于管理Augment用户的账号信息、会员状态等数据。

## 表结构

### augment_users 表

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | BIGINT UNSIGNED | 用户ID | 主键，自增 |
| account | VARCHAR(100) | 账号 | 非空，唯一 |
| password | VARCHAR(255) | 密码（哈希值） | 非空 |
| is_member | BOOLEAN | 是否是会员 | 非空，默认false |
| member_expire_time | TIMESTAMP | 会员到期时间 | 可空 |
| created_at | TIMESTAMP | 创建时间 | 非空，默认当前时间 |
| updated_at | TIMESTAMP | 更新时间 | 非空，自动更新 |

### 索引

- `PRIMARY KEY (id)` - 主键索引
- `UNIQUE KEY idx_account (account)` - 账号唯一索引
- `KEY idx_is_member (is_member)` - 会员状态索引
- `KEY idx_member_expire (member_expire_time)` - 会员到期时间索引

## 代码结构

### 1. 模型层 (Model)

**文件**: `src/models/mysql/augment_user.rs`

```rust
pub struct MySqlAugmentUser {
    pub id: u64,
    pub account: String,
    pub password: String,
    pub is_member: bool,
    pub member_expire_time: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
```

**主要方法**:
- `new()` - 创建新用户
- `new_member()` - 创建会员用户
- `set_member()` - 设置会员状态
- `is_valid_member()` - 检查会员是否有效
- `table_name()` - 获取表名

### 2. 服务层 (Service)

**文件**: `src/services/mysql/augment_user_service.rs`

```rust
pub struct MySqlAugmentUserService {
    pool: Arc<Pool<MySql>>,
}
```

**主要方法**:
- `create_user()` - 创建新用户
- `get_user_by_id()` - 根据ID获取用户
- `get_user_by_account()` - 根据账号获取用户
- `verify_login()` - 验证用户登录
- `set_membership()` - 设置会员状态
- `is_valid_member()` - 检查是否为有效会员
- `list_users()` - 获取用户列表（分页）
- `count_users()` - 获取用户总数
- `delete_user()` - 删除用户
- `update_password()` - 更新密码

### 3. 数据库表结构管理

**文件**: `src/db/schema_manager.rs`

在 `initialize_schema()` 方法中新增了对 `augment_users` 表的创建逻辑。

## 部署脚本

### 1. SQL脚本

**文件**: `scripts/create_augment_users_table.sql`

包含创建 `augment_users` 表的完整SQL语句。

### 2. 部署脚本

**文件**: `scripts/create_augment_users_table.sh`

自动化部署脚本，支持不同环境：

```bash
# 开发环境
./scripts/create_augment_users_table.sh develop

# 预发布环境
./scripts/create_augment_users_table.sh pre

# 生产环境
./scripts/create_augment_users_table.sh production
```

## 使用示例

### 创建用户服务实例

```rust
use crate::services::mysql::MySqlAugmentUserService;

let service = MySqlAugmentUserService::new(pool);
```

### 创建新用户

```rust
let user = service.create_user(
    "test_account".to_string(),
    "hashed_password".to_string()
).await?;
```

### 设置会员状态

```rust
let expire_time = Utc::now() + Duration::days(30);
let user = service.set_membership(
    user_id,
    true,
    Some(expire_time)
).await?;
```

### 验证登录

```rust
let user = service.verify_login(
    "test_account",
    "password"
).await?;
```

## 注意事项

1. **密码安全**: 当前密码验证使用简单比较，生产环境应使用密码哈希验证
2. **会员到期**: `member_expire_time` 为 `None` 表示永久会员
3. **账号唯一性**: 账号字段有唯一约束，重复账号会创建失败
4. **自动表创建**: 应用启动时会自动检查并创建表结构

## 编译和测试

```bash
# 检查编译
cargo check

# 编译项目
cargo build

# 运行测试
cargo test
```

所有代码已通过编译测试，可以正常使用。
