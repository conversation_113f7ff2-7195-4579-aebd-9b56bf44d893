# 创建笔记接口文档

## 概述

本文档介绍了新增的创建笔记接口的使用方法和实现细节。

## 接口信息

- **接口路径**: `POST /note/create`
- **需要认证**: 是（Bearer Token）
- **内容类型**: `application/json`

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| title | string | 是 | 笔记标题，不能为空，最大长度255字符 |
| content | string | 否 | 笔记内容 |
| parent_id | string | 否 | 合集ID，如果笔记属于某个合集 |
| cover | string | 否 | 笔记封面图片URL |
| desc | string | 否 | 笔记描述 |
| html | string | 否 | HTML格式的笔记内容 |

## 请求示例

```bash
curl -X POST http://localhost:8080/note/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token_here" \
  -d '{
    "title": "我的第一篇笔记",
    "content": "这是笔记的内容...",
    "desc": "这是一篇测试笔记",
    "cover": "https://example.com/cover.jpg",
    "html": "<h1>我的笔记</h1><p>内容...</p>"
  }'
```

## 响应格式

### 成功响应

```json
{
  "code": 0,
  "message": "笔记创建成功",
  "data": {
    "id": "123"
  }
}
```

### 错误响应

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 203 | 未登录或身份验证失败 | 检查Token是否有效 |
| 403 | 参数无效 | 检查请求参数格式和内容 |
| 101 | 服务暂时不可用 | 检查服务器状态和数据库连接 |
| 102 | 数据库错误 | 联系管理员检查数据库 |

## 业务规则

1. **标题验证**: 笔记标题不能为空，且不能超过255个字符
2. **用户权限**: 只能为当前登录用户创建笔记
3. **合集验证**: 如果指定了parent_id，系统会验证合集是否存在（当前版本暂时跳过此验证）
4. **自动时间戳**: 创建时间和更新时间由系统自动设置

## 实现细节

### 文件结构

```
src/
├── dto/note.rs                 # 添加了CreateNoteRequest和CreateNoteResponse
├── handlers/note/create.rs     # 创建笔记的处理器
├── handlers/note/mod.rs        # 导出create_note处理函数
├── services/mysql/note_service.rs  # 添加了create_note方法
└── routes.rs                   # 添加了/note/create路由
```

### 核心逻辑

1. **身份验证**: 从请求头中提取Bearer Token并验证用户身份
2. **参数验证**: 验证必填字段和字段长度限制
3. **数据库操作**: 调用MySQL服务创建笔记记录
4. **响应处理**: 返回创建成功的笔记ID或错误信息

## 测试

项目提供了测试脚本来验证接口功能：

```bash
# 使用默认配置测试
./scripts/test_create_note_api.sh

# 指定服务器地址和Token
./scripts/test_create_note_api.sh http://localhost:8080 your_token_here
```

## 相关接口

- `GET /note/detail?id={note_id}` - 获取笔记详情
- `POST /note/update` - 更新笔记

## 注意事项

1. 确保在调用接口前已经获得有效的认证Token
2. 笔记内容支持纯文本和HTML两种格式
3. 封面图片URL需要是有效的HTTP/HTTPS地址
4. 合集功能需要配合合集管理接口使用

## 更新日志

- **2024-12-XX**: 新增创建笔记接口
- 支持基本的笔记创建功能
- 包含完整的参数验证和错误处理
- 提供测试脚本和文档
