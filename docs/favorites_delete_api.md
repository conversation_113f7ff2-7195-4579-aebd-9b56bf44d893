# 删除收藏夹API

## 接口说明

此接口用于删除指定的收藏夹，同时会删除该收藏夹下的所有书签。

## 请求

- **URL**: `/favorites/delete`
- **方法**: `POST`
- **认证**: 需要用户登录（Bearer <PERSON>ken）

### 请求头

```
Content-Type: application/json
Authorization: Bearer your_token_here
```

### 请求体

```json
{
  "id": "65f2a7b8c9d0e1f2a3b4c5d6"
}
```

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | String | 是 | 要删除的收藏夹ID |

## 响应

### 成功响应

```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "id": "65f2a7b8c9d0e1f2a3b4c5d6",
    "deleted_bookmarks_count": 5
  }
}
```

| 参数 | 类型 | 描述 |
|------|------|------|
| id | String | 被删除的收藏夹ID |
| deleted_bookmarks_count | Number | 被删除的书签数量 |

### 错误响应

#### 未登录

```json
{
  "code": 203,
  "message": "登录信息缺失",
  "data": null
}
```

#### 收藏夹不存在

```json
{
  "code": 302,
  "message": "收藏夹不存在",
  "data": null
}
```

#### 无效的收藏夹ID

```json
{
  "code": 306,
  "message": "收藏夹ID格式无效",
  "data": null
}
```

#### 数据库错误

```json
{
  "code": 102,
  "message": "数据库错误",
  "data": null
}
```

## 使用示例

### cURL

```bash
curl -X POST \
  http://your-api-domain/favorites/delete \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer your_token_here' \
  -d '{
    "id": "65f2a7b8c9d0e1f2a3b4c5d6"
  }'
```

### JavaScript

```javascript
const deleteFavorite = async (favoriteId) => {
  const response = await fetch('http://your-api-domain/favorites/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer your_token_here'
    },
    body: JSON.stringify({
      id: favoriteId
    })
  });
  
  const result = await response.json();
  return result;
};
```

## 注意事项

1. 删除收藏夹操作不可撤销，请谨慎使用
2. 删除收藏夹会同时删除该收藏夹下的所有书签
3. 只能删除属于当前用户的收藏夹
4. 删除成功后，响应中会返回被删除的书签数量
