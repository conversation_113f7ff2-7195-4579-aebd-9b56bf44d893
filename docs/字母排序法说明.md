# 收藏夹拖动排序功能 - 字母排序法说明

## 概述

我们使用"字母排序法"（Lexicographical Ordering）来实现收藏夹的拖动排序功能。这种方法使用字符串而非数字来表示排序值，可以在任意两个相邻项之间插入几乎无限多的新项，而不需要重新平衡整个列表。

## 排序原理

1. **字符串比较规则**：字符串按照字典序（lexicographical order）进行比较，即逐字符比较ASCII值。
   - 例如："a" < "b" < "c" < ... < "z"
   - "aa" < "ab" < "ac" < ... < "az"
   - "a" < "aa" < "ab" < ... < "az" < "b"

2. **初始排序值**：新创建的收藏夹默认排序值为"a"。

3. **插入新项**：当需要在两个相邻项之间插入新项时，生成一个字典序在它们之间的新字符串。
   - 例如，在"a"和"b"之间可以插入"aa"、"ab"等
   - 在"a"和"aa"之间可以插入"aaa"、"aab"等

## API接口说明

### 1. 获取收藏夹列表 `/favorites/list`

- 返回的收藏夹列表已按`order`字段排序
- 每个收藏夹项包含`order`字段，类型为字符串

### 2. 计算排序值 `/favorites/calculate_order`

- **请求参数**：
  ```json
  {
    "prev_id": "前一个收藏夹ID（可选）",
    "next_id": "后一个收藏夹ID（可选）"
  }
  ```
  - `prev_id`：前一个收藏夹ID，如果为空则表示放在最前面
  - `next_id`：后一个收藏夹ID，如果为空则表示放在最后面

- **响应**：
  ```json
  {
    "code": 0,
    "message": "成功",
    "data": {
      "order": "计算得到的排序值"
    }
  }
  ```

### 3. 更新收藏夹排序 `/favorites/update_order`

- **请求参数**：
  ```json
  {
    "id": "收藏夹ID",
    "order": "排序值"
  }
  ```

- **响应**：
  ```json
  {
    "code": 0,
    "message": "成功",
    "data": {
      "id": "收藏夹ID"
    }
  }
  ```

## 前端实现建议

### 拖动排序实现流程

1. **获取收藏夹列表**：调用`/favorites/list`接口获取已排序的收藏夹列表。

2. **拖动操作**：
   - 用户拖动收藏夹到新位置
   - 确定拖动后的前一个和后一个收藏夹ID

3. **计算新排序值**：
   - 调用`/favorites/calculate_order`接口，传入前一个和后一个收藏夹ID
   - 获取计算得到的新排序值

4. **更新排序**：
   - 调用`/favorites/update_order`接口，传入收藏夹ID和新排序值
   - 更新本地列表顺序

### 示例代码（伪代码）

```javascript
// 拖动排序处理函数
async function handleDragSort(draggedItemId, newIndex, items) {
  // 确定前一个和后一个收藏夹ID
  const prevId = newIndex > 0 ? items[newIndex - 1].id : null;
  const nextId = newIndex < items.length - 1 ? items[newIndex].id : null;
  
  // 计算新排序值
  const response = await api.post('/favorites/calculate_order', {
    prev_id: prevId,
    next_id: nextId
  });
  
  if (response.code === 0) {
    const newOrder = response.data.order;
    
    // 更新排序
    await api.post('/favorites/update_order', {
      id: draggedItemId,
      order: newOrder
    });
    
    // 刷新列表
    await fetchFavoritesList();
  }
}
```

## 优势

1. **无需重新平衡**：即使频繁拖动排序，也不需要重新计算所有项的排序值。

2. **支持无限插入**：可以在任意两个相邻项之间插入几乎无限多的新项。

3. **性能高效**：只需更新被拖动项的排序值，不影响其他项。

4. **稳定可靠**：不会有数值精度问题，排序结果稳定可靠。

## 注意事项

1. 排序值是字符串类型，不是数字类型。

2. 在极端情况下（例如在同一位置反复插入大量项目），可能会生成很长的排序值字符串，但这种情况在正常使用中几乎不会发生。

3. 前端展示时，不需要显示排序值，只需按照返回的顺序展示即可。
