# 笔记草稿创建接口优化

## 概述

优化了 `/note/draft/create` 接口的行为，使其在发现当前笔记已有草稿时，不再返回错误，而是更新现有草稿的内容。

## 优化前的行为

当用户尝试为已有草稿的笔记创建新草稿时：

```json
{
    "code": 102,
    "message": "该笔记已存在草稿",
    "data": null
}
```

这种行为对用户不够友好，用户需要先删除现有草稿才能创建新的草稿。

## 优化后的行为

当用户尝试为已有草稿的笔记创建新草稿时：
- 系统会自动更新现有草稿的内容
- 返回成功响应，包含草稿ID（可能是现有草稿的ID）
- 用户体验更加流畅，无需额外操作

```json
{
    "code": 0,
    "message": "笔记草稿创建成功",
    "data": {
        "id": "123"
    }
}
```

## 技术实现

### 1. 新增服务方法

在 `MySqlNoteDraftService` 中新增了 `create_or_update_draft` 方法：

```rust
pub async fn create_or_update_draft(
    &self,
    note_id: u64,
    user_id: u64,
    content: Option<String>,
) -> Result<MySqlNoteDraft, MySqlNoteDraftServiceError>
```

### 2. 逻辑流程

1. **检查草稿是否存在**：首先检查指定笔记是否已有草稿
2. **更新现有草稿**：如果草稿存在，直接更新其内容
3. **检查用户限制**：如果草稿不存在，检查用户是否已有其他草稿
4. **创建新草稿**：如果通过所有检查，创建新的草稿

### 3. 处理器更新

更新了 `src/handlers/note_draft/create.rs`：
- 使用新的 `create_or_update_draft` 方法替代原来的 `create_draft`
- 移除了 `DraftAlreadyExists` 错误的处理（因为不再会出现此错误）
- 保持了其他错误处理逻辑不变

## 兼容性

- **向后兼容**：API接口和响应格式保持不变
- **用户体验提升**：用户无需关心草稿是否已存在
- **业务逻辑保持**：用户草稿唯一性限制依然有效

## 测试覆盖

新增了 `test_create_or_update_draft` 测试用例，验证：
- 第一次调用创建新草稿
- 第二次调用更新现有草稿
- 内容正确更新

## 使用场景

这个优化特别适用于以下场景：
1. **自动保存**：应用可以定期调用此接口保存草稿，无需担心重复创建
2. **用户重新编辑**：用户可以随时开始编辑笔记，系统会智能处理草稿状态
3. **多设备同步**：不同设备上的编辑操作不会因为草稿冲突而失败

## 注意事项

- 用户草稿唯一性限制依然有效：每个用户同时只能有一个草稿
- 如果用户已有其他笔记的草稿，仍然会返回错误
- 草稿内容会完全替换，不是增量更新

## 错误处理

优化后仍可能出现的错误：
- `UserHasOtherDraft`：用户已有其他草稿
- `DatabaseError`：数据库操作失败
- `ServiceUnavailable`：服务不可用
- `InvalidParameter`：参数无效
- `DraftNotFound`：更新时草稿不存在（理论上不应该发生）

## 总结

这个优化提升了用户体验，使草稿创建操作更加智能和用户友好，同时保持了系统的业务逻辑完整性。
