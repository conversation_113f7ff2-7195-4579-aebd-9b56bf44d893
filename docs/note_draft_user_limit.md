# 笔记草稿用户限制功能

## 功能概述

实现了笔记草稿的用户限制功能，确保每个用户同时只能存在一个草稿。这个功能防止用户创建过多的草稿，提高用户体验和数据管理效率。

## 核心特性

### 1. 用户草稿唯一性
- 每个用户在任何时候只能拥有一个草稿
- 当用户尝试创建第二个草稿时，系统会返回错误提示
- 用户必须先完成或删除现有草稿才能创建新草稿

### 2. 数据库约束
- 在 `note_drafts` 表中添加了 `user_id` 字段
- 设置了 `user_id` 的唯一约束，确保数据库层面的限制
- 保持了 `note_id` 的唯一约束，确保每个笔记只能有一个草稿

### 3. 错误处理
- 新增了 `UserHasOtherDraft` 错误类型
- 提供了清晰的中文错误提示："您已有其他草稿，请先完成或删除现有草稿"

## 数据库变更

### 表结构更新
```sql
-- 原始表结构
CREATE TABLE note_drafts (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    note_id BIGINT UNSIGNED NOT NULL,
    content LONGTEXT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY idx_note_id (note_id),
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE
);

-- 更新后的表结构
CREATE TABLE note_drafts (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    note_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NOT NULL,  -- 新增字段
    content LONGTEXT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY idx_note_id (note_id),
    UNIQUE KEY idx_user_id (user_id),  -- 新增唯一约束
    KEY idx_user_note (user_id, note_id),  -- 新增复合索引
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE  -- 新增外键
);
```

### 迁移脚本
- `migrations/add_user_id_to_note_drafts.sql` - 为现有表添加用户ID字段和约束

## 代码变更

### 1. 模型更新
- `src/models/mysql/note_draft.rs` - 添加 `user_id` 字段

### 2. 服务层更新
- `src/services/mysql/note_draft_service.rs`
  - 新增 `UserHasOtherDraft` 错误类型
  - 更新 `create_draft` 方法，添加用户草稿检查
  - 新增 `user_has_draft` 私有方法
  - 新增 `get_draft_by_user_id` 公共方法

### 3. 处理器更新
- `src/handlers/note_draft/create.rs` - 更新错误处理，添加新错误类型的处理

### 4. 数据库架构更新
- `src/db/schema_manager.rs` - 更新表创建SQL
- `migrations/add_note_drafts_table.sql` - 更新初始表结构

## API 行为变更

### 创建草稿接口 (`POST /note/draft/create`)

#### 新的错误响应
当用户已有其他草稿时：
```json
{
  "code": 403,
  "message": "您已有其他草稿，请先完成或删除现有草稿",
  "data": null
}
```

#### 业务逻辑流程
1. 验证用户身份和权限
2. 检查用户是否已有其他草稿 ← **新增检查**
3. 检查该笔记是否已存在草稿
4. 创建新草稿

## 测试

### 单元测试
- `src/tests/note_draft_user_limit_test.rs` - 用户草稿限制功能测试

### 测试场景
1. 用户创建第一个草稿应该成功
2. 用户尝试创建第二个草稿应该失败
3. 删除草稿后应该可以创建新草稿
4. 不同用户可以同时拥有草稿

## 部署注意事项

### 数据库迁移
1. 执行 `migrations/add_user_id_to_note_drafts.sql` 脚本
2. 确保现有草稿数据的 `user_id` 字段正确填充
3. 验证唯一约束是否正确应用

### 兼容性
- 该更新向后兼容，不会影响现有的草稿功能
- 现有草稿会通过迁移脚本自动关联到正确的用户

## 总结

通过添加用户维度的草稿限制，我们实现了：
1. ✅ 确保每个用户同时只能有一个草稿
2. ✅ 数据库层面的约束保证
3. ✅ 清晰的错误提示和处理
4. ✅ 完整的测试覆盖
5. ✅ 向后兼容的数据迁移

这个功能提高了用户体验，避免了草稿混乱，同时保持了系统的数据一致性。
