# 系统提示词使用示例

## 概述

我们在测试环境中创建了一个简单的系统提示词，内容为：`你好， {{content}}`

这个提示词演示了如何使用模板变量来创建动态的系统提示词。

## 系统提示词信息

- **ID**: 1
- **标题**: 测试问候提示词
- **内容**: `你好， {{content}}`
- **分类**: 测试
- **标签**: 测试,问候,模板
- **环境**: testing (aishoucang_develop 数据库)

## API 接口

### 1. 获取系统提示词列表

```bash
GET /system-prompt/list
```

### 2. 获取特定系统提示词详情

```bash
GET /system-prompt/detail?id=1
```

## 使用示例

### 1. 基本使用

当你获取到这个系统提示词后，可以将 `{{content}}` 替换为实际内容：

```
原始模板: 你好， {{content}}
替换后:   你好， 欢迎使用我们的AI助手！
```

### 2. 在代码中使用

```rust
// 假设从数据库获取到系统提示词
let system_prompt_template = "你好， {{content}}";
let actual_content = "欢迎使用我们的AI助手！";

// 替换模板变量
let final_prompt = system_prompt_template.replace("{{content}}", actual_content);
// 结果: "你好， 欢迎使用我们的AI助手！"
```

### 3. 更复杂的使用场景

这个模板可以用于各种场景：

```
场景1 - 客服问候:
你好， 我是您的专属客服，有什么可以帮助您的吗？

场景2 - 学习助手:
你好， 今天我们来学习关于Rust编程的知识。

场景3 - 个性化问候:
你好， 张三，欢迎回到我们的平台！
```

## 测试脚本

我们提供了以下测试脚本：

1. **创建系统提示词**: `./scripts/create_test_system_prompt.sh`
2. **测试API接口**: `./scripts/test_system_prompt_api.sh`

### 运行测试

```bash
# 创建系统提示词（如果还没有创建）
./scripts/create_test_system_prompt.sh testing

# 测试API接口（需要先启动服务器）
./scripts/test_system_prompt_api.sh http://localhost:8080
```

## 扩展建议

1. **多语言支持**: 可以创建不同语言版本的问候提示词
2. **时间相关**: 根据时间创建不同的问候语（早上好、下午好等）
3. **用户个性化**: 根据用户偏好定制问候内容
4. **情境感知**: 根据用户当前的操作情境提供相应的提示

## 注意事项

- 模板变量使用 `{{变量名}}` 格式
- 确保在使用前正确替换所有模板变量
- 系统提示词支持HTML内容，可以包含格式化信息
- 在生产环境使用前，请充分测试模板替换逻辑
