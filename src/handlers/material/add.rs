use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};
use serde_json::Value;

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode};
use crate::dto::material::{AddMaterialRequest, AddMaterialResponse, MaterialResponse};
use crate::middleware::token_parser::get_user_id_from_request;
use crate::services::mysql::{MySqlMaterialService, MySqlMaterialServiceError};

/**
 * 添加素材接口处理函数
 *
 * 此函数处理POST /material/add请求，用于添加一个新素材
 *
 * @param req HTTP请求
 * @param payload 请求体，包含素材信息
 * @param app_state 应用状态
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    payload: web::Json<AddMaterialRequest>,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("无效的用户ID: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "无效的用户ID".to_string(),
                data: Value::Null,
            });
        }
    };

    // 验证素材类型
    let material_type = match MySqlMaterialService::validate_material_type(payload.r#type) {
        Ok(mat_type) => mat_type,
        Err(_) => {
            error!("无效的素材类型: {}", payload.r#type);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "无效的素材类型，支持的类型：1图片 2语音 3视频".to_string(),
                data: Value::Null,
            });
        }
    };

    // 获取MySQL连接池
    let mysql_pool = match &app_state.db {
        Some(db) => match &db.mysql {
            Some(mysql) => mysql.pool(),
            None => {
                error!("MySQL连接不可用");
                return HttpResponse::InternalServerError().json(ApiResponse {
                    code: ErrorCode::ServiceUnavailable.code(),
                    message: "服务错误，请稍后再试".to_string(),
                    data: Value::Null,
                });
            }
        },
        None => {
            error!("数据库连接不可用");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: Value::Null,
            });
        }
    };

    // 创建素材服务
    let mysql_material_service = MySqlMaterialService::new(mysql_pool.clone());

    // 调用服务添加素材
    match mysql_material_service.add_material(user_id, payload.url.clone(), material_type).await {
        Ok(material) => {
            info!("用户 {} 添加素材成功: {}", user_id, material.id);

            let material_response = MaterialResponse::from_mysql_model(&material);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "添加素材成功".to_string(),
                data: AddMaterialResponse {
                    material: material_response,
                },
            })
        },
        Err(e) => {
            error!("添加素材失败: {}", e);

            match e {
                MySqlMaterialServiceError::EmptyUrl => {
                    HttpResponse::BadRequest().json(ApiResponse {
                        code: ErrorCode::InvalidParameter.code(),
                        message: "URL不能为空".to_string(),
                        data: Value::Null,
                    })
                },
                MySqlMaterialServiceError::InvalidMaterialType => {
                    HttpResponse::BadRequest().json(ApiResponse {
                        code: ErrorCode::InvalidParameter.code(),
                        message: "无效的素材类型".to_string(),
                        data: Value::Null,
                    })
                },
                _ => {
                    HttpResponse::InternalServerError().json(ApiResponse {
                        code: ErrorCode::ServiceUnavailable.code(),
                        message: "服务错误，请稍后再试".to_string(),
                        data: Value::Null,
                    })
                }
            }
        }
    }
}
