use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};
use serde_json::Value;

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode};
use crate::dto::material::{MaterialListParams, MaterialListResponse, MaterialResponse};
use crate::middleware::token_parser::get_user_id_from_request;
use crate::services::mysql::{MySqlMaterialService, MySqlMaterialServiceError};

/**
 * 获取素材列表接口处理函数
 *
 * 此函数处理GET /material/list请求，用于获取用户的素材列表
 *
 * @param req HTTP请求
 * @param params 查询参数
 * @param app_state 应用状态
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    params: web::Query<MaterialListParams>,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("无效的用户ID: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "无效的用户ID".to_string(),
                data: Value::Null,
            });
        }
    };

    // 验证分页参数
    if params.page < 1 {
        return HttpResponse::BadRequest().json(ApiResponse {
            code: ErrorCode::InvalidParameter.code(),
            message: "页码必须大于0".to_string(),
            data: Value::Null,
        });
    }

    if params.page_size < 1 || params.page_size > 100 {
        return HttpResponse::BadRequest().json(ApiResponse {
            code: ErrorCode::InvalidParameter.code(),
            message: "每页数量必须在1-100之间".to_string(),
            data: Value::Null,
        });
    }

    // 验证素材类型（如果提供）
    let material_type = if let Some(type_value) = params.r#type {
        match MySqlMaterialService::validate_material_type(type_value) {
            Ok(mat_type) => Some(mat_type),
            Err(_) => {
                error!("无效的素材类型: {}", type_value);
                return HttpResponse::BadRequest().json(ApiResponse {
                    code: ErrorCode::InvalidParameter.code(),
                    message: "无效的素材类型，支持的类型：1图片 2语音 3视频".to_string(),
                    data: Value::Null,
                });
            }
        }
    } else {
        None
    };

    // 获取MySQL连接池
    let mysql_pool = match &app_state.db {
        Some(db) => match &db.mysql {
            Some(mysql) => mysql.pool(),
            None => {
                error!("MySQL连接不可用");
                return HttpResponse::InternalServerError().json(ApiResponse {
                    code: ErrorCode::ServiceUnavailable.code(),
                    message: "服务错误，请稍后再试".to_string(),
                    data: Value::Null,
                });
            }
        },
        None => {
            error!("数据库连接不可用");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: Value::Null,
            });
        }
    };

    // 创建素材服务
    let mysql_material_service = MySqlMaterialService::new(mysql_pool.clone());

    // 调用服务获取素材列表
    match mysql_material_service.get_user_materials(user_id, params.page, params.page_size, material_type).await {
        Ok((materials, total)) => {
            info!("用户 {} 获取素材列表成功，共 {} 条", user_id, total);

            let material_responses: Vec<MaterialResponse> = materials
                .iter()
                .map(|material| MaterialResponse::from_mysql_model(material))
                .collect();

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "获取素材列表成功".to_string(),
                data: MaterialListResponse {
                    materials: material_responses,
                    total,
                    page: params.page,
                    page_size: params.page_size,
                },
            })
        },
        Err(e) => {
            error!("获取素材列表失败: {}", e);

            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: Value::Null,
            })
        }
    }
}
