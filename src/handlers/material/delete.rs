use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};
use serde_json::Value;

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode};
use crate::dto::material::{DeleteMaterialRequest, DeleteMaterialResponse};
use crate::middleware::token_parser::get_user_id_from_request;
use crate::services::mysql::{MySqlMaterialService, MySqlMaterialServiceError};

/**
 * 删除素材接口处理函数
 *
 * 此函数处理POST /material/delete请求，用于软删除一个素材
 *
 * @param req HTTP请求
 * @param payload 请求体，包含素材ID
 * @param app_state 应用状态
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    payload: web::Json<DeleteMaterialRequest>,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("无效的用户ID: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "无效的用户ID".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析素材ID
    let material_id = match payload.id.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("无效的素材ID: {}", payload.id);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "无效的素材ID".to_string(),
                data: Value::Null,
            });
        }
    };

    // 获取MySQL连接池
    let mysql_pool = match &app_state.db {
        Some(db) => match &db.mysql {
            Some(mysql) => mysql.pool(),
            None => {
                error!("MySQL连接不可用");
                return HttpResponse::InternalServerError().json(ApiResponse {
                    code: ErrorCode::ServiceUnavailable.code(),
                    message: "服务错误，请稍后再试".to_string(),
                    data: Value::Null,
                });
            }
        },
        None => {
            error!("数据库连接不可用");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: Value::Null,
            });
        }
    };

    // 创建素材服务
    let mysql_material_service = MySqlMaterialService::new(mysql_pool.clone());

    // 调用服务删除素材
    match mysql_material_service.delete_material(material_id, user_id).await {
        Ok(()) => {
            info!("用户 {} 删除素材成功: {}", user_id, material_id);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "删除素材成功".to_string(),
                data: DeleteMaterialResponse {
                    id: payload.id.clone(),
                },
            })
        },
        Err(e) => {
            error!("删除素材失败: {}", e);

            match e {
                MySqlMaterialServiceError::MaterialNotFound => {
                    HttpResponse::NotFound().json(ApiResponse {
                        code: ErrorCode::SystemError.code(),
                        message: "素材不存在或无权限删除".to_string(),
                        data: Value::Null,
                    })
                },
                _ => {
                    HttpResponse::InternalServerError().json(ApiResponse {
                        code: ErrorCode::ServiceUnavailable.code(),
                        message: "服务错误，请稍后再试".to_string(),
                        data: Value::Null,
                    })
                }
            }
        }
    }
}
