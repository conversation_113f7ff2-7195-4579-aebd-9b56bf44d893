use actix_web::{web, HttpResponse, Responder};
use log::{error, info};
use serde_json::Value;
use futures_util::StreamExt;

use crate::AppState;

/// LLM聊天处理器 - SSE流式转发
pub async fn handle(
    data: web::Data<AppState>,
    req: web::Json<Value>,
) -> impl Responder {
    info!("收到LLM聊天请求");

    let llm_config = &data.config.llm;

    // 检查DeepSeek是否可用
    let deepseek_config = match &llm_config.deepseek {
        Some(config) if config.enabled => config.clone(),
        Some(_) => {
            error!("DeepSeek提供商未启用");
            return HttpResponse::ServiceUnavailable().json(serde_json::json!({
                "error": {
                    "message": "DeepSeek服务未启用",
                    "type": "service_unavailable",
                    "code": "deepseek_disabled"
                }
            }));
        }
        None => {
            error!("DeepSeek配置不存在");
            return HttpResponse::ServiceUnavailable().json(serde_json::json!({
                "error": {
                    "message": "DeepSeek服务未配置",
                    "type": "service_unavailable",
                    "code": "deepseek_not_configured"
                }
            }));
        }
    };

    // 克隆请求数据
    let mut request_data = req.into_inner();

    // 强制设置为流式返回
    request_data["stream"] = Value::Bool(true);

    // 创建SSE流式响应
    match create_sse_stream(deepseek_config, request_data).await {
        Ok(stream) => {
            info!("开始SSE流式转发");
            HttpResponse::Ok()
                .content_type("text/event-stream")
                .append_header(("Cache-Control", "no-cache"))
                .append_header(("Connection", "keep-alive"))
                .append_header(("Access-Control-Allow-Origin", "*"))
                .append_header(("Access-Control-Allow-Headers", "Cache-Control"))
                .streaming(stream)
        }
        Err(e) => {
            error!("创建SSE流失败: {}", e);
            HttpResponse::InternalServerError().json(serde_json::json!({
                "error": {
                    "message": "LLM服务调用失败",
                    "type": "api_error",
                    "code": "deepseek_api_error"
                }
            }))
        }
    }
}

/// 创建SSE流式响应
async fn create_sse_stream(
    config: crate::config::LlmProviderConfig,
    request_body: Value,
) -> Result<impl futures_util::Stream<Item = Result<actix_web::web::Bytes, actix_web::Error>>, String> {
    let default_base_url = "https://api.deepseek.com".to_string();
    let base_url = config.base_url.as_ref().unwrap_or(&default_base_url);

    info!("调用DeepSeek API流式接口，基础URL: {}", base_url);

    // 创建HTTP客户端
    let client = reqwest::Client::new();

    // 发送请求
    let response = client
        .post(&format!("{}/v1/chat/completions", base_url))
        .header("Authorization", format!("Bearer {}", config.api_key))
        .header("Content-Type", "application/json")
        .json(&request_body)
        .send()
        .await
        .map_err(|e| format!("HTTP请求失败: {}", e))?;

    // 检查响应状态
    if !response.status().is_success() {
        let status = response.status();
        let error_text = response.text().await
            .unwrap_or_else(|_| "无法读取错误响应".to_string());
        return Err(format!("API请求失败，状态码: {}, 错误: {}", status, error_text));
    }

    // 直接转发响应流
    let stream = response.bytes_stream().map(|result| {
        match result {
            Ok(chunk) => Ok(actix_web::web::Bytes::from(chunk)),
            Err(e) => {
                error!("流式响应读取错误: {}", e);
                let error_data = format!("data: {{\"error\": \"流式响应读取错误: {}\"}}\n\n", e);
                Ok(actix_web::web::Bytes::from(error_data))
            }
        }
    });

    Ok(stream)
}
