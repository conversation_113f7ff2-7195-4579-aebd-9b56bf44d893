use actix_web::{web, HttpResponse, Responder};
use log::info;
use serde::{Deserialize, Serialize};

use crate::AppState;

/// 腾讯云配置测试响应
#[derive(Debug, Serialize, Deserialize)]
pub struct TencentCloudConfigTestResponse {
    /// 腾讯云应用ID
    pub app_id: String,
    /// 腾讯云应用密钥（脱敏显示）
    pub app_secret_masked: String,
    /// 配置状态
    pub status: String,
}

/// 测试腾讯云配置
pub async fn handle(data: web::Data<AppState>) -> impl Responder {
    info!("开始测试腾讯云配置");

    let tencent_config = &data.config.tencent_cloud;
    
    // 脱敏处理密钥，只显示前4位和后4位
    let secret_key_masked = if tencent_config.secret_key.len() > 8 {
        let len = tencent_config.secret_key.len();
        format!("{}****{}",
            &tencent_config.secret_key[..4],
            &tencent_config.secret_key[len-4..])
    } else {
        "****".to_string()
    };

    let response = TencentCloudConfigTestResponse {
        app_id: tencent_config.secret_id.clone(),
        app_secret_masked: secret_key_masked,
        status: "配置加载成功".to_string(),
    };

    info!("腾讯云配置测试完成，APP_ID: {}", response.app_id);

    HttpResponse::Ok().json(response)
}
