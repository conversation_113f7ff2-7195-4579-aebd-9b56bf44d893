use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{error, info};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::time::Instant;

use crate::{
    AppState,
    dto::{ApiResponse, error_code::ErrorCode},
    utils::{MnsMessageAttributes, MnsError},
};

/// MNS测试请求
#[derive(Debug, Deserialize)]
pub struct MnsTestRequest {
    /// 消息内容
    pub message: String,

    /// 延迟时间（秒）
    #[serde(default)]
    pub delay_seconds: Option<i32>,

    /// 优先级 (1-16)
    #[serde(default)]
    pub priority: Option<i32>,
}

/// MNS测试响应
#[derive(Debug, Serialize)]
pub struct MnsTestResponse {
    /// 消息ID
    pub message_id: String,

    /// 消息体MD5
    pub message_body_md5: String,
}

/// 测试MNS消息发送
pub async fn handle(
    _req: HttpRequest,
    app_state: web::Data<AppState>,
    mns_request: web::Json<MnsTestRequest>,
) -> impl Responder {
    // 记录处理函数开始时间
    let handler_start_time = Instant::now();
    info!("开始处理MNS测试请求");

    // 测试接口，暂时不验证用户登录
    // 记录请求信息
    info!("收到MNS测试请求: {:?}", mns_request);

    // 获取MNS客户端
    let mns_client = match &app_state.mns_client {
        Some(client) => client,
        None => {
            error!("MNS客户端未初始化");
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: error_code.code(),
                message: "MNS服务不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 构造消息属性
    let attributes = MnsMessageAttributes {
        message_id: None,
        message_body_md5: None,
        message_body_type: None,
        priority: mns_request.priority,
        delay_seconds: mns_request.delay_seconds,
    };

    // 发送消息
    match mns_client.send_message(&mns_request.message, Some(attributes)).await {
        Ok(response) => {
            let total_duration = handler_start_time.elapsed();
            info!("MNS测试请求处理完成，总耗时: {:?}", total_duration);

            // 构造响应
            let test_response = MnsTestResponse {
                message_id: response.message_id,
                message_body_md5: response.message_body_md5,
            };

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "MNS消息发送成功".to_string(),
                data: test_response,
            })
        },
        Err(e) => {
            let total_duration = handler_start_time.elapsed();
            let error_message = match &e {
                MnsError::MnsServiceError { code, message, request_id } => {
                    format!("MNS服务错误: 代码={}, 消息={}, 请求ID={}", code, message, request_id)
                },
                _ => format!("发送MNS消息失败: {}", e),
            };

            error!("{}, 耗时: {:?}", error_message, total_duration);

            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::SystemError.code(),
                message: error_message,
                data: Value::Null,
            })
        }
    }
}
