use actix_web::{web, HttpResponse, Responder};
use log::{error, info};
use serde::Serialize;

use crate::{
    AppState,
    dto::{ApiResponse, error_code::ErrorCode},
};

/// MySQL服务测试响应
#[derive(Debug, Serialize)]
pub struct MySqlServicesTestResponse {
    /// 用户服务状态
    pub user_service: bool,
    /// 收藏夹服务状态
    pub favorite_service: bool,
    /// 书签服务状态
    pub bookmark_service: bool,
}

/// 测试MySQL服务
pub async fn handle(
    app_state: web::Data<AppState>,
) -> impl Responder {
    info!("测试MySQL服务");

    // 检查MySQL服务是否可用
    let user_service_available = app_state.mysql_user_service.is_some();
    let favorite_service_available = app_state.mysql_favorite_service.is_some();
    let bookmark_service_available = app_state.mysql_bookmark_service.is_some();

    if user_service_available || favorite_service_available || bookmark_service_available {
        // 返回成功响应
        let response = MySqlServicesTestResponse {
            user_service: user_service_available,
            favorite_service: favorite_service_available,
            bookmark_service: bookmark_service_available,
        };

        HttpResponse::Ok().json(ApiResponse::success(response, "MySQL服务测试成功"))
    } else {
        error!("MySQL服务不可用");
        HttpResponse::ServiceUnavailable().json(ApiResponse::error(
            ErrorCode::ServiceUnavailable.code(),
            (),
            "MySQL服务不可用",
        ))
    }
}
