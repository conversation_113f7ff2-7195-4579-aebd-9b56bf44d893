use actix_web::{web, HttpResponse, Responder};
use log::{error, info};
use serde::Serialize;
use sqlx::Row;

use crate::{
    AppState,
    dto::{ApiResponse, error_code::ErrorCode},
    db::utils::mysql_helper,
};

/// MySQL测试响应
#[derive(Debug, Serialize)]
pub struct MySqlTestResponse {
    /// 测试结果
    pub result: String,
    /// 数据库名称
    pub database: String,
    /// 是否连接成功
    pub connected: bool,
}

/// 测试MySQL连接
pub async fn handle(
    app_state: web::Data<AppState>,
) -> impl Responder {
    info!("测试MySQL连接");

    // 获取应用状态
    let config = &app_state.config;

    // 检查数据库连接是否可用
    if let Some(db) = &app_state.db {
        // 检查MySQL是否可用
        if mysql_helper::is_mysql_available(db) {
            // 获取MySQL连接池
            let pool = mysql_helper::get_mysql_pool(db).unwrap();

            // 获取数据库名称
            let database = mysql_helper::get_mysql_database_name(config);

            // 执行简单查询
            match sqlx::query("SELECT 1 as result")
                .fetch_one(pool)
                .await {
                Ok(row) => {
                    let result: i32 = row.get("result");
                    info!("MySQL连接测试成功: {}", result);

                    // 返回成功响应
                    let response = MySqlTestResponse {
                        result: format!("查询结果: {}", result),
                        database: database.to_string(),
                        connected: true,
                    };

                    HttpResponse::Ok().json(ApiResponse::success(response, "MySQL连接测试成功"))
                },
                Err(e) => {
                    error!("MySQL查询失败: {}", e);
                    HttpResponse::InternalServerError().json(ApiResponse::error(
                        ErrorCode::DatabaseError.code(),
                        &format!("MySQL查询失败: {}", e),
                        ErrorCode::DatabaseError.message(),
                    ))
                }
            }
        } else {
            error!("MySQL连接不可用");
            HttpResponse::ServiceUnavailable().json(ApiResponse::error(
                ErrorCode::ServiceUnavailable.code(),
                "MySQL连接不可用",
                ErrorCode::ServiceUnavailable.message(),
            ))
        }
    } else {
        error!("数据库连接不可用");
        HttpResponse::ServiceUnavailable().json(ApiResponse::error(
            ErrorCode::ServiceUnavailable.code(),
            "数据库连接不可用",
            ErrorCode::ServiceUnavailable.message(),
        ))
    }
}
