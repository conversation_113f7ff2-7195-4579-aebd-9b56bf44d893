use actix_web::{web, HttpResponse, Responder};
use log::info;
use serde::Serialize;

use crate::AppState;

/// LLM配置测试响应
#[derive(Serialize)]
pub struct LlmConfigTestResponse {
    /// 是否有启用的LLM提供商
    pub has_enabled_provider: bool,
    /// 启用的提供商列表
    pub enabled_providers: Vec<String>,
    /// DeepSeek配置状态
    pub deepseek_status: Option<LlmProviderStatus>,
    /// OpenAI配置状态
    pub openai_status: Option<LlmProviderStatus>,
    /// 其他提供商状态
    pub other_providers: Vec<String>,
}

/// LLM提供商状态
#[derive(Serialize)]
pub struct LlmProviderStatus {
    /// 是否启用
    pub enabled: bool,
    /// 是否有API密钥
    pub has_api_key: bool,
    /// 基础URL
    pub base_url: Option<String>,
    /// 默认模型
    pub default_model: Option<String>,
}

/// 测试LLM配置
pub async fn handle(data: web::Data<AppState>) -> impl Responder {
    info!("测试LLM配置");

    let llm_config = &data.config.llm;
    
    // 检查DeepSeek配置
    let deepseek_status = llm_config.deepseek.as_ref().map(|config| {
        LlmProviderStatus {
            enabled: config.enabled,
            has_api_key: !config.api_key.is_empty(),
            base_url: config.base_url.clone(),
            default_model: config.default_model.clone(),
        }
    });

    // 检查OpenAI配置
    let openai_status = llm_config.openai.as_ref().map(|config| {
        LlmProviderStatus {
            enabled: config.enabled,
            has_api_key: !config.api_key.is_empty(),
            base_url: config.base_url.clone(),
            default_model: config.default_model.clone(),
        }
    });

    // 获取其他提供商
    let mut other_providers = Vec::new();
    if llm_config.qwen.is_some() {
        other_providers.push("qwen".to_string());
    }
    if llm_config.ernie.is_some() {
        other_providers.push("ernie".to_string());
    }
    if llm_config.hunyuan.is_some() {
        other_providers.push("hunyuan".to_string());
    }

    let response = LlmConfigTestResponse {
        has_enabled_provider: llm_config.has_enabled_provider(),
        enabled_providers: llm_config.get_enabled_providers().into_iter().map(|s| s.to_string()).collect(),
        deepseek_status,
        openai_status,
        other_providers,
    };

    info!("LLM配置测试完成，启用的提供商: {:?}", response.enabled_providers);

    HttpResponse::Ok().json(response)
}
