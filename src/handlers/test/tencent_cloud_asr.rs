use actix_web::{web, HttpResponse, Responder};
use log::{error, info};
use serde::Serialize;

use crate::{
    AppState,
    dto::{ApiResponse, error_code::ErrorCode},
};

/// 腾讯云语音识别服务测试响应
#[derive(Debug, Serialize)]
pub struct TencentAsrTestResponse {
    /// 服务状态
    pub service_available: bool,
    /// 配置状态
    pub config_status: String,
    /// 腾讯云应用ID（脱敏）
    pub app_id_masked: String,
    /// 腾讯云应用密钥（脱敏）
    pub app_secret_masked: String,
}

/// 测试腾讯云语音识别服务
pub async fn handle(data: web::Data<AppState>) -> impl Responder {
    info!("开始测试腾讯云语音识别服务");

    // 检查服务是否可用
    let service_available = data.tencent_asr_service.is_some();
    
    if !service_available {
        error!("腾讯云语音识别服务未初始化");
        return HttpResponse::ServiceUnavailable().json(ApiResponse::error(
            ErrorCode::ServiceUnavailable.code(),
            (),
            "腾讯云语音识别服务不可用",
        ));
    }

    let tencent_config = &data.config.tencent_cloud;
    
    // 脱敏处理SecretId和SecretKey
    let secret_id_masked = if tencent_config.secret_id.len() > 6 {
        let len = tencent_config.secret_id.len();
        format!("{}****{}",
            &tencent_config.secret_id[..3],
            &tencent_config.secret_id[len-3..])
    } else {
        "****".to_string()
    };

    let secret_key_masked = if tencent_config.secret_key.len() > 8 {
        let len = tencent_config.secret_key.len();
        format!("{}****{}",
            &tencent_config.secret_key[..4],
            &tencent_config.secret_key[len-4..])
    } else {
        "****".to_string()
    };

    let response = TencentAsrTestResponse {
        service_available,
        config_status: "配置加载成功".to_string(),
        app_id_masked: secret_id_masked,
        app_secret_masked: secret_key_masked,
    };

    info!("腾讯云语音识别服务测试完成，服务可用: {}", service_available);

    HttpResponse::Ok().json(ApiResponse::success(response, "腾讯云语音识别服务测试成功"))
}
