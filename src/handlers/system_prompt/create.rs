use actix_web::{web, HttpResponse, Responder};
use log::{info, error};

use crate::dto::{ApiResponse, ErrorCode};
use crate::dto::system_prompt::{CreateSystemPromptRequest, CreateSystemPromptResponse, SystemPromptResponse};
use crate::services::mysql::{MySqlSystemPromptService, MySqlSystemPromptServiceError};
use crate::AppState;

/**
 * 创建系统提示词处理函数
 *
 * 此函数处理POST /system_prompt/create请求，用于创建新的系统提示词
 * 业务规则：
 * 1. 系统提示词标题不能为空且不超过255字符
 * 2. 系统提示词内容不能为空
 * 3. 系统提示词标题不能重复
 *
 * @param app_state 应用状态
 * @param system_prompt_data 创建系统提示词请求数据
 * @return HTTP响应
 */
pub async fn handle(
    app_state: web::Data<AppState>,
    system_prompt_data: web::Json<CreateSystemPromptRequest>,
) -> impl Responder {
    info!("收到创建系统提示词请求: {:?}", system_prompt_data.title);

    // 获取数据库连接
    let db_connections = match &app_state.db {
        Some(db) => db,
        None => {
            error!("数据库连接不可用");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "数据库连接不可用".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取MySQL连接池
    let mysql_pool = match &db_connections.mysql {
        Some(mysql_client) => mysql_client.pool().clone(),
        None => {
            error!("MySQL连接不可用");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "MySQL连接不可用".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 创建MySQL系统提示词服务
    let mysql_system_prompt_service = match MySqlSystemPromptService::new(mysql_pool, &app_state.config) {
        Ok(service) => service,
        Err(e) => {
            error!("创建MySQL系统提示词服务失败: {}", e);
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务不可用，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 创建系统提示词
    match mysql_system_prompt_service.create_system_prompt(
        system_prompt_data.title.clone(),
        system_prompt_data.content.clone(),
        system_prompt_data.category.clone(),
        system_prompt_data.tags.clone(),
        system_prompt_data.is_enabled,
        system_prompt_data.sort_weight,
    ).await {
        Ok(system_prompt) => {
            // 转换为响应格式
            let system_prompt_response = SystemPromptResponse::from_mysql_model(&system_prompt);

            info!("创建系统提示词成功: {:?}", system_prompt.title);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "创建系统提示词成功".to_string(),
                data: CreateSystemPromptResponse {
                    system_prompt: system_prompt_response,
                },
            })
        }
        Err(MySqlSystemPromptServiceError::InvalidSystemPromptTitle) => {
            error!("系统提示词标题无效: {:?}", system_prompt_data.title);
            HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidInput.code(),
                message: "系统提示词标题不能为空且不能超过255字符".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(MySqlSystemPromptServiceError::InvalidSystemPromptContent) => {
            error!("系统提示词内容无效");
            HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidInput.code(),
                message: "系统提示词内容不能为空".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(MySqlSystemPromptServiceError::SystemPromptAlreadyExists) => {
            error!("系统提示词已存在: {:?}", system_prompt_data.title);
            HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::DataAlreadyExists.code(),
                message: "系统提示词标题已存在".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(MySqlSystemPromptServiceError::DatabaseError(e)) => {
            error!("数据库创建错误: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(e) => {
            error!("创建系统提示词失败: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}
