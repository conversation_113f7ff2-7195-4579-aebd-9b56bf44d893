use actix_web::{web, HttpResponse, Responder};
use log::{info, error};

use crate::dto::{ApiResponse, ErrorCode};
use crate::dto::system_prompt::{DeleteSystemPromptRequest, DeleteSystemPromptResponse};
use crate::services::mysql::{MySqlSystemPromptService, MySqlSystemPromptServiceError};
use crate::AppState;

/**
 * 删除系统提示词处理函数
 *
 * 此函数处理POST /system_prompt/delete请求，用于删除指定的系统提示词
 *
 * @param app_state 应用状态
 * @param system_prompt_data 删除系统提示词请求数据
 * @return HTTP响应
 */
pub async fn handle(
    app_state: web::Data<AppState>,
    system_prompt_data: web::Json<DeleteSystemPromptRequest>,
) -> impl Responder {
    // 解析系统提示词ID
    let system_prompt_id = match system_prompt_data.id.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("系统提示词ID格式无效: {}", system_prompt_data.id);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidInput.code(),
                message: "系统提示词ID格式无效".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    info!("删除系统提示词: {}", system_prompt_id);

    // 获取数据库连接
    let db_connections = match &app_state.db {
        Some(db) => db,
        None => {
            error!("数据库连接不可用");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "数据库连接不可用".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取MySQL连接池
    let mysql_pool = match &db_connections.mysql {
        Some(mysql_client) => mysql_client.pool().clone(),
        None => {
            error!("MySQL连接不可用");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "MySQL连接不可用".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 创建MySQL系统提示词服务
    let mysql_system_prompt_service = match MySqlSystemPromptService::new(mysql_pool, &app_state.config) {
        Ok(service) => service,
        Err(e) => {
            error!("创建MySQL系统提示词服务失败: {}", e);
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务不可用，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 首先检查系统提示词是否存在
    match mysql_system_prompt_service.get_system_prompt_by_id(system_prompt_id).await {
        Ok(Some(_)) => {
            // 系统提示词存在，继续删除
        }
        Ok(None) => {
            error!("系统提示词不存在: {}", system_prompt_id);
            return HttpResponse::NotFound().json(ApiResponse {
                code: ErrorCode::DataNotFound.code(),
                message: "系统提示词不存在".to_string(),
                data: serde_json::Value::Null,
            });
        }
        Err(MySqlSystemPromptServiceError::DatabaseError(e)) => {
            error!("数据库查询错误: {}", e);
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            });
        }
        Err(e) => {
            error!("查询系统提示词失败: {}", e);
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            });
        }
    }

    // 删除系统提示词
    match mysql_system_prompt_service.delete_system_prompt(system_prompt_id).await {
        Ok(_) => {
            info!("删除系统提示词成功: {}", system_prompt_id);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "删除系统提示词成功".to_string(),
                data: DeleteSystemPromptResponse {
                    id: system_prompt_data.id.clone(),
                },
            })
        }
        Err(MySqlSystemPromptServiceError::SystemPromptNotFound) => {
            error!("系统提示词不存在: {}", system_prompt_id);
            HttpResponse::NotFound().json(ApiResponse {
                code: ErrorCode::DataNotFound.code(),
                message: "系统提示词不存在".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(MySqlSystemPromptServiceError::DatabaseError(e)) => {
            error!("数据库删除错误: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(e) => {
            error!("删除系统提示词失败: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}
