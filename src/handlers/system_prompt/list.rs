use actix_web::{web, HttpResponse, Responder};
use log::{info, error};

use crate::dto::{ApiResponse, ErrorCode};
use crate::dto::system_prompt::{SystemPromptListParams, SystemPromptListResponse, SystemPromptResponse};
use crate::services::mysql::{MySqlSystemPromptService, MySqlSystemPromptServiceError};
use crate::AppState;

/**
 * 获取系统提示词列表处理函数
 *
 * 此函数处理GET /system_prompt/list请求，用于获取系统提示词列表
 * 支持分页查询、分类过滤和启用状态过滤
 *
 * @param app_state 应用状态
 * @param params 查询参数
 * @return HTTP响应
 */
pub async fn handle(
    app_state: web::Data<AppState>,
    mut params: web::Query<SystemPromptListParams>,
) -> impl Responder {
    // 参数验证和调整
    if params.page == 0 {
        params.page = 1;
    }
    if params.page_size == 0 || params.page_size > 100 {
        params.page_size = 10;
    }

    info!("获取系统提示词列表，页码: {}, 每页大小: {}", params.page, params.page_size);

    // 获取数据库连接
    let db_connections = match &app_state.db {
        Some(db) => db,
        None => {
            error!("数据库连接不可用");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "数据库连接不可用".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取MySQL连接池
    let mysql_pool = match &db_connections.mysql {
        Some(mysql_client) => mysql_client.pool().clone(),
        None => {
            error!("MySQL连接不可用");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "MySQL连接不可用".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 创建MySQL系统提示词服务
    let mysql_system_prompt_service = match MySqlSystemPromptService::new(mysql_pool, &app_state.config) {
        Ok(service) => service,
        Err(e) => {
            error!("创建MySQL系统提示词服务失败: {}", e);
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务不可用，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取系统提示词列表和总数
    let system_prompts_result = mysql_system_prompt_service.get_system_prompts(
        params.page, 
        params.page_size, 
        params.category.clone(), 
        params.is_enabled
    ).await;
    let total_result = mysql_system_prompt_service.get_system_prompt_count(
        params.category.clone(), 
        params.is_enabled
    ).await;

    match (system_prompts_result, total_result) {
        (Ok(system_prompts), Ok(total)) => {
            // 转换为响应格式
            let system_prompt_responses: Vec<SystemPromptResponse> = system_prompts
                .into_iter()
                .map(|system_prompt| SystemPromptResponse::from_mysql_model(&system_prompt))
                .collect();

            let response = SystemPromptListResponse::new(
                system_prompt_responses,
                total,
                params.page,
                params.page_size,
            );

            info!("获取系统提示词列表成功，共 {} 个", total);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "获取系统提示词列表成功".to_string(),
                data: response,
            })
        }
        (Err(e), _) | (_, Err(e)) => {
            error!("获取系统提示词列表失败: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}
