use actix_web::{web, HttpResponse, Responder};
use log::{info, error};

use crate::dto::{ApiResponse, ErrorCode};
use crate::dto::system_prompt::{SystemPromptDetailParams, SystemPromptResponse};
use crate::services::mysql::{MySqlSystemPromptService, MySqlSystemPromptServiceError};
use crate::AppState;

/**
 * 获取系统提示词详情处理函数
 *
 * 此函数处理GET /system_prompt/detail请求，用于获取指定系统提示词的详细信息
 * 获取详情时会自动增加使用次数
 *
 * @param app_state 应用状态
 * @param params 查询参数
 * @return HTTP响应
 */
pub async fn handle(
    app_state: web::Data<AppState>,
    params: web::Query<SystemPromptDetailParams>,
) -> impl Responder {
    // 解析系统提示词ID
    let system_prompt_id = match params.id.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("系统提示词ID格式无效: {}", params.id);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidInput.code(),
                message: "系统提示词ID格式无效".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    info!("获取系统提示词详情: {}", system_prompt_id);

    // 获取数据库连接
    let db_connections = match &app_state.db {
        Some(db) => db,
        None => {
            error!("数据库连接不可用");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "数据库连接不可用".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取MySQL连接池
    let mysql_pool = match &db_connections.mysql {
        Some(mysql_client) => mysql_client.pool().clone(),
        None => {
            error!("MySQL连接不可用");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "MySQL连接不可用".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 创建MySQL系统提示词服务
    let mysql_system_prompt_service = match MySqlSystemPromptService::new(mysql_pool, &app_state.config) {
        Ok(service) => service,
        Err(e) => {
            error!("创建MySQL系统提示词服务失败: {}", e);
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务不可用，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取系统提示词详情
    match mysql_system_prompt_service.get_system_prompt_by_id(system_prompt_id).await {
        Ok(Some(system_prompt)) => {
            // 增加使用次数
            if let Err(e) = mysql_system_prompt_service.increment_usage_count(system_prompt_id).await {
                error!("增加系统提示词使用次数失败: {}", e);
                // 不影响主要功能，只记录错误
            }

            // 转换为响应格式
            let system_prompt_response = SystemPromptResponse::from_mysql_model(&system_prompt);

            info!("获取系统提示词详情成功: {}", system_prompt_id);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "获取系统提示词详情成功".to_string(),
                data: system_prompt_response,
            })
        }
        Ok(None) => {
            error!("系统提示词不存在: {}", system_prompt_id);
            HttpResponse::NotFound().json(ApiResponse {
                code: ErrorCode::DataNotFound.code(),
                message: "系统提示词不存在".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(MySqlSystemPromptServiceError::DatabaseError(e)) => {
            error!("数据库查询错误: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(e) => {
            error!("获取系统提示词详情失败: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}
