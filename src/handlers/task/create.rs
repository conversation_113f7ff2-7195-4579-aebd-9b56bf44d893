use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};
use serde_json::Value;

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode, task::{CreateTaskRequest, CreateTaskResponse}};
use crate::services::mysql::{MySqlTaskService, MySqlTaskServiceError, MySqlTencentAsrTaskService};
use crate::services::tencent_cloud::{TencentAsrService, CreateRecTaskRequest};
use crate::middleware::token_parser::get_user_id_from_request;
use crate::models::mysql::TaskType;

/// 处理创建任务请求
pub async fn handle(
    req: HttpRequest,
    data: web::Data<AppState>,
    task_data: web::Json<CreateTaskRequest>,
) -> impl Responder {
    info!("收到创建任务请求: {:?}", task_data);

    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("无效的用户ID: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "无效的用户ID".to_string(),
                data: Value::Null,
            });
        }
    };

    // 验证请求参数
    if let Err(error_msg) = task_data.validate() {
        error!("创建任务参数验证失败: {}", error_msg);
        let error_code = ErrorCode::InvalidParameter;
        return HttpResponse::BadRequest().json(ApiResponse {
            code: error_code.code(),
            message: error_msg,
            data: Value::Null,
        });
    }

    // 解析note_id（如果提供）
    let note_id = if let Some(ref note_id_str) = task_data.note_id {
        if note_id_str.trim().is_empty() {
            None
        } else {
            match note_id_str.parse::<u64>() {
                Ok(id) => Some(id),
                Err(_) => {
                    error!("无效的笔记ID格式: {}", note_id_str);
                    return HttpResponse::BadRequest().json(ApiResponse {
                        code: ErrorCode::InvalidParameter.code(),
                        message: "无效的笔记ID格式".to_string(),
                        data: Value::Null,
                    });
                }
            }
        }
    } else {
        None
    };

    // 获取MySQL连接
    let mysql_client = match &data.db {
        Some(db_conn) => match &db_conn.mysql {
            Some(client) => client,
            None => {
                error!("MySQL连接不可用");
                let error_code = ErrorCode::ServiceUnavailable;
                return HttpResponse::InternalServerError().json(ApiResponse {
                    code: error_code.code(),
                    message: error_code.message().to_string(),
                    data: serde_json::Value::Null,
                });
            }
        },
        None => {
            error!("数据库连接不可用");
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 创建任务服务
    let task_service = MySqlTaskService::new(mysql_client.pool().clone());

    // 创建任务
    match task_service
        .create_task_with_note_id(
            user_id,
            task_data.get_task_type(),
            task_data.title.clone(),
            task_data.platform.clone(),
            task_data.url.clone(),
            note_id,
        )
        .await
    {
        Ok(task) => {
            info!("任务创建成功: id={}, type={:?}, title={}, platform={}",
                  task.id, task.task_type, task.title, task.platform);

            // 如果任务类型是3（创建笔记）或4（更新笔记），在Redis中存储用户标记
            if task.task_type == TaskType::CreateNote || task.task_type == TaskType::UpdateNote {
                if let Err(e) = store_create_note_flag(&data, user_id, task.id).await {
                    error!("存储创建笔记标记失败: {}", e);
                    // 注意：这里不返回错误，因为主任务已经创建成功
                }
            }

            // 如果任务类型是2（下载视频）、3（创建笔记）或4（更新笔记），创建腾讯云语音识别任务
            if task.task_type == TaskType::DownloadVideo || task.task_type == TaskType::CreateNote || task.task_type == TaskType::UpdateNote {
                if let Err(e) = create_tencent_asr_task(&data, &task).await {
                    error!("创建腾讯云语音识别任务失败: {}", e);
                    // 注意：这里不返回错误，因为主任务已经创建成功
                }
            }

            let success_code = ErrorCode::Success;
            HttpResponse::Ok().json(ApiResponse {
                code: success_code.code(),
                message: success_code.message().to_string(),
                data: CreateTaskResponse::from_mysql_model(&task),
            })
        }
        Err(e) => {
            let error_code = match e {
                MySqlTaskServiceError::TaskAlreadyExists => ErrorCode::InvalidParameter,
                MySqlTaskServiceError::DatabaseError(_) => ErrorCode::DatabaseError,
                MySqlTaskServiceError::TaskNotFound => ErrorCode::InvalidParameter,
            };

            error!("创建任务失败: {}", e);

            HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: format!("创建任务失败: {}", e),
                data: serde_json::Value::Null,
            })
        }
    }
}

/// 创建腾讯云语音识别任务
async fn create_tencent_asr_task(
    app_state: &web::Data<AppState>,
    task: &crate::models::mysql::MySqlTask,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    info!("为任务 {} 创建腾讯云语音识别任务", task.id);

    // 第一步：先在数据库中创建一条记录（使用临时的task_id和request_id）
    let mysql_client = app_state.db.as_ref()
        .and_then(|db| db.mysql.as_ref())
        .ok_or("MySQL连接不可用")?;
    let asr_task_service = MySqlTencentAsrTaskService::new(mysql_client.pool().clone());

    // 使用临时ID先创建记录，后续会更新
    let temp_task_id = 0u64; // 临时任务ID
    let temp_request_id = format!("temp_{}", task.id); // 临时请求ID

    let asr_task = asr_task_service
        .create_task_with_related(temp_task_id, temp_request_id, Some(task.id))
        .await?;

    info!("数据库记录创建成功，临时ID: {}", asr_task.id);

    // 第二步：调用腾讯云API创建语音识别任务
    let asr_service = TencentAsrService::new(&app_state.config);

    // 构建腾讯云语音识别请求，参数参考测试代码
    let asr_request = CreateRecTaskRequest {
        source_type: 0, // 语音URL
        engine_model_type: "16k_zh_video".to_string(), // 使用视频模型，与测试保持一致
        channel_num: 1, // 单声道
        customization_id: None,
        hotword_id: None,
        url: task.url.clone(),
        speaker_number: 0, // 自动分离说话人
        callback_url: Some("https://20de-2409-8a1e-6084-261-d03e-48a5-480b-492f.ngrok-free.app/tencent_cloud/asr/callback".to_string()),
        res_text_format: 3, // 详细格式
    };

    // 调用腾讯云API
    let response = asr_service.create_rec_task(asr_request).await?;

    info!("腾讯云API调用成功: task_id={}, request_id={}",
          response.data.task_id, response.request_id);

    // 第三步：更新数据库记录，使用真实的task_id和request_id
    // 这里需要直接更新数据库，因为现有的service方法不支持更新task_id
    sqlx::query(
        r#"
        UPDATE tencent_asr_tasks
        SET task_id = ?, request_id = ?, updated_at = NOW()
        WHERE id = ?
        "#
    )
    .bind(response.data.task_id)
    .bind(&response.request_id)
    .bind(asr_task.id)
    .execute(mysql_client.pool())
    .await?;

    info!("腾讯云语音识别任务创建完成: task_id={}, request_id={}",
          response.data.task_id, response.request_id);

    Ok(())
}

/// 在Redis中存储创建笔记的标记
async fn store_create_note_flag(
    app_state: &web::Data<AppState>,
    user_id: u64,
    task_id: u64,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    info!("为用户 {} 存储创建笔记标记，任务ID: {}", user_id, task_id);

    if let Some(db) = &app_state.db {
        let key = format!("create_note_task:{}", user_id);
        let value = task_id.to_string();
        // 设置过期时间为24小时（86400秒）
        db.redis.set_ex(&key, &value, 86400)?;
        info!("创建笔记标记存储成功: {} -> {}", key, value);
    } else {
        return Err("Redis连接不可用".into());
    }

    Ok(())
}
