use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};
use serde_json::Value;

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode, task::{TaskListParams, TaskListResponse, TaskResponse}};
use crate::services::mysql::{MySqlTaskService, MySqlTaskServiceError};
use crate::models::mysql::{TaskType, TaskStatus};
use crate::middleware::token_parser::get_user_id_from_request;

/// 处理获取任务列表请求
pub async fn handle(
    req: HttpRequest,
    params: web::Query<TaskListParams>,
    app_state: web::Data<AppState>,
) -> impl Responder {
    info!("收到获取任务列表请求: {:?}", params);

    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("无效的用户ID: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "无效的用户ID".to_string(),
                data: Value::Null,
            });
        }
    };

    // 验证分页参数
    if params.page < 1 {
        return HttpResponse::BadRequest().json(ApiResponse {
            code: ErrorCode::InvalidParameter.code(),
            message: "页码必须大于0".to_string(),
            data: Value::Null,
        });
    }

    if params.page_size < 1 || params.page_size > 100 {
        return HttpResponse::BadRequest().json(ApiResponse {
            code: ErrorCode::InvalidParameter.code(),
            message: "每页数量必须在1-100之间".to_string(),
            data: Value::Null,
        });
    }

    // 验证任务类型（如果提供）
    let task_type = if let Some(type_value) = params.task_type {
        match type_value {
            1 => Some(TaskType::ExtractContent),
            2 => Some(TaskType::DownloadVideo),
            3 => Some(TaskType::CreateNote),
            4 => Some(TaskType::UpdateNote),
            _ => {
                error!("无效的任务类型: {}", type_value);
                return HttpResponse::BadRequest().json(ApiResponse {
                    code: ErrorCode::InvalidParameter.code(),
                    message: "无效的任务类型，支持的类型：1提取文案 2下载视频 3创建笔记 4更新笔记".to_string(),
                    data: Value::Null,
                });
            }
        }
    } else {
        None
    };

    // 验证任务状态（如果提供）
    let status = if let Some(status_value) = params.status {
        match status_value {
            1 => Some(TaskStatus::Created),
            2 => Some(TaskStatus::Completed),
            3 => Some(TaskStatus::Failed),
            _ => {
                error!("无效的任务状态: {}", status_value);
                return HttpResponse::BadRequest().json(ApiResponse {
                    code: ErrorCode::InvalidParameter.code(),
                    message: "无效的任务状态，支持的状态：1新创建 2已完成 3失败".to_string(),
                    data: Value::Null,
                });
            }
        }
    } else {
        None
    };

    // 获取MySQL连接池
    let mysql_pool = match &app_state.db {
        Some(db) => match &db.mysql {
            Some(mysql) => mysql.pool(),
            None => {
                error!("MySQL连接不可用");
                return HttpResponse::InternalServerError().json(ApiResponse {
                    code: ErrorCode::ServiceUnavailable.code(),
                    message: "服务错误，请稍后再试".to_string(),
                    data: Value::Null,
                });
            }
        },
        None => {
            error!("数据库连接不可用");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: Value::Null,
            });
        }
    };

    // 创建任务服务
    let task_service = MySqlTaskService::new(mysql_pool.clone());

    // 调用服务获取任务列表
    match task_service.get_tasks_with_pagination(
        user_id,
        params.page,
        params.page_size,
        task_type,
        params.platform.clone(),
        status
    ).await {
        Ok((tasks, total)) => {
            info!("获取任务列表成功，共 {} 条", total);

            let task_responses: Vec<TaskResponse> = tasks
                .iter()
                .map(|task| TaskResponse::from_mysql_model(task))
                .collect();

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "获取任务列表成功".to_string(),
                data: TaskListResponse {
                    tasks: task_responses,
                    total,
                    page: params.page,
                    page_size: params.page_size,
                },
            })
        },
        Err(e) => {
            error!("获取任务列表失败: {}", e);

            let error_code = match e {
                MySqlTaskServiceError::DatabaseError(_) => ErrorCode::ServiceUnavailable,
                MySqlTaskServiceError::TaskNotFound => ErrorCode::InvalidParameter,
                MySqlTaskServiceError::TaskAlreadyExists => ErrorCode::InvalidParameter,
            };

            HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: "获取任务列表失败".to_string(),
                data: Value::Null,
            })
        }
    }
}
