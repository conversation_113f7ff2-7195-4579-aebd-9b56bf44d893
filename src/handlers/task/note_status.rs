use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};
use serde_json::Value;

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode};
use crate::middleware::token_parser::get_user_id_from_request;

/// 查询用户是否有创建笔记任务
pub async fn check_create_note_task(
    req: HttpRequest,
    data: web::Data<AppState>,
) -> impl Responder {
    info!("查询用户创建笔记任务状态");

    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("无效的用户ID: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "无效的用户ID".to_string(),
                data: Value::Null,
            });
        }
    };

    // 检查Redis中是否存在创建笔记标记
    match check_create_note_flag(&data, user_id).await {
        Ok(task_id) => {
            let has_task = task_id.is_some();
            info!("用户 {} 创建笔记任务状态: {}, 任务ID: {:?}", user_id, has_task, task_id);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "查询成功".to_string(),
                data: serde_json::json!({
                    "has_create_note_task": has_task,
                    "task_id": task_id
                }),
            })
        }
        Err(e) => {
            error!("检查创建笔记标记失败: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::SystemError.code(),
                message: "检查创建笔记任务状态失败".to_string(),
                data: Value::Null,
            })
        }
    }
}

/// 取消用户的创建笔记任务
pub async fn cancel_create_note_task(
    req: HttpRequest,
    data: web::Data<AppState>,
) -> impl Responder {
    info!("取消用户创建笔记任务");

    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("无效的用户ID: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "无效的用户ID".to_string(),
                data: Value::Null,
            });
        }
    };

    // 删除Redis中的创建笔记标记
    match remove_create_note_flag(&data, user_id).await {
        Ok(_) => {
            info!("用户 {} 创建笔记任务已取消", user_id);
            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "取消创建笔记任务成功".to_string(),
                data: Value::Null,
            })
        }
        Err(e) => {
            error!("取消创建笔记任务失败: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::SystemError.code(),
                message: "取消创建笔记任务失败".to_string(),
                data: Value::Null,
            })
        }
    }
}

/// 检查Redis中是否存在创建笔记标记，返回任务ID
async fn check_create_note_flag(
    app_state: &web::Data<AppState>,
    user_id: u64,
) -> Result<Option<String>, Box<dyn std::error::Error + Send + Sync>> {
    if let Some(db) = &app_state.db {
        let key = format!("create_note_task:{}", user_id);

        if db.redis.exists(&key)? {
            let task_id: String = db.redis.get(&key)?;
            Ok(Some(task_id))
        } else {
            Ok(None)
        }
    } else {
        Err("Redis连接不可用".into())
    }
}

/// 删除Redis中的创建笔记标记
async fn remove_create_note_flag(
    app_state: &web::Data<AppState>,
    user_id: u64,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    if let Some(db) = &app_state.db {
        let key = format!("create_note_task:{}", user_id);
        db.redis.del(&key)?;
        info!("创建笔记标记删除成功: {}", key);
        Ok(())
    } else {
        Err("Redis连接不可用".into())
    }
}
