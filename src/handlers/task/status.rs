use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};
use serde::{Deserialize, Serialize};
use serde_json::Value;

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode};
use crate::services::mysql::{MySqlTaskService, MySqlTaskServiceError};
use crate::middleware::token_parser::get_user_id_from_request;

/// 任务状态查询请求参数
#[derive(Debug, Deserialize)]
pub struct TaskStatusRequest {
    /// 任务ID
    pub task_id: u64,
}

/// 任务状态响应
#[derive(Debug, Serialize)]
pub struct TaskStatusResponse {
    /// 任务ID
    pub id: u64,
    
    /// 任务类型：1提取文案 2下载视频 3创建笔记 4更新笔记
    pub task_type: i8,
    
    /// 任务标题
    pub title: String,
    
    /// 平台信息
    pub platform: String,
    
    /// 提取的地址URL
    pub url: String,
    
    /// 任务状态：1新创建 2已完成 3失败
    pub status: i8,
    
    /// 任务结果
    pub result: Option<String>,
    
    /// 创建时间
    pub create_time: String,
    
    /// 更新时间
    pub update_time: String,
}

/// 查询任务状态接口
pub async fn handle(
    req: HttpRequest,
    query: web::Query<TaskStatusRequest>,
    data: web::Data<AppState>,
) -> impl Responder {
    info!("查询任务状态: task_id={}", query.task_id);

    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("无效的用户ID: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "无效的用户ID".to_string(),
                data: Value::Null,
            });
        }
    };

    // 创建任务服务
    let task_service = match &data.db {
        Some(db_connections) => {
            match crate::db::utils::mysql_helper::get_mysql_pool(db_connections) {
                Some(pool) => MySqlTaskService::new(pool.clone()),
                None => {
                    error!("MySQL连接池不可用");
                    return HttpResponse::InternalServerError().json(ApiResponse {
                        code: ErrorCode::SystemError.code(),
                        message: "数据库连接不可用".to_string(),
                        data: Value::Null,
                    });
                }
            }
        }
        None => {
            error!("数据库连接不可用");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::SystemError.code(),
                message: "数据库连接不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 查询任务详情
    match task_service.get_task_by_id(query.task_id).await {
        Ok(task) => {
            // 验证任务是否属于当前用户
            if task.user_id != user_id {
                error!("用户 {} 尝试访问不属于自己的任务 {}", user_id, query.task_id);
                return HttpResponse::Forbidden().json(ApiResponse {
                    code: ErrorCode::PermissionDenied.code(),
                    message: "无权限访问该任务".to_string(),
                    data: Value::Null,
                });
            }

            info!("查询任务状态成功: id={}, status={:?}", task.id, task.status);

            let response = TaskStatusResponse {
                id: task.id,
                task_type: task.task_type as i8,
                title: task.title,
                platform: task.platform,
                url: task.url,
                status: task.status as i8,
                result: task.result,
                create_time: task.create_time.format("%Y-%m-%d %H:%M:%S").to_string(),
                update_time: task.update_time.format("%Y-%m-%d %H:%M:%S").to_string(),
            };

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "查询任务状态成功".to_string(),
                data: response,
            })
        }
        Err(MySqlTaskServiceError::TaskNotFound) => {
            error!("任务不存在: task_id={}", query.task_id);
            HttpResponse::NotFound().json(ApiResponse {
                code: ErrorCode::TaskNotFound.code(),
                message: "任务不存在".to_string(),
                data: Value::Null,
            })
        }
        Err(e) => {
            error!("查询任务状态失败: {:?}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::SystemError.code(),
                message: "查询任务状态失败".to_string(),
                data: Value::Null,
            })
        }
    }
}
