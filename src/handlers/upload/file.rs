use actix_web::{web, HttpRequest, HttpResponse, Responder};
use actix_multipart::Multipart;
use futures_util::StreamExt;
use log::{error, info};
use serde_json::Value;
use std::time::Instant;

use crate::{
    AppState,
    dto::{ApiResponse, UploadResponse, error_code::ErrorCode},
    middleware::token_parser::get_user_id_from_request,
    utils::OssError,
};

/// 文件上传处理函数
pub async fn handle(
    req: HttpRequest,
    mut payload: Multipart,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 记录处理函数开始时间
    let handler_start_time = Instant::now();
    info!("开始处理文件上传请求");

    // 验证用户是否已登录
    if get_user_id_from_request(&req).is_none() {
        let error_code = ErrorCode::TokenInvalid;
        error!("用户未登录，上传请求被拒绝");
        return HttpResponse::Unauthorized().json(ApiResponse {
            code: error_code.code(),
            message: error_code.message().to_string(),
            data: Value::Null,
        });
    }

    // 获取OSS客户端
    let oss_client = match &app_state.oss_client {
        Some(client) => client,
        None => {
            error!("OSS客户端未初始化");
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: "OSS服务不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 记录获取文件字段开始时间
    let field_start_time = Instant::now();
    info!("开始获取上传文件字段");

    // 处理上传的文件
    let mut field = match payload.next().await {
        Some(Ok(field)) => field,
        _ => {
            error!("未提供文件");
            let error_code = ErrorCode::InvalidParameter;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: "未提供文件".to_string(),
                data: Value::Null,
            });
        }
    };

    let field_duration = field_start_time.elapsed();
    info!("获取上传文件字段完成，耗时: {:?}", field_duration);

    // 获取文件信息
    let content_type = field.content_type()
        .map(|mime| mime.to_string())
        .unwrap_or_else(|| "application/octet-stream".to_string());
    let filename = field.content_disposition()
        .get_filename()
        .unwrap_or("unknown.dat")
        .to_string();

    info!("准备上传文件: {}, 类型: {}", filename, content_type);

    // 记录OSS上传开始时间
    let oss_upload_start_time = Instant::now();
    info!("开始调用OSS上传函数");

    // 上传文件到OSS
    match oss_client.upload_file(&mut field).await {
        Ok(url) => {
            let oss_upload_duration = oss_upload_start_time.elapsed();
            info!("OSS上传函数调用完成，耗时: {:?}", oss_upload_duration);

            // 获取文件大小（由于已经读取过文件，这里只能估计或固定值）
            let size = 0; // 在upload_file方法内部已经读取了文件，无法在这里获取确切大小

            // 构造响应
            let response = UploadResponse {
                url,
                filename,
                content_type,
                size,
            };

            let total_duration = handler_start_time.elapsed();
            info!("文件上传请求处理完成，总耗时: {:?}, 其中获取字段耗时: {:?}, OSS上传函数调用耗时: {:?}",
                  total_duration, field_duration, oss_upload_duration);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "文件上传成功".to_string(),
                data: response,
            })
        },
        Err(e) => {
            let oss_upload_duration = oss_upload_start_time.elapsed();

            let (mut status, code, message) = match e {
                OssError::FileTooLarge => {
                    error!("文件太大，上传失败，耗时: {:?}", oss_upload_duration);
                    (
                        HttpResponse::PayloadTooLarge(),
                        ErrorCode::InvalidParameter,
                        "文件太大，超过20MB限制".to_string()
                    )
                },
                OssError::InvalidFileType => {
                    error!("无效的文件类型，上传失败，耗时: {:?}", oss_upload_duration);
                    (
                        HttpResponse::BadRequest(),
                        ErrorCode::InvalidParameter,
                        "无效的文件类型".to_string()
                    )
                },
                OssError::NoFileProvided => {
                    error!("未提供文件，上传失败，耗时: {:?}", oss_upload_duration);
                    (
                        HttpResponse::BadRequest(),
                        ErrorCode::InvalidParameter,
                        "未提供文件".to_string()
                    )
                },
                _ => {
                    error!("文件上传失败: {:?}, 耗时: {:?}", e, oss_upload_duration);
                    (
                        HttpResponse::InternalServerError(),
                        ErrorCode::SystemError,
                        "文件上传失败".to_string()
                    )
                }
            };

            let total_duration = handler_start_time.elapsed();
            error!("文件上传请求处理失败，总耗时: {:?}", total_duration);

            status.json(ApiResponse {
                code: code.code(),
                message,
                data: Value::Null,
            })
        }
    }
}