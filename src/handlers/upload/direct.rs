use actix_web::{web, HttpRequest, HttpResponse, Responder};
use actix_multipart::Multipart;
use futures_util::StreamExt;
use log::{error, info};
use serde_json::Value;
use std::time::Instant;
use hmac::{Hmac, Mac};
use sha1::Sha1;
use base64::{Engine as _, engine::general_purpose};
use chrono::Utc;
use uuid::Uuid;
use reqwest::header::{HeaderMap, HeaderValue, CONTENT_TYPE, CONTENT_LENGTH, DATE, AUTHORIZATION};

use crate::{
    AppState,
    dto::{ApiResponse, UploadResponse, error_code::ErrorCode},
    utils::OssError,
    config::OssConfig,
};

/// 直接调用阿里云API上传文件（不使用SDK）
pub async fn handle(
    _req: HttpRequest,
    mut payload: Multipart,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 记录处理函数开始时间
    let handler_start_time = Instant::now();
    info!("开始处理直接API文件上传请求");

    // 不验证用户是否已登录，允许匿名上传
    info!("直接上传接口，跳过token验证");

    // 获取OSS配置
    let oss_config = &app_state.config.oss;

    // 记录获取文件字段开始时间
    let field_start_time = Instant::now();
    info!("开始获取上传文件字段");

    // 处理上传的文件
    let mut field = match payload.next().await {
        Some(Ok(field)) => field,
        _ => {
            error!("未提供文件");
            let error_code = ErrorCode::InvalidParameter;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: "未提供文件".to_string(),
                data: Value::Null,
            });
        }
    };

    let field_duration = field_start_time.elapsed();
    info!("获取上传文件字段完成，耗时: {:?}", field_duration);

    // 获取文件信息
    let content_type = field.content_type()
        .map(|mime| mime.to_string())
        .unwrap_or_else(|| "application/octet-stream".to_string());
    let filename = field.content_disposition()
        .get_filename()
        .unwrap_or("unknown.dat")
        .to_string();

    info!("准备上传文件: {}, 类型: {}", filename, content_type);

    // 记录OSS上传开始时间
    let oss_upload_start_time = Instant::now();
    info!("开始调用直接API上传函数");

    // 上传文件到OSS（直接调用API）
    match upload_file_direct(&mut field, oss_config).await {
        Ok(url) => {
            let oss_upload_duration = oss_upload_start_time.elapsed();
            info!("直接API上传函数调用完成，耗时: {:?}", oss_upload_duration);

            // 获取文件大小（由于已经读取过文件，这里只能估计或固定值）
            let size = 0; // 在upload_file方法内部已经读取了文件，无法在这里获取确切大小

            // 构造响应
            let response = UploadResponse {
                url,
                filename,
                content_type,
                size,
            };

            let total_duration = handler_start_time.elapsed();
            info!("直接API文件上传请求处理完成，总耗时: {:?}, 其中获取字段耗时: {:?}, 上传函数调用耗时: {:?}",
                  total_duration, field_duration, oss_upload_duration);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "文件上传成功".to_string(),
                data: response,
            })
        },
        Err(e) => {
            let oss_upload_duration = oss_upload_start_time.elapsed();

            let (mut status, code, message) = match e {
                OssError::FileTooLarge => {
                    error!("文件太大，上传失败，耗时: {:?}", oss_upload_duration);
                    (
                        HttpResponse::PayloadTooLarge(),
                        ErrorCode::InvalidParameter,
                        "文件太大，超过20MB限制".to_string()
                    )
                },
                OssError::InvalidFileType => {
                    error!("无效的文件类型，上传失败，耗时: {:?}", oss_upload_duration);
                    (
                        HttpResponse::BadRequest(),
                        ErrorCode::InvalidParameter,
                        "无效的文件类型".to_string()
                    )
                },
                OssError::NoFileProvided => {
                    error!("未提供文件，上传失败，耗时: {:?}", oss_upload_duration);
                    (
                        HttpResponse::BadRequest(),
                        ErrorCode::InvalidParameter,
                        "未提供文件".to_string()
                    )
                },
                _ => {
                    error!("文件上传失败: {:?}, 耗时: {:?}", e, oss_upload_duration);
                    (
                        HttpResponse::InternalServerError(),
                        ErrorCode::SystemError,
                        "文件上传失败".to_string()
                    )
                }
            };

            let total_duration = handler_start_time.elapsed();
            error!("直接API文件上传请求处理失败，总耗时: {:?}", total_duration);

            status.json(ApiResponse {
                code: code.code(),
                message,
                data: Value::Null,
            })
        }
    }
}

/// 直接调用阿里云OSS API上传文件
async fn upload_file_direct(field: &mut actix_multipart::Field, config: &OssConfig) -> Result<String, OssError> {
    // 记录总体开始时间
    let total_start_time = Instant::now();
    info!("开始处理直接API文件上传");

    // 获取文件名
    let content_disposition = field.content_disposition();
    let filename = content_disposition
        .get_filename()
        .ok_or(OssError::NoFileProvided)?
        .to_string();

    // 检查文件类型
    let content_type = field.content_type()
        .map(|mime| mime.to_string())
        .unwrap_or_else(|| "application/octet-stream".to_string());

    // 生成唯一的文件名
    let file_ext = filename.split('.').last().unwrap_or("dat");
    let timestamp = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs();
    let uuid = Uuid::new_v4().to_string();
    let object_name = format!("uploads/{}/{}_{}.{}", timestamp % 100, uuid, timestamp, file_ext);

    info!("准备上传文件: {}, 类型: {}", filename, content_type);

    // 记录读取数据开始时间
    let read_start_time = Instant::now();
    info!("开始读取文件数据");

    // 读取全部数据
    let mut file_data = Vec::new();
    let mut chunk_count = 0;
    while let Some(chunk) = field.next().await {
        let data = chunk.map_err(|_| OssError::IoError(std::io::Error::new(
            std::io::ErrorKind::Other, "读取文件数据失败")))?;

        chunk_count += 1;

        // 如果文件过大（超过20MB），返回错误
        if file_data.len() + data.len() > 20 * 1024 * 1024 {
            error!("文件太大，超过20MB限制，当前大小: {} 字节", file_data.len() + data.len());
            return Err(OssError::FileTooLarge);
        }

        file_data.extend_from_slice(&data);
    }

    let read_duration = read_start_time.elapsed();
    info!("文件数据读取完成，耗时: {:?}，大小: {} 字节，分块数: {}",
          read_duration, file_data.len(), chunk_count);

    // 构建OSS请求
    let upload_start_time = Instant::now();
    info!("开始通过直接API上传数据到OSS，文件大小: {} 字节", file_data.len());

    // 构建请求URL
    let host = format!("{}.{}.aliyuncs.com", config.bucket, config.region);
    let url = format!("https://{}/{}", host, object_name);

    // 准备签名所需的信息
    let date = Utc::now().format("%a, %d %b %Y %H:%M:%S GMT").to_string();
    let content_md5 = ""; // 可以计算文件的MD5并Base64编码，这里简化处理

    // 构建待签名字符串
    let string_to_sign = format!(
        "PUT\n{}\n{}\n{}\n/{}/{}",
        content_md5,
        content_type,
        date,
        config.bucket,
        object_name
    );

    // 计算签名
    let signature = hmac_sha1(&config.access_key_secret, &string_to_sign)
        .map_err(|e| OssError::OssClientError(e))?;

    // 构建Authorization头
    let authorization = format!("OSS {}:{}", config.access_key_id, signature);

    // 构建请求头
    let mut headers = HeaderMap::new();
    headers.insert(DATE, HeaderValue::from_str(&date).unwrap());
    headers.insert(CONTENT_TYPE, HeaderValue::from_str(&content_type).unwrap());
    headers.insert(CONTENT_LENGTH, HeaderValue::from_str(&file_data.len().to_string()).unwrap());
    headers.insert(AUTHORIZATION, HeaderValue::from_str(&authorization).unwrap());

    // 发送PUT请求
    let client = reqwest::Client::new();
    let response = client
        .put(&url)
        .headers(headers)
        .body(file_data)
        .send()
        .await
        .map_err(|e| OssError::OssClientError(e.to_string()))?;

    // 检查响应
    if !response.status().is_success() {
        let status = response.status();
        let error_text = response.text().await
            .unwrap_or_else(|_| "无法获取错误详情".to_string());
        return Err(OssError::OssClientError(
            format!("OSS服务返回错误状态码 {}: {}", status, error_text)
        ));
    }

    let upload_duration = upload_start_time.elapsed();
    info!("直接API上传完成，耗时: {:?}", upload_duration);

    // 构造可访问的URL
    let url = format!("https://{}/{}", host, object_name);

    let total_duration = total_start_time.elapsed();
    info!("文件上传成功: {}, 总耗时: {:?}, 读取耗时: {:?} ({:.1}%), 上传耗时: {:?} ({:.1}%)",
          url,
          total_duration,
          read_duration,
          (read_duration.as_secs_f64() / total_duration.as_secs_f64()) * 100.0,
          upload_duration,
          (upload_duration.as_secs_f64() / total_duration.as_secs_f64()) * 100.0);

    Ok(url)
}

/// 使用HMAC-SHA1算法计算签名
fn hmac_sha1(key: &str, data: &str) -> Result<String, String> {
    type HmacSha1 = Hmac<Sha1>;

    let mut mac = HmacSha1::new_from_slice(key.as_bytes())
        .map_err(|e| format!("HMAC初始化失败: {}", e))?;

    mac.update(data.as_bytes());
    let result = mac.finalize().into_bytes();

    // Base64编码
    Ok(general_purpose::STANDARD.encode(result))
}
