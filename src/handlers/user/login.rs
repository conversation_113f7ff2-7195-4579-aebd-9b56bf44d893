use actix_web::{web, HttpResponse, Responder};
use log::{info, error, debug};
use uuid::Uuid;
use std::time::{SystemTime, UNIX_EPOCH};
use redis::RedisError;

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode, user::{LoginRequest, LoginData}};
use crate::services::mysql::MySqlUserServiceError;
use crate::utils::validators::is_valid_china_mobile;
use crate::db::RedisClient;

/**
 * 用户登录处理函数
 *
 * @param data 应用状态数据
 * @param user_data 用户登录请求数据
 * @return HTTP 响应
 */
pub async fn handle(data: web::Data<AppState>, user_data: web::Json<LoginRequest>) -> impl Responder {
    // 校验参数
    // 1. 手机号格式校验
    if !is_valid_china_mobile(&user_data.phone) {
        let error_code = ErrorCode::InvalidPhoneFormat;
        return HttpResponse::BadRequest().json(ApiResponse {
            code: error_code.code(),
            data: serde_json::Value::Null,
            message: error_code.message().to_string(),
        });
    }

    // 2. 调用MySQL用户服务登录
    let mysql_user_service = match &data.mysql_user_service {
        Some(service) => service,
        None => {
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                data: serde_json::Value::Null,
                message: error_code.message().to_string(),
            });
        }
    };

    // 3. 登录用户
    match mysql_user_service.login(&user_data.phone, &user_data.password).await {
        Ok(user) => {
            let user_id = user.id.to_string();
            info!("用户登录成功: {}", user_id);

            // 生成 token
            let token = generate_token();
            debug!("生成 token: {}", token);

            // 存储 token 到 Redis
            if let Err(e) = store_token_in_redis(&data, &token, &user_id).await {
                error!("存储 token 到 Redis 失败: {}", e);
                let error_code = ErrorCode::SystemError;
                return HttpResponse::InternalServerError().json(ApiResponse {
                    code: error_code.code(),
                    data: serde_json::Value::Null,
                    message: "登录失败，请稍后重试".to_string(),
                });
            }

            let success_code = ErrorCode::Success;
            HttpResponse::Ok().json(ApiResponse {
                code: success_code.code(),
                data: LoginData {
                    user_id,
                    phone: user.phone,
                    token,
                },
                message: success_code.message().to_string(),
            })
        },
        Err(e) => {
            error!("用户登录失败: {}", e);
            // 根据错误类型设置不同的错误代码
            let error_code = match e {
                MySqlUserServiceError::UserNotFound => ErrorCode::UserNotFound,
                MySqlUserServiceError::PasswordMismatch => ErrorCode::PasswordMismatch,
                MySqlUserServiceError::DatabaseError(_) => ErrorCode::DatabaseError,
                _ => ErrorCode::SystemError,
            };

            HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                data: serde_json::Value::Null,
                message: error_code.message().to_string(),
            })
        }
    }
}

/// 生成唯一的 token
fn generate_token() -> String {
    // 使用 UUID v4 生成随机 token
    let uuid = Uuid::new_v4();
    // 添加时间戳以确保唯一性
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();

    format!("{}-{}", uuid, timestamp)
}

/// 将 token 存储到 Redis 中
async fn store_token_in_redis(app_state: &web::Data<AppState>, token: &str, user_id: &str) -> Result<(), RedisError> {
    // 获取 Redis 客户端
    if let Some(db) = &app_state.db {
        // 设置 token 到 Redis，有效期为 7 天（604800 秒）
        db.redis.set_ex(token, user_id, 604800)
    } else {
        Err(RedisError::from((redis::ErrorKind::IoError, "Redis connection not available")))
    }
}
