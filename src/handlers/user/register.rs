use actix_web::{web, HttpResponse, Responder};
use log::{info, error, debug, warn};
use uuid::Uuid;
use std::time::{SystemTime, UNIX_EPOCH};
use redis::RedisError;

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode, user::{RegisterRequest, RegisterData}};
use crate::services::mysql::MySqlUserServiceError;
use crate::utils::validators::{is_valid_china_mobile, is_valid_password};

/**
 * 用户注册处理函数
 *
 * @param data 应用状态数据
 * @param user_data 用户注册请求数据
 * @return HTTP 响应
 */
pub async fn handle(data: web::Data<AppState>, user_data: web::Json<RegisterRequest>) -> impl Responder {
    // 校验参数
    // 1. 手机号格式校验（使用正则表达式验证中国大陆手机号）
    if !is_valid_china_mobile(&user_data.phone) {
        let error_code = ErrorCode::InvalidPhoneFormat;
        return HttpResponse::BadRequest().json(ApiResponse {
            code: error_code.code(),
            data: RegisterData { user_id: None, token: None },
            message: error_code.message().to_string(),
        });
    }

    // 2. 密码强度校验（长度 6-20 位，不包含空格）
    if !is_valid_password(&user_data.password) {
        let error_code = ErrorCode::PasswordTooShort;
        return HttpResponse::BadRequest().json(ApiResponse {
            code: error_code.code(),
            data: RegisterData { user_id: None, token: None },
            message: error_code.message().to_string(),
        });
    }

    // 3. 调用MySQL用户服务注册用户
    let mysql_user_service = match &data.mysql_user_service {
        Some(service) => service,
        None => {
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                data: RegisterData { user_id: None, token: None },
                message: error_code.message().to_string(),
            });
        }
    };

    // 4. 注册用户
    match mysql_user_service.register_user(user_data.phone.clone(), user_data.password.clone()).await {
        Ok(user) => {
            let user_id = user.id.to_string();
            info!("用户注册成功: {}", user_id);

            // 生成 token
            let token = generate_token();
            debug!("生成 token: {}", token);

            // 存储 token 到 Redis
            if let Err(e) = store_token_in_redis(&data, &token, &user_id).await {
                error!("存储 token 到 Redis 失败: {}", e);
                let error_code = ErrorCode::SystemError;
                return HttpResponse::InternalServerError().json(ApiResponse {
                    code: error_code.code(),
                    data: RegisterData { user_id: Some(user_id), token: None },
                    message: "Token 存储失败，请稍后重试".to_string(),
                });
            }

            // 为新注册用户创建默认收藏夹
            create_default_favorite(&data, user.id).await;

            let success_code = ErrorCode::Success;
            HttpResponse::Created().json(ApiResponse {
                code: success_code.code(),
                data: RegisterData { user_id: Some(user_id), token: Some(token) },
                message: success_code.message().to_string(),
            })
        },
        Err(e) => {
            error!("用户注册失败: {}", e);
            // 根据错误类型设置不同的错误代码
            let error_code = match e {
                MySqlUserServiceError::UserAlreadyExists => ErrorCode::UserAlreadyExists,
                MySqlUserServiceError::DatabaseError(_) => ErrorCode::DatabaseError,
                _ => ErrorCode::SystemError,
            };

            HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                data: RegisterData { user_id: None, token: None },
                message: format!("{}", error_code),
            })
        }
    }
}

/// 生成唯一的 token
fn generate_token() -> String {
    // 使用 UUID v4 生成随机 token
    let uuid = Uuid::new_v4();
    // 添加时间戳以确保唯一性
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();

    format!("{}-{}", uuid, timestamp)
}

/// 将 token 存储到 Redis 中
async fn store_token_in_redis(app_state: &web::Data<AppState>, token: &str, user_id: &str) -> Result<(), RedisError> {
    // 获取 Redis 客户端
    if let Some(db) = &app_state.db {
        // 设置 token 到 Redis，有效期为 7 天（604800 秒）
        db.redis.set_ex(token, user_id, 604800)
    } else {
        Err(RedisError::from((redis::ErrorKind::IoError, "Redis connection not available")))
    }
}

/// 为新注册用户创建默认收藏夹
async fn create_default_favorite(app_state: &web::Data<AppState>, user_id: u64) {
    // 获取MySQL收藏夹服务
    let mysql_favorite_service = match &app_state.mysql_favorite_service {
        Some(service) => service,
        None => {
            warn!("MySQL收藏夹服务未初始化，无法创建默认收藏夹");
            return;
        }
    };

    // 创建默认收藏夹
    match mysql_favorite_service
        .create_favorite(
            user_id,
            "默认收藏夹".to_string(),
            "".to_string(), // 空封面，将由第一个书签自动设置
        )
        .await
    {
        Ok(favorite) => {
            info!("为用户 {} 创建默认收藏夹成功，收藏夹ID: {}", user_id, favorite.id);
        }
        Err(e) => {
            error!("为用户 {} 创建默认收藏夹失败: {}", user_id, e);
            // 这里不影响用户注册流程，只记录错误日志
        }
    }
}
