use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{error, info};
use serde_json::Value;

use crate::{
    AppState,
    dto::{ApiResponse, error_code::ErrorCode, note::{DeleteNoteRequest, DeleteNoteResponse}},
    middleware::token_parser::get_user_id_from_request,
    services::mysql::MySqlNoteServiceError,
};

/**
 * 删除笔记处理函数
 *
 * 此函数处理POST /note/delete请求，用于删除指定的笔记（软删除）
 * 请求参数：
 * - id: 笔记ID（字符串）
 * 
 * 返回数据：
 * - id: 被删除的笔记ID
 *
 * @param req HTTP请求
 * @param payload 请求体，包含笔记删除信息
 * @param app_state 应用状态
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    payload: web::Json<DeleteNoteRequest>,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 将字符串用户ID转换为 u64
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("无效的用户ID格式: {}", e);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "无效的用户ID格式".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析笔记ID
    let note_id = match payload.id.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("笔记ID格式错误: {}", payload.id);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "笔记ID格式错误".to_string(),
                data: Value::Null,
            });
        }
    };

    // 获取MySQL笔记服务
    let mysql_note_service = match &app_state.mysql_note_service {
        Some(service) => service,
        None => {
            error!("MySQL笔记服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务暂时不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 删除笔记
    match mysql_note_service.delete_note(note_id, user_id).await {
        Ok(note) => {
            let note_id_str = note.id.to_string();
            info!("笔记删除成功，笔记ID: {}", note_id_str);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "笔记删除成功".to_string(),
                data: DeleteNoteResponse {
                    id: note_id_str,
                },
            })
        }
        Err(e) => {
            error!("删除笔记失败: {}", e);

            let (error_code, message) = match e {
                MySqlNoteServiceError::NoteNotFound => {
                    (ErrorCode::InvalidParameter, "笔记不存在或已被删除")
                }
                MySqlNoteServiceError::PermissionDenied => {
                    (ErrorCode::InvalidParameter, "无权限删除该笔记")
                }
                MySqlNoteServiceError::DatabaseError(_) => {
                    (ErrorCode::DatabaseError, "数据库错误")
                }
                MySqlNoteServiceError::DatabaseNotAvailable => {
                    (ErrorCode::ServiceUnavailable, "服务暂时不可用")
                }
                MySqlNoteServiceError::InvalidParameter => {
                    (ErrorCode::InvalidParameter, "参数无效")
                }
            };

            HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: message.to_string(),
                data: Value::Null,
            })
        }
    }
}
