use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{error, info};
use serde::Deserialize;

use crate::{
    AppState,
    dto::{ApiResponse, error_code::ErrorCode, NoteDetailResponse},
    services::mysql::MySqlNoteServiceError,
};

/// 获取笔记详情请求参数
#[derive(Debug, Deserialize)]
pub struct NoteDetailParams {
    /// 笔记ID
    pub id: String,
}

/**
 * 获取笔记详情处理函数
 *
 * 此函数处理GET /note/detail请求，用于获取指定笔记的详细信息
 *
 * @param req HTTP请求
 * @param app_state 应用状态
 * @param query 查询参数
 * @return HTTP响应
 */
pub async fn handle(
    _req: HttpRequest,
    app_state: web::Data<AppState>,
    query: web::Query<NoteDetailParams>,
) -> impl Responder {
    // 解析笔记ID
    let note_id = match query.id.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("笔记ID格式错误: {}", query.id);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "笔记ID格式错误".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取MySQL笔记服务
    let mysql_note_service = match &app_state.mysql_note_service {
        Some(service) => service,
        None => {
            error!("MySQL笔记服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务暂时不可用".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取笔记详情
    match mysql_note_service.get_note_by_id(note_id).await {
        Ok(note) => {
            info!("获取笔记详情成功，笔记ID: {}", note_id);

            // 转换为响应格式
            let note_response = NoteDetailResponse::from_mysql_model(&note);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "获取笔记详情成功".to_string(),
                data: note_response,
            })
        }
        Err(e) => {
            error!("获取笔记详情失败: {}", e);

            let (error_code, message) = match e {
                MySqlNoteServiceError::NoteNotFound => {
                    (ErrorCode::InvalidParameter, "笔记不存在")
                }
                MySqlNoteServiceError::PermissionDenied => {
                    (ErrorCode::InvalidParameter, "无权限访问该笔记")
                }
                MySqlNoteServiceError::DatabaseError(_) => {
                    (ErrorCode::DatabaseError, "服务错误，请稍后再试")
                }
                MySqlNoteServiceError::DatabaseNotAvailable => {
                    (ErrorCode::ServiceUnavailable, "服务暂时不可用")
                }
                MySqlNoteServiceError::InvalidParameter => {
                    (ErrorCode::InvalidParameter, "参数无效")
                }
            };

            HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: message.to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}
