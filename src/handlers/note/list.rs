use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{error, info};
use serde_json::Value;

use crate::{
    AppState,
    dto::{ApiResponse, error_code::ErrorCode, note::{NoteListParams, NoteListResponse, NoteDetailResponse}},
    middleware::token_parser::get_user_id_from_request,
    services::mysql::MySqlNoteServiceError,
};

/**
 * 获取笔记列表处理函数
 *
 * 此函数处理GET /note/list请求，用于获取用户的笔记列表
 * 支持分页和按合集过滤
 *
 * @param req HTTP请求
 * @param app_state 应用状态
 * @param query 查询参数
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    app_state: web::Data<AppState>,
    query: web::Query<NoteListParams>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式错误: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "用户ID格式错误".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析合集ID（如果提供）
    let parent_id = if let Some(parent_id_str) = &query.parent_id {
        match parent_id_str.parse::<u64>() {
            Ok(id) => Some(id),
            Err(_) => {
                error!("合集ID格式错误: {}", parent_id_str);
                return HttpResponse::BadRequest().json(ApiResponse {
                    code: ErrorCode::InvalidParameter.code(),
                    message: "合集ID格式错误".to_string(),
                    data: Value::Null,
                });
            }
        }
    } else {
        None
    };

    // 验证分页参数
    if query.page < 1 {
        error!("页码必须大于0");
        return HttpResponse::BadRequest().json(ApiResponse {
            code: ErrorCode::InvalidParameter.code(),
            message: "页码必须大于0".to_string(),
            data: Value::Null,
        });
    }

    if query.page_size < 1 || query.page_size > 100 {
        error!("每页数量必须在1-100之间");
        return HttpResponse::BadRequest().json(ApiResponse {
            code: ErrorCode::InvalidParameter.code(),
            message: "每页数量必须在1-100之间".to_string(),
            data: Value::Null,
        });
    }

    // 获取MySQL笔记服务
    let mysql_note_service = match &app_state.mysql_note_service {
        Some(service) => service,
        None => {
            error!("MySQL笔记服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务暂时不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 获取笔记列表
    match mysql_note_service.get_notes_by_user_id(
        user_id,
        parent_id,
        query.page,
        query.page_size,
    ).await {
        Ok((notes, total)) => {
            info!(
                "获取用户 {} 的笔记列表成功，合集ID: {:?}，共 {} 条",
                user_id, parent_id, total
            );

            // 转换为响应格式
            let note_responses: Vec<NoteDetailResponse> = notes
                .iter()
                .map(|note| NoteDetailResponse::from_mysql_model(note))
                .collect();

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "获取笔记列表成功".to_string(),
                data: NoteListResponse {
                    notes: note_responses,
                    total,
                    page: query.page,
                    page_size: query.page_size,
                },
            })
        }
        Err(e) => {
            error!("获取笔记列表失败: {}", e);

            let (error_code, message) = match e {
                MySqlNoteServiceError::DatabaseError(_) => {
                    (ErrorCode::DatabaseError, "服务错误，请稍后再试")
                }
                MySqlNoteServiceError::DatabaseNotAvailable => {
                    (ErrorCode::ServiceUnavailable, "服务暂时不可用")
                }
                MySqlNoteServiceError::InvalidParameter => {
                    (ErrorCode::InvalidParameter, "参数无效")
                }
                _ => {
                    (ErrorCode::SystemError, "系统错误")
                }
            };

            HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: message.to_string(),
                data: Value::Null,
            })
        }
    }
}
