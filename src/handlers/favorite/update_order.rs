use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};
use serde::{Deserialize, Serialize};

use crate::AppState;
use crate::dto::{ApiResponse, error_code::ErrorCode};
use crate::middleware::token_parser::get_user_id_from_request;
use crate::services::mysql::MySqlFavoriteServiceError;

/// 更新收藏夹排序请求
#[derive(Debug, Deserialize)]
pub struct UpdateFavoriteOrderRequest {
    /// 收藏夹ID
    pub id: String,

    /// 新的排序值
    pub order: String,
}

/// 更新收藏夹排序响应
#[derive(Debug, Serialize)]
pub struct UpdateFavoriteOrderResponse {
    /// 收藏夹ID
    pub id: String,
}

/// 处理更新收藏夹排序请求
pub async fn handle(
    data: web::Data<AppState>,
    req: HttpRequest,
    order_data: web::Json<UpdateFavoriteOrderRequest>,
) -> impl Responder {
    // 从请求中获取用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("无法获取用户ID");
            let error_code = ErrorCode::TokenInvalid;
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 将用户ID字符串转换为u64
    let _user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("用户ID格式无效: {}", e);
            let error_code = ErrorCode::InvalidUserId;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 将收藏夹ID字符串转换为u64
    let favorite_id = match order_data.id.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("收藏夹ID格式无效: {}", e);
            let error_code = ErrorCode::InvalidFavoriteId;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取MySQL收藏夹服务
    let favorite_service = match data.mysql_favorite_service.as_ref() {
        Some(service) => service,
        None => {
            error!("MySQL收藏夹服务不可用");
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 将排序值字符串转换为整数
    let order_value = match order_data.order.parse::<i32>() {
        Ok(value) => value,
        Err(e) => {
            error!("排序值格式无效: {}", e);
            let error_code = ErrorCode::InvalidParameter;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: "排序值必须是整数".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 更新收藏夹排序
    match favorite_service
        .update_favorite(
            favorite_id,
            None, // 不更新名称
            None, // 不更新封面
            Some(order_value), // 只更新排序值
        )
        .await
    {
        Ok(favorite) => {
            let favorite_id = favorite.id.to_string();

            info!("收藏夹排序更新成功: {}", favorite_id);

            let success_code = ErrorCode::Success;
            HttpResponse::Ok().json(ApiResponse {
                code: success_code.code(),
                message: success_code.message().to_string(),
                data: UpdateFavoriteOrderResponse {
                    id: favorite_id
                },
            })
        }
        Err(e) => {
            let error_code = match e {
                MySqlFavoriteServiceError::FavoriteNotFound => ErrorCode::FavoriteNotFound,
                MySqlFavoriteServiceError::DatabaseError(_) => ErrorCode::DatabaseError,
                _ => ErrorCode::SystemError,
            };

            error!("更新收藏夹排序失败: {}", e);

            HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}
