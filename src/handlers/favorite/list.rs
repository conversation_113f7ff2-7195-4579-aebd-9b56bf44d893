use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

use crate::AppState;
use crate::dto::{ApiResponse, error_code::ErrorCode};
use crate::middleware::token_parser::get_user_id_from_request;
use crate::services::mysql::MySqlFavoriteServiceError;

/// 收藏夹列表响应中的收藏夹项
#[derive(Debug, Serialize)]
pub struct FavoriteItem {
    /// 收藏夹ID
    pub id: String,

    /// 收藏夹名称
    pub name: String,

    /// 封面图片URL
    pub cover: String,

    /// 排序值
    pub order: String,

    /// 创建时间
    pub create_time: DateTime<Utc>,

    /// 最后更新时间
    pub update_time: DateTime<Utc>,
}

/// 分页参数
#[derive(Debug, Deserialize)]
pub struct PaginationParams {
    /// 页码，从1开始
    #[serde(default = "default_page")]
    pub page: u32,

    /// 每页数量
    #[serde(default = "default_page_size")]
    pub page_size: u32,
}

/// 默认页码
#[inline]
fn default_page() -> u32 {
    1
}

/// 默认每页数量
#[inline]
fn default_page_size() -> u32 {
    10
}

/// 收藏夹列表响应
#[derive(Debug, Serialize)]
pub struct FavoriteListResponse {
    /// 收藏夹列表
    pub favorites: Vec<FavoriteItem>,

    /// 收藏夹总数
    pub total: u64,

    /// 当前页码
    pub page: u32,

    /// 每页数量
    pub page_size: u32,
}

/// 处理获取收藏夹列表请求
pub async fn handle(
    data: web::Data<AppState>,
    req: HttpRequest,
    query: web::Query<PaginationParams>,
) -> impl Responder {
    // 从请求中获取用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("无法获取用户ID");
            let error_code = ErrorCode::TokenInvalid;
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 将字符串用户ID转换为 u64
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("无效的用户ID格式: {}", e);
            let error_code = ErrorCode::InvalidParameter;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: "无效的用户ID格式".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取MySQL收藏夹服务
    let mysql_favorite_service = match &data.mysql_favorite_service {
        Some(service) => service,
        None => {
            error!("MySQL收藏夹服务不可用");
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取分页参数
    let page = query.page;
    let page_size = query.page_size;

    // 获取用户的收藏夹列表
    match mysql_favorite_service.get_favorites_by_user_id(user_id, page, page_size).await {
        Ok((favorites, total)) => {
            // 转换为响应格式
            let favorite_items: Vec<FavoriteItem> = favorites.into_iter().map(|f| {
                FavoriteItem {
                    id: f.id.to_string(),
                    name: f.name,
                    cover: f.cover,
                    order: f.order.to_string(),
                    create_time: f.created_at,
                    update_time: f.updated_at,
                }
            }).collect();

            info!("获取用户 {} 的收藏夹列表成功，共 {} 个", user_id, total);

            let success_code = ErrorCode::Success;
            HttpResponse::Ok().json(ApiResponse {
                code: success_code.code(),
                message: success_code.message().to_string(),
                data: FavoriteListResponse {
                    favorites: favorite_items,
                    total,
                    page,
                    page_size,
                },
            })
        }
        Err(e) => {
            let error_code = match e {
                MySqlFavoriteServiceError::DatabaseError(_) => ErrorCode::DatabaseError,
                _ => ErrorCode::SystemError,
            };

            error!("获取收藏夹列表失败: {}", e);

            HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}
