use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};
use serde::{Deserialize, Serialize};

use crate::AppState;
use crate::dto::{ApiResponse, error_code::ErrorCode};
use crate::middleware::token_parser::get_user_id_from_request;
use crate::services::mysql::MySqlFavoriteServiceError;

/// 交换收藏夹位置请求
#[derive(Debug, Deserialize)]
pub struct SwapFavoriteRequest {
    /// 源收藏夹ID
    pub source_id: String,

    /// 目标收藏夹ID
    pub target_id: String,
}

/// 交换收藏夹位置响应
#[derive(Debug, Serialize)]
pub struct SwapFavoriteResponse {
    /// 是否成功
    pub success: bool,
}

/// 处理交换收藏夹位置请求
pub async fn handle(
    data: web::Data<AppState>,
    req: HttpRequest,
    swap_data: web::Json<SwapFavoriteRequest>,
) -> impl Responder {
    // 从请求中获取用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("无法获取用户ID");
            let error_code = ErrorCode::TokenInvalid;
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 将用户ID字符串转换为u64
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("用户ID格式无效: {}", e);
            let error_code = ErrorCode::InvalidUserId;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 将源收藏夹ID字符串转换为u64
    let source_id = match swap_data.source_id.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("源收藏夹ID格式无效: {}", e);
            let error_code = ErrorCode::InvalidFavoriteId;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 将目标收藏夹ID字符串转换为u64
    let target_id = match swap_data.target_id.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("目标收藏夹ID格式无效: {}", e);
            let error_code = ErrorCode::InvalidFavoriteId;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取MySQL收藏夹服务
    let favorite_service = match data.mysql_favorite_service.as_ref() {
        Some(service) => service,
        None => {
            error!("MySQL收藏夹服务不可用");
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 交换收藏夹位置
    match favorite_service
        .swap_favorite_positions(
            user_id,
            source_id,
            target_id,
        )
        .await
    {
        Ok(_) => {
            info!("收藏夹位置交换成功: {} <-> {}", swap_data.source_id, swap_data.target_id);

            let success_code = ErrorCode::Success;
            HttpResponse::Ok().json(ApiResponse {
                code: success_code.code(),
                message: success_code.message().to_string(),
                data: SwapFavoriteResponse {
                    success: true
                },
            })
        }
        Err(e) => {
            let error_code = match e {
                MySqlFavoriteServiceError::FavoriteNotFound => ErrorCode::FavoriteNotFound,
                MySqlFavoriteServiceError::DatabaseError(_) => ErrorCode::DatabaseError,
                _ => ErrorCode::SystemError,
            };

            error!("交换收藏夹位置失败: {}", e);

            HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}
