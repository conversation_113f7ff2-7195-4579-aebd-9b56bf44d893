use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};
use serde::{Deserialize, Serialize};

use crate::AppState;
use crate::dto::{ApiResponse, error_code::ErrorCode};
use crate::middleware::token_parser::get_user_id_from_request;
use crate::services::mysql::MySqlFavoriteServiceError;

/// 更新收藏夹请求
#[derive(Debug, Deserialize)]
pub struct UpdateFavoriteRequest {
    /// 收藏夹ID
    pub id: String,

    /// 收藏夹名称
    pub name: String,

    /// 封面图片URL
    #[serde(default)]
    pub cover: String,
}

/// 更新收藏夹响应
#[derive(Debug, Serialize)]
pub struct UpdateFavoriteResponse {
    /// 收藏夹ID
    pub id: String,
}

/// 处理更新收藏夹请求
pub async fn handle(
    data: web::Data<AppState>,
    req: HttpRequest,
    favorite_data: web::Json<UpdateFavoriteRequest>,
) -> impl Responder {
    // 从请求中获取用户ID
    let _user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("无法获取用户ID");
            let error_code = ErrorCode::TokenInvalid;
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 验证参数
    if favorite_data.name.trim().is_empty() {
        error!("收藏夹名称不能为空");
        let error_code = ErrorCode::FavoriteNameEmpty;
        return HttpResponse::BadRequest().json(ApiResponse {
            code: error_code.code(),
            message: error_code.message().to_string(),
            data: serde_json::Value::Null,
        });
    }

    if favorite_data.name.chars().count() > 12 {
        error!("收藏夹名称过长");
        let error_code = ErrorCode::FavoriteNameTooLong;
        return HttpResponse::BadRequest().json(ApiResponse {
            code: error_code.code(),
            message: error_code.message().to_string(),
            data: serde_json::Value::Null,
        });
    }

    // 将字符串收藏夹ID转换为 u64
    let favorite_id = match favorite_data.id.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("无效的收藏夹ID格式: {}", e);
            let error_code = ErrorCode::InvalidParameter;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: "无效的收藏夹ID格式".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取MySQL收藏夹服务
    let mysql_favorite_service = match &data.mysql_favorite_service {
        Some(service) => service,
        None => {
            error!("MySQL收藏夹服务不可用");
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 更新收藏夹
    match mysql_favorite_service
        .update_favorite(
            favorite_id,
            Some(favorite_data.name.clone()),
            Some(favorite_data.cover.clone()),
            None, // 不更新排序值
        )
        .await
    {
        Ok(favorite) => {
            let favorite_id = favorite.id.to_string();

            info!("收藏夹更新成功: {}", favorite_id);

            let success_code = ErrorCode::Success;
            HttpResponse::Ok().json(ApiResponse {
                code: success_code.code(),
                message: success_code.message().to_string(),
                data: UpdateFavoriteResponse {
                    id: favorite_id
                },
            })
        }
        Err(e) => {
            let error_code = match e {
                MySqlFavoriteServiceError::FavoriteNotFound => ErrorCode::FavoriteNotFound,
                MySqlFavoriteServiceError::FavoriteAlreadyExists => ErrorCode::FavoriteAlreadyExists,
                MySqlFavoriteServiceError::DatabaseError(_) => ErrorCode::DatabaseError,
                _ => ErrorCode::SystemError,
            };

            error!("更新收藏夹失败: {}", e);

            HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}
