use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};
use serde::{Deserialize, Serialize};

use crate::AppState;
use crate::dto::{ApiResponse, error_code::ErrorCode};
use crate::middleware::token_parser::get_user_id_from_request;
use crate::services::mysql::{MySqlFavoriteServiceError, MySqlBookmarkServiceError};

/// 删除收藏夹请求
#[derive(Debug, Deserialize)]
pub struct DeleteFavoriteRequest {
    /// 收藏夹ID
    pub id: String,
}

/// 删除收藏夹响应
#[derive(Debug, Serialize)]
pub struct DeleteFavoriteResponse {
    /// 收藏夹ID
    pub id: String,
    /// 删除的书签数量
    pub deleted_bookmarks_count: u64,
}

/// 处理删除收藏夹请求
pub async fn handle(
    data: web::Data<AppState>,
    req: HttpRequest,
    favorite_data: web::Json<DeleteFavoriteRequest>,
) -> impl Responder {
    // 从请求中获取用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("无法获取用户ID");
            let error_code = ErrorCode::TokenInvalid;
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 将字符串用户ID转换为 u64
    let _user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("无效的用户ID格式: {}", e);
            let error_code = ErrorCode::InvalidUserId;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 将字符串收藏夹ID转换为 u64
    let favorite_id = match favorite_data.id.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("无效的收藏夹ID格式: {}", e);
            let error_code = ErrorCode::InvalidFavoriteId;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取MySQL收藏夹服务
    let mysql_favorite_service = match &data.mysql_favorite_service {
        Some(service) => service,
        None => {
            error!("MySQL收藏夹服务不可用");
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取MySQL书签服务
    let mysql_bookmark_service = match &data.mysql_bookmark_service {
        Some(service) => service,
        None => {
            error!("MySQL书签服务不可用");
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 删除收藏夹下的所有书签
    let deleted_bookmarks_count = match mysql_bookmark_service.delete_bookmarks_by_favorite_id(favorite_id).await {
        Ok(count) => count,
        Err(e) => {
            let error_code = match e {
                MySqlBookmarkServiceError::DatabaseError(_) => ErrorCode::DatabaseError,
                _ => ErrorCode::SystemError,
            };

            error!("删除收藏夹下的书签失败: {}", e);

            return HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 删除收藏夹
    match mysql_favorite_service.delete_favorite(favorite_id).await {
        Ok(()) => {
            let favorite_id_str = favorite_data.id.clone();

            info!("收藏夹删除成功: {}, 删除书签数量: {}", favorite_id_str, deleted_bookmarks_count);

            let success_code = ErrorCode::Success;
            HttpResponse::Ok().json(ApiResponse {
                code: success_code.code(),
                message: success_code.message().to_string(),
                data: DeleteFavoriteResponse {
                    id: favorite_id_str,
                    deleted_bookmarks_count,
                },
            })
        }
        Err(e) => {
            let error_code = match e {
                MySqlFavoriteServiceError::FavoriteNotFound => ErrorCode::FavoriteNotFound,
                MySqlFavoriteServiceError::DatabaseError(_) => ErrorCode::DatabaseError,
                _ => ErrorCode::SystemError,
            };

            error!("删除收藏夹失败: {}", e);

            HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}
