use actix_web::{web, HttpResponse, Responder};
use log::{info, error};

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode};
use crate::dto::app_version::VersionCheckResponse;
use crate::services::mysql::MySqlAppVersionServiceError;

/**
 * 版本检查处理函数
 *
 * GET /app/version/check
 *
 * @param data 应用状态数据
 * @return HTTP 响应
 */
pub async fn handle(data: web::Data<AppState>) -> impl Responder {
    info!("处理版本检查请求");

    // 获取MySQL应用版本服务
    let mysql_app_version_service = match &data.mysql_app_version_service {
        Some(service) => service,
        None => {
            error!("MySQL应用版本服务未初始化");
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                data: VersionCheckResponse {
                    version: "".to_string(),
                    update_description: vec![],
                },
                message: error_code.message().to_string(),
            });
        }
    };

    // 获取当前版本信息
    match mysql_app_version_service.get_current_version().await {
        Ok(version) => {
            info!("获取当前版本成功: {}", version.version);

            let version_string = version.version.clone();
            let update_description = version.get_update_description_as_strings();

            let success_code = ErrorCode::Success;
            HttpResponse::Ok().json(ApiResponse {
                code: success_code.code(),
                data: VersionCheckResponse {
                    version: version_string,
                    update_description,
                },
                message: success_code.message().to_string(),
            })
        },
        Err(e) => {
            error!("获取当前版本失败: {}", e);

            let error_code = match e {
                MySqlAppVersionServiceError::VersionNotFound => {
                    // 如果没有找到当前版本，返回默认版本信息
                    info!("未找到当前版本，返回默认版本信息");
                    return HttpResponse::Ok().json(ApiResponse {
                        code: 0,
                        data: VersionCheckResponse {
                            version: "1.0.0".to_string(),
                            update_description: vec!["初始版本".to_string()],
                        },
                        message: "成功".to_string(),
                    });
                },
                MySqlAppVersionServiceError::DatabaseError(_) => ErrorCode::DatabaseError,
                _ => ErrorCode::SystemError,
            };

            HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                data: VersionCheckResponse {
                    version: "".to_string(),
                    update_description: vec![],
                },
                message: format!("{}", error_code),
            })
        }
    }
}
