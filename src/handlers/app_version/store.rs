use actix_web::{web, HttpResponse, Responder};
use log::{info, error};

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode};
use crate::dto::app_version::{VersionStoreRequest, VersionStoreResponse};
use crate::services::mysql::MySqlAppVersionServiceError;

/**
 * 版本存储处理函数
 * 
 * POST /app/version/store
 * 
 * @param data 应用状态数据
 * @param version_data 版本存储请求数据
 * @return HTTP 响应
 */
pub async fn handle(
    data: web::Data<AppState>, 
    version_data: web::Json<VersionStoreRequest>
) -> impl Responder {
    info!("处理版本存储请求: {}", version_data.version);

    // 校验参数
    if version_data.version.trim().is_empty() {
        let error_code = ErrorCode::InvalidParameter;
        return HttpResponse::BadRequest().json(ApiResponse {
            code: error_code.code(),
            data: VersionStoreResponse {
                version_id: 0,
                version: "".to_string(),
                is_current: false,
            },
            message: "版本号不能为空".to_string(),
        });
    }

    if version_data.update_description.is_empty() {
        let error_code = ErrorCode::InvalidParameter;
        return HttpResponse::BadRequest().json(ApiResponse {
            code: error_code.code(),
            data: VersionStoreResponse {
                version_id: 0,
                version: "".to_string(),
                is_current: false,
            },
            message: "更新描述不能为空".to_string(),
        });
    }

    // 获取MySQL应用版本服务
    let mysql_app_version_service = match &data.mysql_app_version_service {
        Some(service) => service,
        None => {
            error!("MySQL应用版本服务未初始化");
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                data: VersionStoreResponse {
                    version_id: 0,
                    version: "".to_string(),
                    is_current: false,
                },
                message: error_code.message().to_string(),
            });
        }
    };

    // 存储版本信息
    match mysql_app_version_service.store_version(
        version_data.version.clone(),
        version_data.update_description.clone(),
        version_data.is_current,
    ).await {
        Ok(version) => {
            info!("版本存储成功: {} (ID: {})", version.version, version.id);
            
            let success_code = ErrorCode::Success;
            HttpResponse::Created().json(ApiResponse {
                code: success_code.code(),
                data: VersionStoreResponse {
                    version_id: version.id,
                    version: version.version,
                    is_current: version.is_current,
                },
                message: success_code.message().to_string(),
            })
        },
        Err(e) => {
            error!("版本存储失败: {}", e);
            
            let error_code = match e {
                MySqlAppVersionServiceError::VersionAlreadyExists => ErrorCode::DataAlreadyExists,
                MySqlAppVersionServiceError::DatabaseError(_) => ErrorCode::DatabaseError,
                _ => ErrorCode::SystemError,
            };

            HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                data: VersionStoreResponse {
                    version_id: 0,
                    version: "".to_string(),
                    is_current: false,
                },
                message: format!("{}", error_code),
            })
        }
    }
}
