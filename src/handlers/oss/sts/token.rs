use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{error, info};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::time::Instant;
use hmac::{Hmac, Mac};
use sha1::Sha1;
use base64::{Engine as _, engine::general_purpose};
use url::form_urlencoded;
use chrono::{DateTime, Utc};
use uuid::Uuid;

use crate::{
    AppState,
    dto::{ApiResponse, error_code::ErrorCode, sts::{StsCredentials, StsTokenResponse}},
    middleware::token_parser::get_user_id_from_request,
};

/// 阿里云STS服务响应
#[derive(Debug, Deserialize)]
struct AliyunStsResponse {
    #[serde(rename = "RequestId")]
    request_id: String,
    #[serde(rename = "Credentials")]
    credentials: AliyunStsCredentials,
}

/// 阿里云STS凭证
#[derive(Debug, Deserialize)]
struct AliyunStsCredentials {
    #[serde(rename = "AccessKeyId")]
    access_key_id: String,
    #[serde(rename = "AccessKeySecret")]
    access_key_secret: String,
    #[serde(rename = "SecurityToken")]
    security_token: String,
    #[serde(rename = "Expiration")]
    expiration: String,
}

/// 获取STS临时授权Token
pub async fn handle(
    req: HttpRequest,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 记录处理函数开始时间
    let handler_start_time = Instant::now();
    info!("开始处理STS Token请求");

    // 验证用户是否已登录
    if get_user_id_from_request(&req).is_none() {
        let error_code = ErrorCode::TokenInvalid;
        error!("用户未登录，STS Token请求被拒绝");
        return HttpResponse::Unauthorized().json(ApiResponse {
            code: error_code.code(),
            message: error_code.message().to_string(),
            data: Value::Null,
        });
    }

    // 获取OSS配置
    let oss_config = &app_state.config.oss;

    // 调用阿里云STS服务获取临时授权
    match get_sts_token(
        &oss_config.sts_access_key_id,
        &oss_config.sts_access_key_secret,
        &oss_config.role_arn,
    ).await {
        Ok(sts_response) => {
            let total_duration = handler_start_time.elapsed();
            info!("STS Token请求处理完成，总耗时: {:?}", total_duration);

            // 构造响应
            let response = StsTokenResponse {
                credentials: StsCredentials {
                    access_key_id: sts_response.credentials.access_key_id,
                    access_key_secret: sts_response.credentials.access_key_secret,
                    security_token: sts_response.credentials.security_token,
                    expiration: sts_response.credentials.expiration,
                },
                request_id: sts_response.request_id,
                region: oss_config.region.clone(),
                bucket: oss_config.bucket.clone(),
            };

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "获取STS Token成功".to_string(),
                data: response,
            })
        },
        Err(e) => {
            let total_duration = handler_start_time.elapsed();
            error!("获取STS Token失败: {}, 耗时: {:?}", e, total_duration);

            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::SystemError.code(),
                message: format!("获取STS Token失败: {}", e),
                data: Value::Null,
            })
        }
    }
}

/// 调用阿里云STS服务获取临时授权Token
async fn get_sts_token(
    access_key_id: &str,
    access_key_secret: &str,
    role_arn: &str,
) -> Result<AliyunStsResponse, String> {
    // 生成会话名称，使用UUID确保唯一性
    let session_name = format!("sts-session-{}", Uuid::new_v4().to_string().split('-').next().unwrap_or("default"));

    // 设置请求参数
    let mut params = vec![
        ("Action", "AssumeRole"),
        ("Format", "JSON"),
        ("Version", "2015-04-01"),
        ("SignatureMethod", "HMAC-SHA1"),
        ("SignatureVersion", "1.0"),
        ("RoleArn", role_arn),
        ("RoleSessionName", &session_name),
        ("DurationSeconds", "900"), // 15分钟有效期（阿里云STS服务要求最小值为15分钟）
        ("AccessKeyId", access_key_id),
    ];

    // 添加时间戳
    let now: DateTime<Utc> = Utc::now();
    let timestamp = now.format("%Y-%m-%dT%H:%M:%SZ").to_string();
    params.push(("Timestamp", &timestamp));

    // 添加随机数，防止重放攻击
    let nonce = Uuid::new_v4().to_string();
    params.push(("SignatureNonce", &nonce));

    // 按照参数名称的字典顺序排序
    params.sort_by(|a, b| a.0.cmp(b.0));

    // 构造规范化请求字符串
    let canonical_query_string = params
        .iter()
        .map(|(k, v)| {
            format!(
                "{}={}",
                url_encode(k),
                url_encode(v)
            )
        })
        .collect::<Vec<String>>()
        .join("&");

    // 构造待签名字符串
    let string_to_sign = format!(
        "GET&{}&{}",
        url_encode("/"),
        url_encode(&canonical_query_string)
    );

    // 计算签名
    let signing_key = format!("{}&", access_key_secret);
    let signature = hmac_sha1(&signing_key, &string_to_sign)?;

    // 将签名添加到参数中
    params.push(("Signature", &signature));

    // 构造最终的请求URL
    let query_string = params
        .iter()
        .map(|(k, v)| {
            format!(
                "{}={}",
                url_encode(k),
                url_encode(v)
            )
        })
        .collect::<Vec<String>>()
        .join("&");

    let url = format!("https://sts.aliyuncs.com/?{}", query_string);

    // 发送HTTP请求
    let client = Client::new();
    let response = client
        .get(&url)
        .send()
        .await
        .map_err(|e| format!("请求STS服务失败: {}", e))?;

    // 检查HTTP状态码
    if !response.status().is_success() {
        let status = response.status();
        let error_text = response.text().await.unwrap_or_else(|_| "无法获取错误详情".to_string());
        return Err(format!("STS服务返回错误状态码 {}: {}", status, error_text));
    }

    // 解析响应
    let sts_response = response
        .json::<AliyunStsResponse>()
        .await
        .map_err(|e| format!("解析STS响应失败: {}", e))?;

    Ok(sts_response)
}

/// URL编码
fn url_encode(s: &str) -> String {
    form_urlencoded::byte_serialize(s.as_bytes())
        .collect::<String>()
        .replace("+", "%20")
        .replace("*", "%2A")
        .replace("%7E", "~")
}

/// 使用HMAC-SHA1算法计算签名
fn hmac_sha1(key: &str, data: &str) -> Result<String, String> {
    type HmacSha1 = Hmac<Sha1>;

    let mut mac = HmacSha1::new_from_slice(key.as_bytes())
        .map_err(|e| format!("HMAC初始化失败: {}", e))?;

    mac.update(data.as_bytes());
    let result = mac.finalize().into_bytes();

    // Base64编码
    Ok(general_purpose::STANDARD.encode(result))
}
