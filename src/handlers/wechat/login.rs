use actix_web::{web, HttpResponse, Responder};
use log::{info, error, debug, warn};
use uuid::Uuid;
use std::time::{SystemTime, UNIX_EPOCH};
use redis::RedisError;

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode, wechat::{WechatLoginRequest, WechatLoginData}};
use crate::services::mysql::MySqlUserServiceError;
use crate::services::wechat::WechatServiceError;

/**
 * 微信登录处理函数
 *
 * @param data 应用状态数据
 * @param login_data 微信登录请求数据
 * @return HTTP 响应
 */
pub async fn handle(data: web::Data<AppState>, login_data: web::Json<WechatLoginRequest>) -> impl Responder {
    // 1. 获取微信服务
    let wechat_service = match &data.wechat_service {
        Some(service) => service,
        None => {
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                data: serde_json::Value::Null,
                message: "微信服务不可用".to_string(),
            });
        }
    };

    // 2. 获取MySQL用户服务
    let mysql_user_service = match &data.mysql_user_service {
        Some(service) => service,
        None => {
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                data: serde_json::Value::Null,
                message: "用户服务不可用".to_string(),
            });
        }
    };

    // 3. 调用微信API获取访问令牌
    let token_response = match wechat_service.get_access_token(&login_data.code).await {
        Ok(response) => response,
        Err(e) => {
            error!("获取微信访问令牌失败: {}", e);
            let error_code = ErrorCode::ExternalServiceError;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                data: serde_json::Value::Null,
                message: format!("获取微信访问令牌失败: {}", e),
            });
        }
    };

    // 4. 检查必要的字段
    let access_token = match &token_response.access_token {
        Some(token) => token,
        None => {
            error!("微信返回的访问令牌为空");
            let error_code = ErrorCode::ExternalServiceError;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                data: serde_json::Value::Null,
                message: "微信返回的访问令牌为空".to_string(),
            });
        }
    };

    let openid = match &token_response.openid {
        Some(id) => id,
        None => {
            error!("微信返回的OpenID为空");
            let error_code = ErrorCode::ExternalServiceError;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                data: serde_json::Value::Null,
                message: "微信返回的OpenID为空".to_string(),
            });
        }
    };

    // 5. 检查unionid是否存在，如果存在则查询用户
    let unionid = token_response.unionid.clone();

    if let Some(unionid) = &unionid {
        debug!("获取到微信unionid: {}", unionid);

        // 查询用户是否存在
        match mysql_user_service.find_by_unionid(unionid).await {
            Ok(Some(user)) => {
                info!("通过unionid找到用户: {}", user.id);

                // 生成token
                let token = generate_token();
                debug!("生成token: {}", token);

                // 存储token到Redis
                if let Err(e) = store_token_in_redis(&data, &token, &user.id.to_string()).await {
                    error!("存储token到Redis失败: {}", e);
                    let error_code = ErrorCode::SystemError;
                    return HttpResponse::InternalServerError().json(ApiResponse {
                        code: error_code.code(),
                        data: serde_json::Value::Null,
                        message: "登录失败，请稍后重试".to_string(),
                    });
                }

                // 返回登录成功响应
                let success_code = ErrorCode::Success;
                return HttpResponse::Ok().json(ApiResponse {
                    code: success_code.code(),
                    data: WechatLoginData {
                        user_id: user.id.to_string(),
                        phone: Some(user.phone),
                        nickname: user.nickname,
                        avatar: user.avatar,
                        token,
                    },
                    message: success_code.message().to_string(),
                });
            },
            Ok(None) => {
                info!("未找到unionid对应的用户，将获取用户信息并注册");
            },
            Err(e) => {
                error!("查询用户失败: {}", e);
                let error_code = ErrorCode::DatabaseError;
                return HttpResponse::InternalServerError().json(ApiResponse {
                    code: error_code.code(),
                    data: serde_json::Value::Null,
                    message: format!("查询用户失败: {}", e),
                });
            }
        }
    }

    // 6. 如果用户不存在，获取用户信息并注册
    let user_info = match wechat_service.get_user_info(access_token, openid).await {
        Ok(info) => info,
        Err(e) => {
            error!("获取微信用户信息失败: {}", e);
            let error_code = ErrorCode::ExternalServiceError;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                data: serde_json::Value::Null,
                message: format!("获取微信用户信息失败: {}", e),
            });
        }
    };

    // 7. 检查unionid是否存在
    let unionid = match user_info.unionid {
        Some(id) => id,
        None => {
            error!("微信返回的用户信息中unionid为空");
            let error_code = ErrorCode::ExternalServiceError;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                data: serde_json::Value::Null,
                message: "微信返回的用户信息中unionid为空".to_string(),
            });
        }
    };

    // 8. 注册新用户
    let nickname = user_info.nickname.unwrap_or_else(|| "微信用户".to_string());
    let avatar = user_info.headimgurl.clone();
    let openid = user_info.openid.clone();
    let sex = user_info.sex.map(|s| s as i8);
    let province = user_info.province.clone();
    let city = user_info.city.clone();
    let country = user_info.country.clone();
    let privilege = user_info.privilege.map(|p| serde_json::Value::Array(
        p.into_iter().map(|s| serde_json::Value::String(s)).collect()
    ));

    // 生成随机密码（用户无需知道，因为后续使用微信登录）
    let password = generate_random_password();

    // 生成随机手机号前缀（实际项目中可能需要让用户绑定手机号）
    let phone = format!("wx{}", unionid.chars().take(9).collect::<String>());

    // 使用简化版注册函数，保持兼容性
    match mysql_user_service.register_wechat_user(phone, password, unionid.clone(), nickname.clone(), avatar.clone()).await {
        Ok(user) => {
            info!("微信用户注册成功: {}", user.id);

            // 生成token
            let token = generate_token();
            debug!("生成token: {}", token);

            // 存储token到Redis
            if let Err(e) = store_token_in_redis(&data, &token, &user.id.to_string()).await {
                error!("存储token到Redis失败: {}", e);
                let error_code = ErrorCode::SystemError;
                return HttpResponse::InternalServerError().json(ApiResponse {
                    code: error_code.code(),
                    data: serde_json::Value::Null,
                    message: "登录失败，请稍后重试".to_string(),
                });
            }

            // 为新注册用户创建默认收藏夹
            create_default_favorite(&data, user.id).await;

            // 返回登录成功响应
            let success_code = ErrorCode::Success;
            HttpResponse::Created().json(ApiResponse {
                code: success_code.code(),
                data: WechatLoginData {
                    user_id: user.id.to_string(),
                    phone: Some(user.phone),
                    nickname: user.nickname,
                    avatar: user.avatar,
                    token,
                },
                message: success_code.message().to_string(),
            })
        },
        Err(e) => {
            error!("微信用户注册失败: {}", e);
            let error_code = match e {
                MySqlUserServiceError::UserAlreadyExists => ErrorCode::UserAlreadyExists,
                MySqlUserServiceError::DatabaseError(_) => ErrorCode::DatabaseError,
                _ => ErrorCode::SystemError,
            };

            HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                data: serde_json::Value::Null,
                message: format!("微信用户注册失败: {}", e),
            })
        }
    }
}

/// 生成唯一的 token
fn generate_token() -> String {
    // 使用 UUID v4 生成随机 token
    let uuid = Uuid::new_v4();
    // 添加时间戳以确保唯一性
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();

    format!("{}-{}", uuid, timestamp)
}

/// 将 token 存储到 Redis 中
async fn store_token_in_redis(app_state: &web::Data<AppState>, token: &str, user_id: &str) -> Result<(), RedisError> {
    // 获取 Redis 客户端
    if let Some(db) = &app_state.db {
        // 设置 token 到 Redis，有效期为 7 天（604800 秒）
        db.redis.set_ex(token, user_id, 604800)
    } else {
        Err(RedisError::from((redis::ErrorKind::IoError, "Redis connection not available")))
    }
}

/// 生成随机密码
fn generate_random_password() -> String {
    Uuid::new_v4().to_string().replace("-", "").chars().take(12).collect()
}

/// 为新注册用户创建默认收藏夹
async fn create_default_favorite(app_state: &web::Data<AppState>, user_id: u64) {
    // 获取MySQL收藏夹服务
    let mysql_favorite_service = match &app_state.mysql_favorite_service {
        Some(service) => service,
        None => {
            warn!("MySQL收藏夹服务未初始化，无法创建默认收藏夹");
            return;
        }
    };

    // 创建默认收藏夹
    match mysql_favorite_service
        .create_favorite(
            user_id,
            "默认收藏夹".to_string(),
            "".to_string(), // 空封面，将由第一个书签自动设置
        )
        .await
    {
        Ok(favorite) => {
            info!("为用户 {} 创建默认收藏夹成功，收藏夹ID: {}", user_id, favorite.id);
        }
        Err(e) => {
            error!("为用户 {} 创建默认收藏夹失败: {}", user_id, e);
            // 这里不影响用户注册流程，只记录错误日志
        }
    }
}
