use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};
use serde::Deserialize;

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode, prompt::PromptResponse};
use crate::services::mysql::{MySqlPromptService, MySqlPromptServiceError};
use crate::middleware::token_parser::get_user_id_from_request;

/// 获取提示词详情请求参数
#[derive(Debug, Deserialize)]
pub struct PromptDetailParams {
    /// 提示词ID
    pub id: String,
}

/**
 * 获取提示词详情处理函数
 *
 * 此函数处理GET /prompt/detail请求，用于获取指定提示词的详细信息
 * 只有提示词的创建者才能查看私有提示词
 *
 * @param req HTTP请求
 * @param app_state 应用状态
 * @param params 查询参数
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    app_state: web::Data<AppState>,
    params: web::Query<PromptDetailParams>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式错误: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidUserId.code(),
                message: "用户ID格式错误".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 解析提示词ID
    let prompt_id = match params.id.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("提示词ID格式错误: {}", params.id);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidInput.code(),
                message: "提示词ID格式错误".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 创建MySQL提示词服务
    let db_connections = app_state.db.as_ref().ok_or_else(|| {
        error!("数据库连接不可用");
        HttpResponse::InternalServerError().json(ApiResponse {
            code: ErrorCode::ServiceUnavailable.code(),
            message: "服务不可用，请稍后再试".to_string(),
            data: serde_json::Value::Null,
        })
    });

    let db_connections = match db_connections {
        Ok(db) => db,
        Err(response) => return response,
    };

    let mysql_prompt_service = match MySqlPromptService::new(db_connections, &app_state.config) {
        Ok(service) => service,
        Err(e) => {
            error!("创建MySQL提示词服务失败: {}", e);
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务不可用，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    info!("用户 {} 获取提示词详情: {}", user_id, prompt_id);

    // 获取提示词详情
    match mysql_prompt_service.get_prompt_by_id(prompt_id).await {
        Ok(Some(prompt)) => {
            // 检查权限：只有创建者可以查看私有提示词
            if prompt.is_public == 0 && prompt.user_id != user_id {
                error!("用户 {} 无权访问私有提示词: {}", user_id, prompt_id);
                return HttpResponse::Forbidden().json(ApiResponse {
                    code: ErrorCode::AccessDenied.code(),
                    message: "无权访问此提示词".to_string(),
                    data: serde_json::Value::Null,
                });
            }

            // 如果是查看自己的提示词，增加使用次数
            if prompt.user_id == user_id {
                if let Err(e) = mysql_prompt_service.increment_usage_count(prompt_id).await {
                    error!("增加提示词使用次数失败: {}", e);
                    // 不影响主要功能，只记录错误
                }
            }

            // 转换为响应格式
            let prompt_response = PromptResponse::from_mysql_model(&prompt);

            info!("获取提示词详情成功: {}", prompt_id);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "获取提示词详情成功".to_string(),
                data: prompt_response,
            })
        }
        Ok(None) => {
            error!("提示词不存在: {}", prompt_id);
            HttpResponse::NotFound().json(ApiResponse {
                code: ErrorCode::DataNotFound.code(),
                message: "提示词不存在".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(MySqlPromptServiceError::DatabaseError(e)) => {
            error!("数据库查询错误: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(e) => {
            error!("获取提示词详情失败: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}
