use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode, prompt::{CreatePromptRequest, CreatePromptResponse, PromptResponse}};
use crate::services::mysql::{MySqlPromptService, MySqlPromptServiceError};
use crate::middleware::token_parser::get_user_id_from_request;

/**
 * 创建提示词处理函数
 *
 * 此函数处理POST /prompt/create请求，用于创建新的提示词
 * 业务规则：
 * 1. 每个用户最多创建100个提示词
 * 2. 提示词标题不能为空且不超过255字符
 * 3. 提示词内容不能为空
 * 4. 同一个用户不能有同名的提示词
 *
 * @param req HTTP请求
 * @param app_state 应用状态
 * @param prompt_data 创建提示词请求数据
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    app_state: web::Data<AppState>,
    prompt_data: web::Json<CreatePromptRequest>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式错误: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidUserId.code(),
                message: "用户ID格式错误".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 创建MySQL提示词服务
    let db_connections = app_state.db.as_ref().ok_or_else(|| {
        error!("数据库连接不可用");
        HttpResponse::InternalServerError().json(ApiResponse {
            code: ErrorCode::ServiceUnavailable.code(),
            message: "服务不可用，请稍后再试".to_string(),
            data: serde_json::Value::Null,
        })
    });

    let db_connections = match db_connections {
        Ok(db) => db,
        Err(response) => return response,
    };

    let mysql_prompt_service = match MySqlPromptService::new(db_connections, &app_state.config) {
        Ok(service) => service,
        Err(e) => {
            error!("创建MySQL提示词服务失败: {}", e);
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务不可用，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    info!("用户 {} 尝试创建提示词: {}", user_id, prompt_data.title);

    // 创建提示词
    match mysql_prompt_service.create_prompt(
        user_id,
        prompt_data.title.clone(),
        prompt_data.content.clone(),
        prompt_data.category.clone(),
        prompt_data.tags.clone(),
        prompt_data.is_public,
    ).await {
        Ok(prompt) => {
            // 转换为响应格式
            let prompt_response = PromptResponse::from_mysql_model(&prompt);

            info!("用户 {} 创建提示词成功: {}", user_id, prompt.title);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "创建提示词成功".to_string(),
                data: CreatePromptResponse {
                    prompt: prompt_response,
                },
            })
        }
        Err(MySqlPromptServiceError::InvalidPromptTitle) => {
            error!("提示词标题无效: {}", prompt_data.title);
            HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidInput.code(),
                message: "提示词标题不能为空且不能超过255字符".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(MySqlPromptServiceError::InvalidPromptContent) => {
            error!("提示词内容无效");
            HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidInput.code(),
                message: "提示词内容不能为空".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(MySqlPromptServiceError::PromptAlreadyExists) => {
            error!("提示词已存在: {}", prompt_data.title);
            HttpResponse::Conflict().json(ApiResponse {
                code: ErrorCode::DuplicateData.code(),
                message: "提示词标题已存在".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(MySqlPromptServiceError::PromptLimitExceeded) => {
            error!("用户 {} 提示词数量已达上限", user_id);
            HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::LimitExceeded.code(),
                message: "提示词数量已达上限，每个用户最多创建100个提示词".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(MySqlPromptServiceError::DatabaseError(e)) => {
            error!("数据库创建错误: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(e) => {
            error!("创建提示词失败: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}
