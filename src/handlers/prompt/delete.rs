use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode, prompt::{DeletePromptRequest, DeletePromptResponse}};
use crate::services::mysql::{MySqlPromptService, MySqlPromptServiceError};
use crate::middleware::token_parser::get_user_id_from_request;

/**
 * 删除提示词处理函数
 *
 * 此函数处理POST /prompt/delete请求，用于删除指定的提示词
 * 只有提示词的创建者才能删除提示词
 *
 * @param req HTTP请求
 * @param app_state 应用状态
 * @param prompt_data 删除提示词请求数据
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    app_state: web::Data<AppState>,
    prompt_data: web::Json<DeletePromptRequest>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式错误: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidUserId.code(),
                message: "用户ID格式错误".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 解析提示词ID
    let prompt_id = match prompt_data.id.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("提示词ID格式错误: {}", prompt_data.id);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidInput.code(),
                message: "提示词ID格式错误".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 创建MySQL提示词服务
    let db_connections = app_state.db.as_ref().ok_or_else(|| {
        error!("数据库连接不可用");
        HttpResponse::InternalServerError().json(ApiResponse {
            code: ErrorCode::ServiceUnavailable.code(),
            message: "服务不可用，请稍后再试".to_string(),
            data: serde_json::Value::Null,
        })
    });

    let db_connections = match db_connections {
        Ok(db) => db,
        Err(response) => return response,
    };

    let mysql_prompt_service = match MySqlPromptService::new(db_connections, &app_state.config) {
        Ok(service) => service,
        Err(e) => {
            error!("创建MySQL提示词服务失败: {}", e);
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务不可用，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 首先检查提示词是否存在以及权限
    match mysql_prompt_service.get_prompt_by_id(prompt_id).await {
        Ok(Some(existing_prompt)) => {
            // 检查权限：只有创建者可以删除提示词
            if existing_prompt.user_id != user_id {
                error!("用户 {} 无权删除提示词: {}", user_id, prompt_id);
                return HttpResponse::Forbidden().json(ApiResponse {
                    code: ErrorCode::AccessDenied.code(),
                    message: "无权删除此提示词".to_string(),
                    data: serde_json::Value::Null,
                });
            }
        }
        Ok(None) => {
            error!("提示词不存在: {}", prompt_id);
            return HttpResponse::NotFound().json(ApiResponse {
                code: ErrorCode::DataNotFound.code(),
                message: "提示词不存在".to_string(),
                data: serde_json::Value::Null,
            });
        }
        Err(e) => {
            error!("查询提示词失败: {}", e);
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            });
        }
    }

    info!("用户 {} 尝试删除提示词: {}", user_id, prompt_id);

    // 删除提示词
    match mysql_prompt_service.delete_prompt(prompt_id).await {
        Ok(_) => {
            info!("用户 {} 删除提示词成功: {}", user_id, prompt_id);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "删除提示词成功".to_string(),
                data: DeletePromptResponse {
                    id: prompt_data.id.clone(),
                },
            })
        }
        Err(MySqlPromptServiceError::PromptNotFound) => {
            error!("提示词不存在: {}", prompt_id);
            HttpResponse::NotFound().json(ApiResponse {
                code: ErrorCode::DataNotFound.code(),
                message: "提示词不存在".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(MySqlPromptServiceError::DatabaseError(e)) => {
            error!("数据库删除错误: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(e) => {
            error!("删除提示词失败: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}
