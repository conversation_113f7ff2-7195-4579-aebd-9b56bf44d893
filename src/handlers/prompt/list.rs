use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode, prompt::{PromptListParams, PromptListResponse, PromptResponse}};
use crate::services::mysql::{MySqlPromptService, MySqlPromptServiceError};
use crate::middleware::token_parser::get_user_id_from_request;

/**
 * 获取提示词列表处理函数
 *
 * 此函数处理GET /prompt/list请求，用于获取用户的提示词列表
 * 支持分页查询和分类过滤
 *
 * @param req HTTP请求
 * @param app_state 应用状态
 * @param params 查询参数
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    app_state: web::Data<AppState>,
    mut params: web::Query<PromptListParams>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式错误: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidUserId.code(),
                message: "用户ID格式错误".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 验证并调整查询参数
    params.validate();

    // 创建MySQL提示词服务
    let db_connections = app_state.db.as_ref().ok_or_else(|| {
        error!("数据库连接不可用");
        HttpResponse::InternalServerError().json(ApiResponse {
            code: ErrorCode::ServiceUnavailable.code(),
            message: "服务不可用，请稍后再试".to_string(),
            data: serde_json::Value::Null,
        })
    });

    let db_connections = match db_connections {
        Ok(db) => db,
        Err(response) => return response,
    };

    let mysql_prompt_service = match MySqlPromptService::new(db_connections, &app_state.config) {
        Ok(service) => service,
        Err(e) => {
            error!("创建MySQL提示词服务失败: {}", e);
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务不可用，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    info!("用户 {} 获取提示词列表，页码: {}, 每页大小: {}", user_id, params.page, params.page_size);

    // 获取提示词列表和总数
    let prompts_result = mysql_prompt_service.get_user_prompts(user_id, params.page, params.page_size).await;
    let total_result = mysql_prompt_service.get_user_prompt_count(user_id).await;

    match (prompts_result, total_result) {
        (Ok(prompts), Ok(total)) => {
            // 转换为响应格式
            let prompt_responses: Vec<PromptResponse> = prompts
                .into_iter()
                .map(|prompt| PromptResponse::from_mysql_model(&prompt))
                .collect();

            let response = PromptListResponse::new(
                prompt_responses,
                total,
                params.page,
                params.page_size,
            );

            info!("获取用户 {} 的提示词列表成功，共 {} 个", user_id, total);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "获取提示词列表成功".to_string(),
                data: response,
            })
        }
        (Err(e), _) | (_, Err(e)) => {
            error!("获取提示词列表失败: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}
