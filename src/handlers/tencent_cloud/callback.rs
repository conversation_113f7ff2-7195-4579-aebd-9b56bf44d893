use actix_web::{web, HttpResponse, Responder, HttpRequest};
use log::{error, info, warn};
use std::collections::HashMap;
use futures_util::StreamExt;

use crate::{
    AppState,
    dto::tencent_cloud::TencentAsrCallbackRequest,
    services::mysql::MySqlTencentAsrTaskService,
    models::mysql::{AsrTaskStatus, TaskStatus},
};

/// 处理腾讯云语音识别回调
///
/// 腾讯云会通过POST方法发送回调，Content-Type为application/x-www-form-urlencoded
/// 所有内容都在Body中，需要解析form数据
///
/// 为了避免腾讯云重试，先立即返回成功响应，然后异步处理后续操作
pub async fn handle_asr_callback(
    req: HttpRequest,
    app_state: web::Data<AppState>,
    mut payload: web::Payload,
) -> impl Responder {
    info!("收到腾讯云语音识别回调请求");

    // 记录Content-Type
    if let Some(content_type) = req.headers().get("content-type") {
        info!("Content-Type: {:?}", content_type);
    }

    // 读取原始请求体
    let mut body = web::BytesMut::new();
    while let Some(chunk) = payload.next().await {
        match chunk {
            Ok(data) => {
                body.extend_from_slice(&data);

                // 防止请求体过大
                if body.len() > 10 * 1024 * 1024 { // 10MB限制
                    error!("请求体过大: {} 字节", body.len());
                    return HttpResponse::PayloadTooLarge().finish();
                }
            }
            Err(e) => {
                error!("读取请求体失败: {}", e);
                return HttpResponse::BadRequest().finish();
            }
        }
    }

    let body_str = match String::from_utf8(body.to_vec()) {
        Ok(s) => s,
        Err(e) => {
            error!("请求体不是有效的UTF-8字符串: {}", e);
            return HttpResponse::BadRequest().finish();
        }
    };

    info!("收到请求体，长度: {} 字节", body_str.len());

    // 解析URL编码的表单数据
    let form_data = match parse_url_encoded_data(&body_str) {
        Ok(data) => data,
        Err(e) => {
            error!("解析URL编码数据失败: {}", e);
            return HttpResponse::BadRequest().finish();
        }
    };

    info!("表单数据解析成功，包含 {} 个字段", form_data.len());

    // 解析表单数据为结构化对象
    let callback_data = match parse_form_data(&form_data) {
        Ok(data) => data,
        Err(e) => {
            error!("解析回调数据失败: {}", e);
            return HttpResponse::BadRequest().finish();
        }
    };

    // 立即返回成功响应给腾讯云，避免重试
    info!("回调数据解析成功，立即返回成功响应给腾讯云");

    // 异步处理回调数据，不阻塞响应
    let app_state_clone = app_state.clone();
    tokio::spawn(async move {
        if let Err(e) = process_callback_data(&callback_data, &app_state_clone).await {
            error!("异步处理回调数据失败: {}", e);
        }
    });

    // 立即返回空响应体给腾讯云
    HttpResponse::Ok().finish()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_get_error_message_by_code() {
        assert_eq!(get_error_message_by_code(10000), "转码失败，请确认音频格式是否符合标准");
        assert_eq!(get_error_message_by_code(10001), "识别失败");
        assert_eq!(get_error_message_by_code(10002), "语音时长太短");
        assert_eq!(get_error_message_by_code(10003), "语音时长太长");
        assert_eq!(get_error_message_by_code(10004), "无效的语音文件");
        assert_eq!(get_error_message_by_code(10005), "其他失败");
        assert_eq!(get_error_message_by_code(10006), "音轨个数不匹配");
        assert_eq!(get_error_message_by_code(10007), "音频下载失败");
        assert_eq!(get_error_message_by_code(99999), "未知错误，错误码: 99999");
    }
}

/// 解析URL编码的数据
fn parse_url_encoded_data(body: &str) -> Result<HashMap<String, String>, String> {
    let mut form_data = HashMap::new();

    for pair in body.split('&') {
        if let Some(eq_pos) = pair.find('=') {
            let key = &pair[..eq_pos];
            let value = &pair[eq_pos + 1..];

            // URL解码
            let decoded_key = urlencoding::decode(key)
                .map_err(|e| format!("解码key失败: {}", e))?
                .to_string();
            let decoded_value = urlencoding::decode(value)
                .map_err(|e| format!("解码value失败: {}", e))?
                .to_string();

            form_data.insert(decoded_key, decoded_value);
        } else if !pair.is_empty() {
            // 没有等号的情况，作为key处理，value为空
            let decoded_key = urlencoding::decode(pair)
                .map_err(|e| format!("解码key失败: {}", e))?
                .to_string();
            form_data.insert(decoded_key, String::new());
        }
    }

    Ok(form_data)
}

/// 解析表单数据为结构化对象
fn parse_form_data(form_data: &HashMap<String, String>) -> Result<TencentAsrCallbackRequest, String> {
    // 解析必需字段
    let code = form_data.get("code")
        .ok_or("缺少code字段")?
        .parse::<i64>()
        .map_err(|e| format!("code字段格式错误: {}", e))?;
    
    let request_id = form_data.get("requestId")
        .ok_or("缺少requestId字段")?
        .parse::<u64>()
        .map_err(|e| format!("requestId字段格式错误: {}", e))?;
    
    let appid = form_data.get("appid")
        .ok_or("缺少appid字段")?
        .parse::<u64>()
        .map_err(|e| format!("appid字段格式错误: {}", e))?;
    
    let projectid = form_data.get("projectid")
        .ok_or("缺少projectid字段")?
        .parse::<i64>()
        .map_err(|e| format!("projectid字段格式错误: {}", e))?;
    
    // 解析可选字段
    let message = form_data.get("message").cloned();
    let audio_url = form_data.get("audioUrl").cloned();
    let text = form_data.get("text").cloned();
    let result_detail = form_data.get("resultDetail").cloned();
    
    let audio_time = if let Some(time_str) = form_data.get("audioTime") {
        Some(time_str.parse::<f64>()
            .map_err(|e| format!("audioTime字段格式错误: {}", e))?)
    } else {
        None
    };
    
    Ok(TencentAsrCallbackRequest {
        code,
        message,
        request_id,
        appid,
        projectid,
        audio_url,
        text,
        result_detail,
        audio_time,
    })
}

/// 根据错误码获取错误信息
pub fn get_error_message_by_code(code: i64) -> String {
    match code {
        10000 => "转码失败，请确认音频格式是否符合标准".to_string(),
        10001 => "识别失败".to_string(),
        10002 => "语音时长太短".to_string(),
        10003 => "语音时长太长".to_string(),
        10004 => "无效的语音文件".to_string(),
        10005 => "其他失败".to_string(),
        10006 => "音轨个数不匹配".to_string(),
        10007 => "音频下载失败".to_string(),
        _ => format!("未知错误，错误码: {}", code),
    }
}

/// 处理回调数据的业务逻辑
async fn process_callback_data(callback_data: &TencentAsrCallbackRequest, app_state: &web::Data<AppState>) -> Result<(), String> {
    // 获取MySQL连接池
    let mysql_pool = match &app_state.db {
        Some(db) => match &db.mysql {
            Some(mysql) => mysql.pool(),
            None => {
                warn!("MySQL连接不可用，无法更新任务状态");
                return Ok(()); // 不影响回调响应
            }
        },
        None => {
            warn!("数据库连接不可用，无法更新任务状态");
            return Ok(()); // 不影响回调响应
        }
    };

    let task_service = MySqlTencentAsrTaskService::new(mysql_pool.clone());

    // 检查任务状态
    if callback_data.code == 0 {
        info!("✅ 语音识别成功 - 任务ID: {}", callback_data.request_id);

        // 只输出识别的文本内容
        if let Some(ref text) = callback_data.text {
            info!("🎯 识别结果文本: {}", text);
        } else {
            warn!("⚠️  识别成功但没有返回文本内容");
        }

        // 更新数据库中的任务状态为成功
        match task_service.update_task_result(
            callback_data.request_id,
            AsrTaskStatus::Success,
            callback_data.text.clone()
        ).await {
            Ok(asr_task) => {
                info!("腾讯云ASR任务状态已更新为成功: task_id={}", callback_data.request_id);

                // 如果有关联的主任务，同时更新主任务的result字段
                if let Some(related_task_id) = asr_task.related_task_id {
                    if let Err(e) = update_main_task_result_and_cleanup_redis(app_state, mysql_pool, related_task_id, callback_data.text.clone(), TaskStatus::Completed).await {
                        error!("更新主任务结果失败: {:?}", e);
                    }
                }
            }
            Err(e) => {
                error!("更新腾讯云ASR任务状态失败: {:?}", e);
                // 不影响回调响应，只记录错误
            }
        }

    } else {
        warn!("❌ 语音识别失败 - 任务ID: {}, 错误码: {}", callback_data.request_id, callback_data.code);

        if let Some(ref message) = callback_data.message {
            warn!("错误信息: {}", message);
        }

        // 根据错误码获取标准化的错误信息
        let error_message = get_error_message_by_code(callback_data.code);
        warn!("标准化错误信息: {}", error_message);

        // 更新数据库中的任务状态为失败，使用标准化的错误信息
        match task_service.update_task_result(
            callback_data.request_id,
            AsrTaskStatus::Failed,
            Some(error_message.clone())
        ).await {
            Ok(asr_task) => {
                info!("腾讯云ASR任务状态已更新为失败: task_id={}", callback_data.request_id);

                // 如果有关联的主任务，同时更新主任务的result字段为失败状态
                if let Some(related_task_id) = asr_task.related_task_id {
                    if let Err(e) = update_main_task_result_and_cleanup_redis(app_state, mysql_pool, related_task_id, Some(error_message), TaskStatus::Failed).await {
                        error!("更新主任务结果失败: {:?}", e);
                    }
                }
            }
            Err(e) => {
                error!("更新腾讯云ASR任务状态失败: {:?}", e);
                // 不影响回调响应，只记录错误
            }
        }
    }

    Ok(())
}

/// 更新主任务表的result字段和状态，并清理Redis中的任务标记
async fn update_main_task_result_and_cleanup_redis(
    app_state: &web::Data<AppState>,
    mysql_pool: &sqlx::Pool<sqlx::MySql>,
    task_id: u64,
    result: Option<String>,
    status: TaskStatus,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    info!("更新主任务结果: task_id={}, status={:?}", task_id, status);

    // 首先查询任务信息，获取用户ID和任务类型
    let task_info = sqlx::query_as::<_, (u64, i8)>(
        "SELECT user_id, task_type FROM tasks WHERE id = ?"
    )
    .bind(task_id)
    .fetch_optional(mysql_pool)
    .await?;

    let now = chrono::Utc::now();

    // 更新主任务状态和结果
    sqlx::query(
        r#"
        UPDATE tasks
        SET result = ?, status = ?, update_time = ?
        WHERE id = ?
        "#
    )
    .bind(&result)
    .bind(i8::from(status))
    .bind(now)
    .bind(task_id)
    .execute(mysql_pool)
    .await?;

    info!("主任务结果更新成功: task_id={}", task_id);

    // 如果任务类型是创建笔记(3)或更新笔记(4)，需要清理Redis中的任务标记
    if let Some((user_id, task_type)) = task_info {
        if task_type == 3 || task_type == 4 { // TaskType::CreateNote = 3, TaskType::UpdateNote = 4
            info!("检测到笔记任务（类型{}），尝试清理Redis标记: user_id={}", task_type, user_id);

            if let Err(e) = cleanup_create_note_redis_flag(app_state, user_id).await {
                warn!("清理Redis笔记任务标记失败: {}", e);
                // 不影响主流程，只记录警告
            }
        }
    }

    Ok(())
}

/// 清理Redis中的创建笔记任务标记
async fn cleanup_create_note_redis_flag(
    app_state: &web::Data<AppState>,
    user_id: u64,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    if let Some(db) = &app_state.db {
        let key = format!("create_note_task:{}", user_id);

        // 检查key是否存在
        if db.redis.exists(&key)? {
            db.redis.del(&key)?;
            info!("Redis创建笔记标记清理成功: {}", key);
        } else {
            info!("Redis创建笔记标记不存在，无需清理: {}", key);
        }
    } else {
        return Err("Redis连接不可用".into());
    }

    Ok(())
}

/// 更新主任务表的result字段和状态（保留原函数以兼容其他地方的调用）
async fn update_main_task_result(
    mysql_pool: &sqlx::Pool<sqlx::MySql>,
    task_id: u64,
    result: Option<String>,
    status: TaskStatus,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    info!("更新主任务结果: task_id={}, status={:?}", task_id, status);

    let now = chrono::Utc::now();

    sqlx::query(
        r#"
        UPDATE tasks
        SET result = ?, status = ?, update_time = ?
        WHERE id = ?
        "#
    )
    .bind(&result)
    .bind(i8::from(status))
    .bind(now)
    .bind(task_id)
    .execute(mysql_pool)
    .await?;

    info!("主任务结果更新成功: task_id={}", task_id);
    Ok(())
}
