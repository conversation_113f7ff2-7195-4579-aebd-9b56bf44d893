use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{error, info};
use serde_json::Value;

use crate::dto::{ApiResponse, ErrorCode, NoteDraftListParams, NoteDraftListResponse, NoteDraftListItem};
use crate::middleware::token_parser::get_user_id_from_request;
use crate::services::mysql::MySqlNoteDraftServiceError;
use crate::AppState;

/**
 * 获取笔记草稿列表处理函数
 *
 * 此函数处理GET /note/draft/list请求，用于获取用户的草稿列表
 * 支持分页，返回草稿信息及对应笔记的基本信息
 *
 * 请求参数:
 * - page: 页码，从1开始，默认为1
 * - page_size: 每页数量，默认为10，最大100
 *
 * 返回数据格式:
 * {
 *   "code": 200,
 *   "message": "获取草稿列表成功",
 *   "data": {
 *     "drafts": [
 *       {
 *         "id": "草稿ID",
 *         "note_id": "笔记ID",
 *         "content": "草稿内容",
 *         "create_time": "创建时间",
 *         "update_time": "更新时间",
 *         "note_title": "笔记标题",
 *         "note_cover": "笔记封面",
 *         "note_desc": "笔记描述"
 *       }
 *     ],
 *     "total": 总数量,
 *     "page": 当前页码,
 *     "page_size": 每页数量
 *   }
 * }
 *
 * @param req HTTP请求
 * @param app_state 应用状态
 * @param query 查询参数
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    app_state: web::Data<AppState>,
    query: web::Query<NoteDraftListParams>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式错误: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "用户ID格式错误".to_string(),
                data: Value::Null,
            });
        }
    };

    // 验证分页参数
    if query.page < 1 {
        error!("页码必须大于0");
        return HttpResponse::BadRequest().json(ApiResponse {
            code: ErrorCode::InvalidParameter.code(),
            message: "页码必须大于0".to_string(),
            data: Value::Null,
        });
    }

    if query.page_size < 1 || query.page_size > 100 {
        error!("每页数量必须在1-100之间");
        return HttpResponse::BadRequest().json(ApiResponse {
            code: ErrorCode::InvalidParameter.code(),
            message: "每页数量必须在1-100之间".to_string(),
            data: Value::Null,
        });
    }

    // 获取MySQL笔记草稿服务
    let mysql_note_draft_service = match &app_state.mysql_note_draft_service {
        Some(service) => service,
        None => {
            error!("MySQL笔记草稿服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务暂时不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 获取MySQL笔记服务
    let mysql_note_service = match &app_state.mysql_note_service {
        Some(service) => service,
        None => {
            error!("MySQL笔记服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务暂时不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 获取草稿列表
    match mysql_note_draft_service.get_drafts_by_user_id(
        user_id,
        query.page,
        query.page_size,
    ).await {
        Ok((drafts, total)) => {
            info!(
                "获取用户 {} 的草稿列表成功，共 {} 条",
                user_id, total
            );

            // 构建响应数据，需要获取对应的笔记信息
            let mut draft_items = Vec::new();
            
            for draft in drafts {
                // 获取对应的笔记信息
                match mysql_note_service.get_note_by_id(draft.note_id).await {
                    Ok(note) => {
                        draft_items.push(NoteDraftListItem {
                            id: draft.id.to_string(),
                            note_id: draft.note_id.to_string(),
                            content: draft.content,
                            create_time: draft.create_time,
                            update_time: draft.update_time,
                            note_title: note.title,
                            note_cover: note.cover,
                            note_desc: note.desc,
                        });
                    }
                    Err(e) => {
                        error!("获取笔记信息失败，笔记ID: {}, 错误: {}", draft.note_id, e);
                        // 如果笔记不存在，使用默认值
                        draft_items.push(NoteDraftListItem {
                            id: draft.id.to_string(),
                            note_id: draft.note_id.to_string(),
                            content: draft.content,
                            create_time: draft.create_time,
                            update_time: draft.update_time,
                            note_title: "笔记不存在".to_string(),
                            note_cover: "".to_string(),
                            note_desc: "".to_string(),
                        });
                    }
                }
            }

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "获取草稿列表成功".to_string(),
                data: NoteDraftListResponse {
                    drafts: draft_items,
                    total,
                    page: query.page,
                    page_size: query.page_size,
                },
            })
        }
        Err(e) => {
            error!("获取草稿列表失败: {}", e);

            let (error_code, message) = match e {
                MySqlNoteDraftServiceError::DatabaseError(_) => {
                    (ErrorCode::DatabaseError, "服务错误，请稍后再试")
                }
                MySqlNoteDraftServiceError::DatabaseNotAvailable => {
                    (ErrorCode::ServiceUnavailable, "服务暂时不可用")
                }
                MySqlNoteDraftServiceError::InvalidParameter => {
                    (ErrorCode::InvalidParameter, "参数无效")
                }
                _ => {
                    (ErrorCode::SystemError, "系统错误")
                }
            };

            HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: message.to_string(),
                data: Value::Null,
            })
        }
    }
}
