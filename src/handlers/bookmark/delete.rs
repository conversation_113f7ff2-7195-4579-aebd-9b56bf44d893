use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{error, info};
use serde_json::Value;

use crate::{
    AppState,
    dto::{ApiResponse, DeleteBookmarkRequest, DeleteBookmarkResponse, ErrorCode},
    middleware::token_parser::get_user_id_from_request,
    services::mysql::MySqlBookmarkServiceError,
};

/**
 * 删除书签接口处理函数
 *
 * 此函数处理POST /bookmark/delete请求，用于删除一个书签
 *
 * @param req HTTP请求
 * @param payload 请求体，包含书签ID
 * @param app_state 应用状态
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    payload: web::Json<DeleteBookmarkRequest>,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 获取当前用户ID
    let _user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 获取提交的数据
    let bookmark_data = payload.into_inner();

    // 获取MySQL书签服务
    let mysql_bookmark_service = match &app_state.mysql_bookmark_service {
        Some(service) => service,
        None => {
            error!("MySQL书签服务未初始化");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 验证并转换书签ID
    let bookmark_id = match bookmark_data.id.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("无效的书签ID格式: {}", e);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "无效的书签ID格式".to_string(),
                data: Value::Null,
            });
        }
    };

    // 调用服务删除书签
    match mysql_bookmark_service.delete_bookmark(bookmark_id).await {
        Ok(()) => {
            let bookmark_id_str = bookmark_data.id.clone();

            info!("书签删除成功: {}", bookmark_id_str);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "书签删除成功".to_string(),
                data: DeleteBookmarkResponse {
                    id: bookmark_id_str,
                },
            })
        },
        Err(e) => {
            error!("删除书签失败: {}", e);

            match e {
                MySqlBookmarkServiceError::BookmarkNotFound => {
                    HttpResponse::NotFound().json(ApiResponse {
                        code: ErrorCode::SystemError.code(),
                        message: "书签不存在".to_string(),
                        data: Value::Null,
                    })
                },
                _ => {
                    HttpResponse::InternalServerError().json(ApiResponse {
                        code: ErrorCode::SystemError.code(),
                        message: "服务器内部错误".to_string(),
                        data: Value::Null,
                    })
                }
            }
        }
    }
}
