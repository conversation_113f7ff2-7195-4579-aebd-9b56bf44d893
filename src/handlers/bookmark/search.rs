use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

use crate::AppState;
use crate::dto::{ApiResponse, error_code::ErrorCode};
use crate::middleware::token_parser::get_user_id_from_request;
use crate::services::mysql::MySqlBookmarkServiceError;
use crate::enums::PlatformType;

/// 搜索书签请求
#[derive(Debug, Deserialize)]
pub struct SearchBookmarkRequest {
    /// 搜索关键词
    pub keyword: String,

    /// 页码，从1开始
    #[serde(default = "default_page")]
    pub page: u32,

    /// 每页数量
    #[serde(default = "default_page_size")]
    pub page_size: u32,
}

/// 默认页码
#[inline]
fn default_page() -> u32 {
    1
}

/// 默认每页数量
#[inline]
fn default_page_size() -> u32 {
    10
}

/// 书签项
#[derive(Debug, Serialize)]
pub struct BookmarkItem {
    /// 书签ID
    pub id: String,

    /// 博主名称
    pub influencer_name: String,

    /// 博主头像
    #[serde(skip_serializing_if = "Option::is_none")]
    pub influencer_avatar: Option<String>,

    /// 封面
    pub cover: String,

    /// 标题
    pub title: String,

    /// 简介
    pub desc: String,

    /// 原生跳转链接
    pub scheme_url: String,

    /// 收藏夹id
    pub parent_id: String,

    /// 创建时间
    pub create_time: DateTime<Utc>,

    /// 最后更新时间
    pub update_time: DateTime<Utc>,

    /// 标签列表
    pub tags: Vec<crate::dto::bookmark::TagInfo>,

    /// 平台类型（可选）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub platform_type: Option<PlatformType>,
}

/// 搜索书签响应
#[derive(Debug, Serialize)]
pub struct SearchBookmarkResponse {
    /// 书签列表
    pub bookmarks: Vec<BookmarkItem>,

    /// 总数
    pub total: u64,

    /// 当前页码
    pub page: u32,

    /// 每页数量
    pub page_size: u32,
}

/// 处理搜索书签请求
pub async fn handle(
    data: web::Data<AppState>,
    req: HttpRequest,
    query: web::Query<SearchBookmarkRequest>,
) -> impl Responder {
    // 从请求中获取用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("无法获取用户ID");
            let error_code = ErrorCode::TokenInvalid;
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 将字符串用户ID转换为 u64
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("无效的用户ID格式: {}", e);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidUserId.code(),
                message: ErrorCode::InvalidUserId.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取MySQL书签服务
    let mysql_bookmark_service = match &data.mysql_bookmark_service {
        Some(service) => service,
        None => {
            error!("MySQL书签服务未初始化");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务不可用".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取分页参数
    let page = query.page;
    let page_size = query.page_size;
    let keyword = &query.keyword;

    // 搜索书签
    match mysql_bookmark_service.search_bookmarks(user_id, keyword, page, page_size).await {
        Ok((bookmarks, total)) => {
            // 提取书签ID列表
            let bookmark_ids: Vec<u64> = bookmarks.iter().map(|b| b.id).collect();

            // 批量获取标签信息
            let bookmark_tags = match mysql_bookmark_service.get_tags_by_bookmark_ids(&bookmark_ids).await {
                Ok(tags) => tags,
                Err(e) => {
                    error!("获取书签标签失败: {}", e);
                    Vec::new() // 如果获取标签失败，返回空标签列表
                }
            };

            // 转换为响应格式
            let bookmark_items: Vec<BookmarkItem> = bookmarks.into_iter().map(|b| {
                // 为当前书签筛选标签
                let tags: Vec<crate::dto::bookmark::TagInfo> = bookmark_tags
                    .iter()
                    .filter(|tag| tag.bookmark_id == b.id)
                    .map(|tag| crate::dto::bookmark::TagInfo {
                        id: tag.tag_id.to_string(),
                        name: tag.tag_name.clone(),
                        background_color: tag.tag_background_color.clone(),
                        text_color: tag.tag_text_color.clone(),
                    })
                    .collect();

                BookmarkItem {
                    id: b.id.to_string(),
                    influencer_name: b.influencer_name,
                    influencer_avatar: b.influencer_avatar,
                    cover: b.cover,
                    title: b.title,
                    desc: b.desc,
                    scheme_url: b.scheme_url,
                    parent_id: b.favorite_id.to_string(), // MySQL中是favorite_id，映射到API的parent_id
                    create_time: b.created_at,            // MySQL中是created_at，映射到API的create_time
                    update_time: b.updated_at,            // MySQL中是updated_at，映射到API的update_time
                    tags,
                    platform_type: b.platform_type.clone(),
                }
            }).collect();

            info!("用户 {} 搜索书签成功，关键词: {}, 找到 {} 条结果", user_id_str, keyword, total);

            let success_code = ErrorCode::Success;
            HttpResponse::Ok().json(ApiResponse {
                code: success_code.code(),
                message: success_code.message().to_string(),
                data: SearchBookmarkResponse {
                    bookmarks: bookmark_items,
                    total,
                    page,
                    page_size,
                },
            })
        }
        Err(e) => {
            // 根据错误类型选择合适的错误码
            let error_code = match e {
                MySqlBookmarkServiceError::DatabaseError(_) => ErrorCode::DatabaseError,
                MySqlBookmarkServiceError::DatabaseNotAvailable => ErrorCode::ServiceUnavailable,
                MySqlBookmarkServiceError::FavoriteNotFound => ErrorCode::FavoriteNotFound,
                MySqlBookmarkServiceError::BookmarkNotFound => ErrorCode::InvalidParameter, // 使用通用的参数错误
                MySqlBookmarkServiceError::BookmarkAlreadyExists => ErrorCode::InvalidParameter, // 使用通用的参数错误
                MySqlBookmarkServiceError::FavoriteServiceError(_) => ErrorCode::FavoriteNotFound, // 使用收藏夹不存在错误
                MySqlBookmarkServiceError::TagServiceError(_) => ErrorCode::SystemError, // 标签服务错误
                MySqlBookmarkServiceError::TagNotFound => ErrorCode::InvalidParameter, // 标签不存在
            };

            error!("搜索书签失败: {}", e);

            HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}
