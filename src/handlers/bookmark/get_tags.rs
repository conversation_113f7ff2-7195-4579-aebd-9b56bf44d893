use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{error, info};
use serde_json::Value;

use crate::{
    AppState,
    dto::{ApiResponse, ErrorCode, GetBookmarkTagsParams, GetBookmarkTagsResponse},
    dto::bookmark::TagInfo,
    middleware::token_parser::get_user_id_from_request,
};

/**
 * 获取书签标签处理函数
 *
 * 此函数处理GET /bookmark/tags请求，用于获取指定书签的标签列表
 * 业务规则：
 * 1. 用户只能查看自己的书签标签
 *
 * @param req HTTP请求
 * @param app_state 应用状态
 * @param query 查询参数
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    app_state: web::Data<AppState>,
    query: web::Query<GetBookmarkTagsParams>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式无效: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidUserId.code(),
                message: "用户ID格式无效".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析书签ID
    let bookmark_id = match query.bookmark_id.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("书签ID格式无效: {}", query.bookmark_id);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "书签ID格式无效".to_string(),
                data: Value::Null,
            });
        }
    };

    // 获取MySQL书签服务
    let mysql_bookmark_service = match &app_state.mysql_bookmark_service {
        Some(service) => service,
        None => {
            error!("MySQL书签服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "书签服务不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 验证书签是否存在且属于当前用户
    match mysql_bookmark_service.get_bookmark_by_id(bookmark_id).await {
        Ok(Some(bookmark)) => {
            if bookmark.user_id != user_id {
                error!("用户 {} 尝试查看不属于自己的书签 {} 的标签", user_id, bookmark_id);
                return HttpResponse::Forbidden().json(ApiResponse {
                    code: ErrorCode::InvalidParameter.code(),
                    message: "无权限查看此书签".to_string(),
                    data: Value::Null,
                });
            }
        }
        Ok(None) => {
            error!("书签不存在: {}", bookmark_id);
            return HttpResponse::NotFound().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "书签不存在".to_string(),
                data: Value::Null,
            });
        }
        Err(e) => {
            error!("查询书签失败: {}", e);
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::SystemError.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: Value::Null,
            });
        }
    }

    // 获取书签的标签列表
    match mysql_bookmark_service.get_tags_by_bookmark_ids(&[bookmark_id]).await {
        Ok(bookmark_tags) => {
            // 转换为响应格式
            let tags: Vec<TagInfo> = bookmark_tags
                .into_iter()
                .filter(|tag| tag.bookmark_id == bookmark_id)
                .map(|tag| TagInfo {
                    id: tag.tag_id.to_string(),
                    name: tag.tag_name,
                    background_color: tag.tag_background_color,
                    text_color: tag.tag_text_color,
                })
                .collect();

            info!("用户 {} 成功获取书签 {} 的标签列表，共 {} 个", user_id, bookmark_id, tags.len());

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "获取书签标签成功".to_string(),
                data: GetBookmarkTagsResponse {
                    bookmark_id: query.bookmark_id.clone(),
                    tags,
                },
            })
        }
        Err(e) => {
            error!("获取书签标签失败: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::SystemError.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: Value::Null,
            })
        }
    }
}
