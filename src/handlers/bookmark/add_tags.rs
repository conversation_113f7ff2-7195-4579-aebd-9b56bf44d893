use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{error, info};
use serde_json::Value;

use crate::{
    AppState,
    dto::{ApiResponse, ErrorCode, AddBookmarkTagsRequest, AddBookmarkTagsResponse},
    middleware::token_parser::get_user_id_from_request,
};

/**
 * 给书签添加标签处理函数
 *
 * 此函数处理POST /bookmark/tags/add请求，用于给指定书签添加标签
 * 业务规则：
 * 1. 用户只能给自己的书签添加标签
 * 2. 如果标签不存在会自动创建
 * 3. 如果标签已经关联到书签，不会重复添加
 *
 * @param req HTTP请求
 * @param app_state 应用状态
 * @param tag_data 添加标签请求数据
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    app_state: web::Data<AppState>,
    tag_data: web::Json<AddBookmarkTagsRequest>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式无效: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidUserId.code(),
                message: "用户ID格式无效".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析书签ID
    let bookmark_id = match tag_data.bookmark_id.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("书签ID格式无效: {}", tag_data.bookmark_id);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "书签ID格式无效".to_string(),
                data: Value::Null,
            });
        }
    };

    // 验证标签名称列表
    if tag_data.tag_names.is_empty() {
        return HttpResponse::BadRequest().json(ApiResponse {
            code: ErrorCode::InvalidParameter.code(),
            message: "标签名称列表不能为空".to_string(),
            data: Value::Null,
        });
    }

    // 验证标签名称
    for tag_name in &tag_data.tag_names {
        if tag_name.trim().is_empty() {
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::TagNameEmpty.code(),
                message: "标签名称不能为空".to_string(),
                data: Value::Null,
            });
        }
        if tag_name.len() > 50 {
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::TagNameTooLong.code(),
                message: "标签名称不能超过50个字符".to_string(),
                data: Value::Null,
            });
        }
    }

    // 获取MySQL书签服务
    let mysql_bookmark_service = match &app_state.mysql_bookmark_service {
        Some(service) => service,
        None => {
            error!("MySQL书签服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "书签服务不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 验证书签是否存在且属于当前用户
    match mysql_bookmark_service.get_bookmark_by_id(bookmark_id).await {
        Ok(Some(bookmark)) => {
            if bookmark.user_id != user_id {
                error!("用户 {} 尝试给不属于自己的书签 {} 添加标签", user_id, bookmark_id);
                return HttpResponse::Forbidden().json(ApiResponse {
                    code: ErrorCode::InvalidParameter.code(),
                    message: "无权限操作此书签".to_string(),
                    data: Value::Null,
                });
            }
        }
        Ok(None) => {
            error!("书签不存在: {}", bookmark_id);
            return HttpResponse::NotFound().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "书签不存在".to_string(),
                data: Value::Null,
            });
        }
        Err(e) => {
            error!("查询书签失败: {}", e);
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::SystemError.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: Value::Null,
            });
        }
    }

    // 添加标签到书签
    match mysql_bookmark_service.add_tags_to_bookmark(
        bookmark_id,
        tag_data.tag_names.clone(),
        user_id,
    ).await {
        Ok(()) => {
            info!("用户 {} 成功给书签 {} 添加标签: {:?}", user_id, bookmark_id, tag_data.tag_names);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "添加标签成功".to_string(),
                data: AddBookmarkTagsResponse {
                    bookmark_id: tag_data.bookmark_id.clone(),
                    added_count: tag_data.tag_names.len(),
                },
            })
        }
        Err(e) => {
            error!("给书签添加标签失败: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::SystemError.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: Value::Null,
            })
        }
    }
}
