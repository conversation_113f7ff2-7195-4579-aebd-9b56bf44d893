use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{error, info};
use serde_json::Value;

use crate::{
    AppState,
    dto::{ApiResponse, ErrorCode, RemoveBookmarkTagsRequest, RemoveBookmarkTagsResponse},
    middleware::token_parser::get_user_id_from_request,
};

/**
 * 从书签移除标签处理函数
 *
 * 此函数处理POST /bookmark/tags/remove请求，用于从指定书签移除标签
 * 业务规则：
 * 1. 用户只能从自己的书签移除标签
 * 2. 如果标签不存在或未关联到书签，不会报错
 *
 * @param req HTTP请求
 * @param app_state 应用状态
 * @param tag_data 移除标签请求数据
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    app_state: web::Data<AppState>,
    tag_data: web::Json<RemoveBookmarkTagsRequest>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式无效: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidUserId.code(),
                message: "用户ID格式无效".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析书签ID
    let bookmark_id = match tag_data.bookmark_id.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("书签ID格式无效: {}", tag_data.bookmark_id);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "书签ID格式无效".to_string(),
                data: Value::Null,
            });
        }
    };

    // 验证标签ID列表
    if tag_data.tag_ids.is_empty() {
        return HttpResponse::BadRequest().json(ApiResponse {
            code: ErrorCode::InvalidParameter.code(),
            message: "标签ID列表不能为空".to_string(),
            data: Value::Null,
        });
    }

    // 解析标签ID列表
    let mut tag_ids = Vec::new();
    for tag_id_str in &tag_data.tag_ids {
        match tag_id_str.parse::<u64>() {
            Ok(tag_id) => tag_ids.push(tag_id),
            Err(_) => {
                error!("标签ID格式无效: {}", tag_id_str);
                return HttpResponse::BadRequest().json(ApiResponse {
                    code: ErrorCode::InvalidParameter.code(),
                    message: format!("标签ID格式无效: {}", tag_id_str),
                    data: Value::Null,
                });
            }
        }
    }

    // 获取MySQL书签服务
    let mysql_bookmark_service = match &app_state.mysql_bookmark_service {
        Some(service) => service,
        None => {
            error!("MySQL书签服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "书签服务不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 验证书签是否存在且属于当前用户
    match mysql_bookmark_service.get_bookmark_by_id(bookmark_id).await {
        Ok(Some(bookmark)) => {
            if bookmark.user_id != user_id {
                error!("用户 {} 尝试从不属于自己的书签 {} 移除标签", user_id, bookmark_id);
                return HttpResponse::Forbidden().json(ApiResponse {
                    code: ErrorCode::InvalidParameter.code(),
                    message: "无权限操作此书签".to_string(),
                    data: Value::Null,
                });
            }
        }
        Ok(None) => {
            error!("书签不存在: {}", bookmark_id);
            return HttpResponse::NotFound().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "书签不存在".to_string(),
                data: Value::Null,
            });
        }
        Err(e) => {
            error!("查询书签失败: {}", e);
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::SystemError.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: Value::Null,
            });
        }
    }

    // 从书签移除标签
    match mysql_bookmark_service.remove_tags_from_bookmark(bookmark_id, tag_ids).await {
        Ok(()) => {
            info!("用户 {} 成功从书签 {} 移除标签: {:?}", user_id, bookmark_id, tag_data.tag_ids);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "移除标签成功".to_string(),
                data: RemoveBookmarkTagsResponse {
                    bookmark_id: tag_data.bookmark_id.clone(),
                    removed_count: tag_data.tag_ids.len(),
                },
            })
        }
        Err(e) => {
            error!("从书签移除标签失败: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::SystemError.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: Value::Null,
            })
        }
    }
}
