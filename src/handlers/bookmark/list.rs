use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

use crate::AppState;
use crate::dto::{ApiResponse, error_code::ErrorCode, bookmark::TagInfo};
use crate::middleware::token_parser::get_user_id_from_request;
use crate::services::mysql::MySqlBookmarkServiceError;
use crate::enums::PlatformType;

/// 书签列表响应中的书签项
#[derive(Debug, Serialize)]
pub struct BookmarkItem {
    /// 书签ID
    pub id: String,

    /// 博主名称
    pub influencer_name: String,

    /// 博主头像，可选
    #[serde(skip_serializing_if = "Option::is_none")]
    pub influencer_avatar: Option<String>,

    /// 封面
    pub cover: String,

    /// 标题
    pub title: String,

    /// 简介
    pub desc: String,

    /// 原生跳转链接
    pub scheme_url: String,

    /// 收藏夹ID
    pub parent_id: String,

    /// 创建时间
    pub create_time: DateTime<Utc>,

    /// 最后更新时间
    pub update_time: DateTime<Utc>,

    /// 标签列表
    pub tags: Vec<crate::dto::bookmark::TagInfo>,

    /// 平台类型（可选）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub platform_type: Option<PlatformType>,
}

/// 获取书签列表请求参数
#[derive(Debug, Deserialize)]
pub struct BookmarkListParams {
    /// 收藏夹ID（可选）
    #[serde(default)]
    pub parent_id: Option<String>,

    /// 标签名称列表（可选，用于按标签搜索）
    #[serde(default)]
    pub tag_names: Option<Vec<String>>,

    /// 页码，从1开始
    #[serde(default = "default_page")]
    pub page: u32,

    /// 每页数量
    #[serde(default = "default_page_size")]
    pub page_size: u32,

    /// 平台类型过滤（可选）
    #[serde(default)]
    pub platform_type: Option<PlatformType>,
}

/// 默认页码
#[inline]
fn default_page() -> u32 {
    1
}

/// 默认每页数量
#[inline]
fn default_page_size() -> u32 {
    10
}

/// 书签列表响应
#[derive(Debug, Serialize)]
pub struct BookmarkListResponse {
    /// 书签列表
    pub bookmarks: Vec<BookmarkItem>,

    /// 书签总数
    pub total: u64,

    /// 当前页码
    pub page: u32,

    /// 每页数量
    pub page_size: u32,
}

/// 处理获取书签列表请求 (POST方法)
pub async fn handle(
    data: web::Data<AppState>,
    req: HttpRequest,
    payload: web::Json<BookmarkListParams>,
) -> impl Responder {
    handle_bookmark_list(data, req, payload.into_inner()).await
}

/// 处理获取书签列表请求 (GET方法)
pub async fn handle_get(
    data: web::Data<AppState>,
    req: HttpRequest,
    query: web::Query<BookmarkListParams>,
) -> impl Responder {
    handle_bookmark_list(data, req, query.into_inner()).await
}

/// 书签列表处理的核心逻辑
async fn handle_bookmark_list(
    data: web::Data<AppState>,
    req: HttpRequest,
    params: BookmarkListParams,
) -> impl Responder {
    // 从请求中获取用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("无法获取用户ID");
            let error_code = ErrorCode::TokenInvalid;
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 将字符串用户ID转换为 u64
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("无效的用户ID格式: {}", e);
            let error_code = ErrorCode::InvalidParameter;
            return HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: "无效的用户ID格式".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取MySQL书签服务
    let mysql_bookmark_service = match &data.mysql_bookmark_service {
        Some(service) => service,
        None => {
            error!("MySQL书签服务不可用");
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取分页参数
    let page = params.page;
    let page_size = params.page_size;

    // 检查是否有标签搜索参数
    let has_tag_search = params.tag_names.as_ref().map_or(false, |tags| !tags.is_empty());
    let has_parent_id = params.parent_id.is_some();
    let platform_filter = params.platform_type.clone();

    // 根据参数决定查询方式
    let result = match (has_parent_id, has_tag_search) {
        (true, true) => {
            // 同时传了收藏夹ID和标签：在指定收藏夹内按标签搜索
            let parent_id_str = params.parent_id.as_ref().unwrap();
            let favorite_id = match parent_id_str.parse::<u64>() {
                Ok(id) => id,
                Err(e) => {
                    error!("无效的收藏夹ID格式: {}", e);
                    let error_code = ErrorCode::InvalidParameter;
                    return HttpResponse::BadRequest().json(ApiResponse {
                        code: error_code.code(),
                        message: "无效的收藏夹ID格式".to_string(),
                        data: serde_json::Value::Null,
                    });
                }
            };
            let tag_names = params.tag_names.as_ref().unwrap();
            mysql_bookmark_service.search_bookmarks_by_tags(user_id, tag_names, Some(favorite_id), page, page_size).await
        },
        (true, false) => {
            // 只传了收藏夹ID：按收藏夹查询（原有功能）
            let parent_id_str = params.parent_id.as_ref().unwrap();
            let favorite_id = match parent_id_str.parse::<u64>() {
                Ok(id) => id,
                Err(e) => {
                    error!("无效的收藏夹ID格式: {}", e);
                    let error_code = ErrorCode::InvalidParameter;
                    return HttpResponse::BadRequest().json(ApiResponse {
                        code: error_code.code(),
                        message: "无效的收藏夹ID格式".to_string(),
                        data: serde_json::Value::Null,
                    });
                }
            };
            mysql_bookmark_service.get_bookmarks_by_favorite_id_with_platform_filter(user_id, favorite_id, page, page_size, platform_filter.clone()).await
        },
        (false, true) => {
            // 只传了标签：在用户全量书签中按标签搜索
            let tag_names = params.tag_names.as_ref().unwrap();
            mysql_bookmark_service.search_bookmarks_by_tags(user_id, tag_names, None, page, page_size).await
        },
        (false, false) => {
            // 都不传：返回用户的所有书签
            mysql_bookmark_service.get_all_bookmarks_by_user_with_platform_filter(user_id, page, page_size, platform_filter).await
        }
    };

    // 处理查询结果
    match result {
        Ok((bookmarks, total)) => {
            // 提取书签ID列表
            let bookmark_ids: Vec<u64> = bookmarks.iter().map(|b| b.id).collect();

            // 批量获取标签信息
            let bookmark_tags = match mysql_bookmark_service.get_tags_by_bookmark_ids(&bookmark_ids).await {
                Ok(tags) => tags,
                Err(e) => {
                    error!("获取书签标签失败: {}", e);
                    Vec::new() // 如果获取标签失败，返回空标签列表
                }
            };

            // 转换为响应格式
            let bookmark_items: Vec<BookmarkItem> = bookmarks.into_iter().map(|b| {
                // 为当前书签筛选标签
                let tags: Vec<crate::dto::bookmark::TagInfo> = bookmark_tags
                    .iter()
                    .filter(|tag| tag.bookmark_id == b.id)
                    .map(|tag| crate::dto::bookmark::TagInfo {
                        id: tag.tag_id.to_string(),
                        name: tag.tag_name.clone(),
                        background_color: tag.tag_background_color.clone(),
                        text_color: tag.tag_text_color.clone(),
                    })
                    .collect();

                BookmarkItem {
                    id: b.id.to_string(),
                    influencer_name: b.influencer_name,
                    influencer_avatar: b.influencer_avatar,
                    cover: b.cover,
                    title: b.title,
                    desc: b.desc,
                    scheme_url: b.scheme_url,
                    parent_id: b.favorite_id.to_string(),
                    create_time: b.created_at,
                    update_time: b.updated_at,
                    tags,
                    platform_type: b.platform_type.clone(),
                }
            }).collect();

            match (has_parent_id, has_tag_search) {
                (true, true) => info!("用户 {} 在收藏夹内按标签搜索书签成功，共 {} 个", user_id, total),
                (true, false) => info!("用户 {} 按收藏夹获取书签列表成功，共 {} 个", user_id, total),
                (false, true) => info!("用户 {} 按标签搜索全量书签成功，共 {} 个", user_id, total),
                (false, false) => info!("用户 {} 获取全量书签列表成功，共 {} 个", user_id, total),
            }

            let success_code = ErrorCode::Success;
            HttpResponse::Ok().json(ApiResponse {
                code: success_code.code(),
                message: success_code.message().to_string(),
                data: BookmarkListResponse {
                    bookmarks: bookmark_items,
                    total,
                    page,
                    page_size,
                },
            })
        }
        Err(e) => {
            let error_code = match e {
                MySqlBookmarkServiceError::DatabaseError(_) => ErrorCode::DatabaseError,
                _ => ErrorCode::SystemError,
            };

            error!("获取书签列表失败: {}", e);

            HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: error_code.message().to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}