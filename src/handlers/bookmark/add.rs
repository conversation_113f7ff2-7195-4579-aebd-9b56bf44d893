use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{error, info};
use serde_json::Value;
use chrono::Utc;

use crate::{
    AppState,
    dto::{ApiResponse, AddBookmarkRequest, BookmarkResponse, ErrorCode},
    middleware::token_parser::get_user_id_from_request,
    db::utils::mysql_helper::get_mysql_database_name,
};

/**
 * 添加书签接口处理函数
 *
 * 此函数处理POST /bookmark/add请求，用于添加一个新的书签
 *
 * @param req HTTP请求
 * @param payload 请求体，包含书签信息
 * @param app_state 应用状态
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    payload: web::Json<AddBookmarkRequest>,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 获取提交的数据
    let bookmark_data = payload.into_inner();

    // 获取MySQL书签服务
    let mysql_bookmark_service = match &app_state.mysql_bookmark_service {
        Some(service) => service,
        None => {
            error!("MySQL书签服务未初始化");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 验证并转换收藏夹ID
    let favorite_id = match bookmark_data.parent_id.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("无效的收藏夹ID格式: {}", e);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "无效的收藏夹ID格式".to_string(),
                data: Value::Null,
            });
        }
    };

    // 获取用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("无效的用户ID格式: {}", e);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "无效的用户ID格式".to_string(),
                data: Value::Null,
            });
        }
    };

    // 调用服务添加书签
    let result = mysql_bookmark_service.create_bookmark(
        user_id,
        favorite_id,
        bookmark_data.influencer_name,
        bookmark_data.influencer_avatar,
        bookmark_data.cover,
        bookmark_data.title,
        bookmark_data.desc,
        bookmark_data.scheme_url,
        bookmark_data.tags,
        bookmark_data.platform_type,
    ).await;

    // 处理结果
    match result {
        Ok(bookmark) => {
            info!("用户 {} 添加书签成功: {}", user_id_str, bookmark.title);

            // 获取书签的标签信息
            let bookmark_tags = match mysql_bookmark_service.get_tags_by_bookmark_ids(&[bookmark.id]).await {
                Ok(tags) => tags,
                Err(e) => {
                    error!("获取书签标签失败: {}", e);
                    Vec::new() // 如果获取标签失败，返回空标签列表
                }
            };

            // 创建书签响应
            let response = BookmarkResponse::from_mysql_model_with_tags(&bookmark, &bookmark_tags);

            // 构造HTTP响应
            let http_response = HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "添加书签成功".to_string(),
                data: response.clone(),
            });

            // 异步发送MNS消息
            if let Some(mns_client) = &app_state.mns_client {
                // 克隆MNS客户端的Arc引用，以便在异步任务中使用
                let mns_client_clone = mns_client.clone();

                // 获取当前环境对应的数据库名称
                let db_name = get_mysql_database_name(&app_state.config);

                // 构造消息内容，包含书签信息、数据库名称和表名
                let message_content = serde_json::json!({
                    "bookmark": {
                        "id": bookmark.id.to_string(),
                        "user_id": bookmark.user_id.to_string(),
                        "influencer_name": bookmark.influencer_name,
                        "influencer_avatar": bookmark.influencer_avatar,
                        "cover": bookmark.cover,
                        "title": bookmark.title,
                        "desc": bookmark.desc,
                        "scheme_url": bookmark.scheme_url,
                        "favorite_id": bookmark.favorite_id.to_string(),
                        "created_at": bookmark.created_at.timestamp_millis(),
                        "updated_at": bookmark.updated_at.timestamp_millis()
                    },
                    "database": db_name,
                    "table": "bookmarks"
                });

                // 将消息内容转换为字符串
                let message_str = message_content.to_string();

                // 构造消息属性
                let attributes = crate::utils::MnsMessageAttributes {
                    message_id: None,
                    message_body_md5: None,
                    message_body_type: None,
                    priority: None,
                    delay_seconds: None,
                };

                // 发送消息到MNS（不等待结果，避免阻塞用户响应）
                tokio::spawn(async move {
                    match mns_client_clone.send_message(&message_str, Some(attributes)).await {
                        Ok(response) => {
                            info!("书签MNS消息发送成功，消息ID: {}", response.message_id);
                        },
                        Err(e) => {
                            // 仅记录错误，不影响用户操作
                            error!("书签MNS消息发送失败: {}", e);
                        }
                    }
                });
            } else {
                error!("MNS客户端未初始化，无法发送书签变更消息");
            }

            // 返回响应
            http_response
        },
        Err(e) => {
            error!("添加书签失败: {}", e);

            match e {
                crate::services::mysql::MySqlBookmarkServiceError::BookmarkAlreadyExists => {
                    HttpResponse::Conflict().json(ApiResponse {
                        code: ErrorCode::SystemError.code(),
                        message: "书签已存在".to_string(),
                        data: Value::Null,
                    })
                },
                crate::services::mysql::MySqlBookmarkServiceError::FavoriteNotFound => {
                    HttpResponse::NotFound().json(ApiResponse {
                        code: ErrorCode::SystemError.code(),
                        message: "收藏夹不存在".to_string(),
                        data: Value::Null,
                    })
                },
                _ => {
                    HttpResponse::InternalServerError().json(ApiResponse {
                        code: ErrorCode::SystemError.code(),
                        message: "服务器内部错误".to_string(),
                        data: Value::Null,
                    })
                }
            }
        }
    }
}