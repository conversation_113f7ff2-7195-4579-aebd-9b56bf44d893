use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{error, info};

use crate::{
    AppState,
    dto::{ApiResponse, error_code::ErrorCode, tag::DeleteTagRequest},
    middleware::token_parser::get_user_id_from_request,
    services::mysql::MySqlTagServiceError,
};

/**
 * 删除标签处理函数
 *
 * 此函数处理DELETE /tags/delete请求，用于删除指定的标签
 * 业务规则：
 * 1. 用户只能删除自己创建的标签
 * 2. 删除标签时会自动删除相关的书签标签关联记录
 *
 * @param req HTTP请求
 * @param app_state 应用状态
 * @param delete_data 删除标签请求数据
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    app_state: web::Data<AppState>,
    delete_data: web::Json<DeleteTagRequest>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式错误: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "用户ID格式错误".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 解析标签ID
    let tag_id = match delete_data.id.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("标签ID格式错误: {}", delete_data.id);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "标签ID格式错误".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取MySQL标签服务
    let mysql_tag_service = match &app_state.mysql_tag_service {
        Some(service) => service,
        None => {
            error!("MySQL标签服务未初始化");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务不可用".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 验证标签是否存在且属于当前用户
    match mysql_tag_service.get_tag_by_id(tag_id).await {
        Ok(Some(tag)) => {
            if tag.user_id != user_id {
                error!("用户 {} 尝试删除不属于自己的标签 {}", user_id, tag_id);
                return HttpResponse::Forbidden().json(ApiResponse {
                    code: ErrorCode::InvalidParameter.code(),
                    message: "无权限删除此标签".to_string(),
                    data: serde_json::Value::Null,
                });
            }
        }
        Ok(None) => {
            error!("标签不存在: {}", tag_id);
            return HttpResponse::NotFound().json(ApiResponse {
                code: ErrorCode::TagNotFound.code(),
                message: "标签不存在".to_string(),
                data: serde_json::Value::Null,
            });
        }
        Err(MySqlTagServiceError::DatabaseError(e)) => {
            error!("数据库查询错误: {}", e);
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            });
        }
        Err(e) => {
            error!("查询标签失败: {}", e);
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            });
        }
    }

    // 删除标签
    match mysql_tag_service.delete_tag(tag_id).await {
        Ok(_) => {
            info!("用户 {} 删除标签成功: {}", user_id, tag_id);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "删除标签成功".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(MySqlTagServiceError::TagNotFound) => {
            error!("标签不存在: {}", tag_id);
            HttpResponse::NotFound().json(ApiResponse {
                code: ErrorCode::TagNotFound.code(),
                message: "标签不存在".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(MySqlTagServiceError::DatabaseError(e)) => {
            error!("数据库删除错误: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            })
        }
        Err(e) => {
            error!("删除标签失败: {}", e);
            HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务错误，请稍后再试".to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}
