use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};

use crate::{
    AppState,
    dto::{ApiResponse, error_code::ErrorCode, tag::{TagListResponse, TagWithCountResponse}},
    middleware::token_parser::get_user_id_from_request,
    services::mysql::MySqlTagServiceError,
};

/**
 * 获取用户标签列表处理函数
 *
 * 此函数处理GET /tags/list请求，用于获取用户的所有标签及使用统计
 *
 * @param req HTTP请求
 * @param app_state 应用状态
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取MySQL标签服务
    let mysql_tag_service = match &app_state.mysql_tag_service {
        Some(service) => service,
        None => {
            error!("MySQL标签服务未初始化");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务不可用".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("无效的用户ID格式: {}", e);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "无效的用户ID格式".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取用户标签列表
    match mysql_tag_service.get_user_tags_with_count(user_id).await {
        Ok(tags_with_count) => {
            // 转换为响应格式
            let tag_responses: Vec<TagWithCountResponse> = tags_with_count
                .into_iter()
                .map(|tag_with_count| TagWithCountResponse::from_mysql_model(&tag_with_count))
                .collect();

            info!("获取用户 {} 的标签列表成功，共 {} 个", user_id, tag_responses.len());

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "获取标签列表成功".to_string(),
                data: TagListResponse {
                    tags: tag_responses,
                },
            })
        }
        Err(e) => {
            let error_code = match e {
                MySqlTagServiceError::DatabaseError(_) => ErrorCode::DatabaseError,
                MySqlTagServiceError::DatabaseNotAvailable => ErrorCode::ServiceUnavailable,
                _ => ErrorCode::SystemError,
            };

            error!("获取标签列表失败: {}", e);

            HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: "获取标签列表失败".to_string(),
                data: serde_json::Value::Null,
            })
        }
    }
}
