use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};

use crate::{
    AppState,
    dto::{ApiResponse, error_code::ErrorCode, tag::{CreateTagRequest, TagResponse}},
    middleware::token_parser::get_user_id_from_request,
    services::mysql::MySqlTagServiceError,
};

/**
 * 创建标签处理函数
 *
 * 此函数处理POST /tags/create请求，用于创建新的标签
 * 业务规则：
 * 1. 每个用户最多创建50个标签
 * 2. 标签名字不能为空
 * 3. 同一个用户不能有同名的标签
 *
 * @param req HTTP请求
 * @param app_state 应用状态
 * @param tag_data 创建标签请求数据
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    app_state: web::Data<AppState>,
    tag_data: web::Json<CreateTagRequest>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取MySQL标签服务
    let mysql_tag_service = match &app_state.mysql_tag_service {
        Some(service) => service,
        None => {
            error!("MySQL标签服务未初始化");
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务不可用".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 获取用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("无效的用户ID格式: {}", e);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "无效的用户ID格式".to_string(),
                data: serde_json::Value::Null,
            });
        }
    };

    // 验证标签名称
    if tag_data.name.trim().is_empty() {
        error!("标签名称不能为空");
        return HttpResponse::BadRequest().json(ApiResponse {
            code: ErrorCode::TagNameEmpty.code(),
            message: "标签名称不能为空".to_string(),
            data: serde_json::Value::Null,
        });
    }

    if tag_data.name.len() > 50 {
        error!("标签名称过长: {}", tag_data.name);
        return HttpResponse::BadRequest().json(ApiResponse {
            code: ErrorCode::TagNameTooLong.code(),
            message: "标签名称不能超过50个字符".to_string(),
            data: serde_json::Value::Null,
        });
    }

    // 创建标签
    match mysql_tag_service.create_tag(
        user_id,
        tag_data.name.clone(),
        tag_data.background_color.clone(),
        tag_data.text_color.clone(),
    ).await {
        Ok(tag) => {
            // 转换为响应格式
            let tag_response = TagResponse::from_mysql_model(&tag);

            info!("用户 {} 创建标签成功: {}", user_id, tag.name);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "创建标签成功".to_string(),
                data: tag_response,
            })
        }
        Err(e) => {
            let (error_code, message) = match e {
                MySqlTagServiceError::InvalidTagName => {
                    (ErrorCode::TagNameEmpty, "标签名称无效".to_string())
                }
                MySqlTagServiceError::InvalidTagColor => {
                    (ErrorCode::TagColorInvalid, "标签颜色格式无效".to_string())
                }
                MySqlTagServiceError::TagAlreadyExists => {
                    (ErrorCode::TagAlreadyExists, "标签名称已存在".to_string())
                }
                MySqlTagServiceError::TagLimitExceeded => {
                    (ErrorCode::TagLimitExceeded, "标签数量已达上限，每个用户最多创建50个标签".to_string())
                }
                MySqlTagServiceError::DatabaseError(_) => {
                    (ErrorCode::DatabaseError, "数据库错误".to_string())
                }
                MySqlTagServiceError::DatabaseNotAvailable => {
                    (ErrorCode::ServiceUnavailable, "服务不可用".to_string())
                }
                _ => {
                    (ErrorCode::SystemError, "系统错误".to_string())
                }
            };

            error!("创建标签失败: {}", e);

            HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message,
                data: serde_json::Value::Null,
            })
        }
    }
}
