use sqlx::Row;
use std::sync::Arc;
use crate::AppState;
use crate::db::utils::mysql_helper;

/// 执行更新收藏夹封面的定时任务
pub async fn execute(app_state: &Arc<AppState>) -> Result<u32, Box<dyn std::error::Error>> {
    // 只在生产环境执行
    let app_env = std::env::var("APP_ENV").unwrap_or_else(|_| "development".to_string());
    if app_env != "production" {
        println!("定时任务只在生产环境执行，当前环境: {}", app_env);
        return Ok(0);
    }

    // 检查数据库连接是否可用
    let db_connections = app_state.db.as_ref()
        .ok_or("数据库连接不可用")?;

    // 获取MySQL连接池
    let pool = mysql_helper::get_mysql_pool(db_connections)
        .ok_or("MySQL连接池不可用")?;

    // 查询封面不是OSS链接的收藏夹ID、名称和当前封面
    let favorites = sqlx::query(
        "SELECT id, name, cover FROM favorites WHERE cover != '' AND cover NOT LIKE '%assets-xunhe.oss-cn-qingdao.aliyuncs.com%' ORDER BY updated_at DESC"
    )
    .fetch_all(pool)
    .await?;

    let mut updated_count = 0;
    let mut no_oss_count = 0;

    // 循环处理每个收藏夹
    for favorite in favorites {
        let favorite_id: u64 = favorite.get("id");
        let _name: String = favorite.get("name");
        let _current_cover: String = favorite.get("cover");

        // 查询该收藏夹内按时间正序排序的最早的一个OSS封面
        let earliest_oss_bookmark = sqlx::query(
            "SELECT cover FROM bookmarks WHERE favorite_id = ? AND cover LIKE '%assets-xunhe.oss-cn-qingdao.aliyuncs.com%' ORDER BY created_at ASC LIMIT 1"
        )
        .bind(favorite_id)
        .fetch_optional(pool)
        .await?;

        if let Some(bookmark) = earliest_oss_bookmark {
            let oss_cover: String = bookmark.get("cover");

            // 更新收藏夹封面为OSS链接
            let update_result = sqlx::query(
                "UPDATE favorites SET cover = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
            )
            .bind(&oss_cover)
            .bind(favorite_id)
            .execute(pool)
            .await;

            match update_result {
                Ok(result) => {
                    if result.rows_affected() > 0 {
                        updated_count += 1;
                    }
                }
                Err(_) => {
                    // 忽略错误，继续处理下一个
                }
            }
        } else {
            no_oss_count += 1;
        }
    }

    // 简单打印结果
    println!("收藏夹封面更新完成 - 成功更新: {}, 无OSS封面: {}", updated_count, no_oss_count);

    Ok(updated_count)
}