use std::sync::Arc;
use std::time::Duration;
use tokio::time;
use log::{info, error};
use chrono::{Local, Timelike};

use crate::AppState;

pub mod update_favorite_covers;

/// 定时任务管理器
pub struct ScheduleManager {
    app_state: Arc<AppState>,
}

impl ScheduleManager {
    /// 创建新的定时任务管理器
    pub fn new(app_state: Arc<AppState>) -> Self {
        Self { app_state }
    }

    /// 启动所有定时任务
    pub async fn start_all(&self) {
        info!("开始启动所有定时任务");

        // 启动更新收藏夹封面的定时任务
        self.start_update_favorite_covers().await;

        info!("所有定时任务启动完成");
    }

    /// 启动更新收藏夹封面的定时任务
    async fn start_update_favorite_covers(&self) {
        let app_state = self.app_state.clone();

        tokio::spawn(async move {
            // 等待1分钟后开始第一次执行
            time::sleep(Duration::from_secs(60)).await;

            let mut interval = time::interval(Duration::from_secs(21600)); // 每6小时执行一次 (6 * 60 * 60 = 21600秒)

            loop {
                interval.tick().await;

                info!("开始执行更新收藏夹封面定时任务");

                match update_favorite_covers::execute(&app_state).await {
                    Ok(updated_count) => {
                        info!("更新收藏夹封面定时任务执行完成，更新了 {} 个收藏夹", updated_count);
                    },
                    Err(e) => {
                        error!("更新收藏夹封面定时任务执行失败: {}", e);
                    }
                }
            }
        });

        info!("更新收藏夹封面定时任务已启动，1分钟后开始执行，每6小时执行一次");
    }
}
