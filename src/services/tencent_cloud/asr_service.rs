use std::sync::Arc;
use chrono::{DateTime, Utc};
use hmac::{Hmac, Mac};
use log::{debug, error, info};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use sha2::{Digest, Sha256};
use thiserror::Error;

use crate::config::{AppConfig, TencentCloudConfig};

/// 腾讯云语音识别服务错误类型
#[derive(Debug, Error)]
pub enum TencentAsrServiceError {
    #[error("HTTP请求错误: {0}")]
    HttpError(#[from] reqwest::Error),

    #[error("腾讯云API返回错误: {0}")]
    ApiError(String),

    #[error("JSON解析错误: {0}")]
    JsonError(#[from] serde_json::Error),

    #[error("签名计算错误: {0}")]
    SignatureError(String),

    #[error("参数错误: {0}")]
    ParameterError(String),
}

/// 创建语音识别任务请求
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateRecTaskRequest {

    /// 语音数据来源，0：语音URL，1：语音数据（post body）
    #[serde(rename = "SourceType")]
    pub source_type: u32,



    /// 引擎模型类型，支持：16k_zh、16k_zh_video、16k_en、16k_ca、16k_ja、16k_zh_edu等
    #[serde(rename = "EngineModelType")]
    pub engine_model_type: String,

    /// 音频声道数，仅支持单声道，请填写1
    #[serde(rename = "ChannelNum")]
    pub channel_num: u32,



    /// 自定义热词ID，用于提高特定词汇的识别准确率
    #[serde(rename = "CustomizationId", skip_serializing_if = "Option::is_none")]
    pub customization_id: Option<String>,

    /// 热词ID，用于提高特定词汇的识别准确率
    #[serde(rename = "HotwordId", skip_serializing_if = "Option::is_none")]
    pub hotword_id: Option<String>,

    /// 音频文件的下载地址
    #[serde(rename = "Url")]
    pub url: String,

    /// 说话人分离人数，取值范围：0-10，0代表自动分离（目前仅支持≤6个人），1-10代表指定说话人数分离
    #[serde(rename = "SpeakerNumber")]
    pub speaker_number: u32,

    /// 回调地址，用户自行搭建的用于接收识别结果的服务器地址，长度小于2048字节
    #[serde(rename = "CallbackUrl", skip_serializing_if = "Option::is_none")]
    pub callback_url: Option<String>,

    /// 识别结果文本格式，0：基础格式，1：详细格式，默认为0
    #[serde(rename = "ResTextFormat")]
    pub res_text_format: u32,
}

/// 创建语音识别任务响应数据
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateRecTaskData {
    /// 任务ID，可通过此ID在轮询接口获取识别状态与结果
    #[serde(rename = "TaskId")]
    pub task_id: u64,
}

/// 创建语音识别任务响应
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateRecTaskResponse {
    /// 响应数据
    #[serde(rename = "Data")]
    pub data: CreateRecTaskData,

    /// 请求的唯一标识，每次请求都会返回
    #[serde(rename = "RequestId")]
    pub request_id: String,
}

/// 腾讯云语音识别服务
pub struct TencentAsrService {
    config: TencentCloudConfig,
    http_client: Client,
}

impl TencentAsrService {
    /// 创建新的腾讯云语音识别服务实例
    pub fn new(config: &Arc<AppConfig>) -> Self {
        let http_client = Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .expect("创建HTTP客户端失败");

        Self {
            config: config.tencent_cloud.clone(),
            http_client,
        }
    }

    /// 创建语音识别任务
    pub async fn create_rec_task(&self, request: CreateRecTaskRequest) -> Result<CreateRecTaskResponse, TencentAsrServiceError> {
        info!("开始创建腾讯云语音识别任务");
        
        let host = "asr.tencentcloudapi.com";
        let service = "asr";
        let region = "";
        let action = "CreateRecTask";
        let version = "2019-06-14";
        
        // 获取当前时间戳
        let timestamp = Utc::now().timestamp();
        let date = self.get_date(timestamp);
        
        // 序列化请求体
        let payload = serde_json::to_string(&request)?;
        debug!("请求体: {}", payload);
        
        // 计算签名
        let authorization = self.calculate_authorization(
            host, service, region, action, version, timestamp, &date, &payload
        )?;
        
        // 构造请求头
        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert("Authorization", authorization.parse().unwrap());
        headers.insert("Content-Type", "application/json; charset=utf-8".parse().unwrap());
        headers.insert("Host", host.parse().unwrap());
        headers.insert("X-TC-Action", action.parse().unwrap());
        headers.insert("X-TC-Timestamp", timestamp.to_string().parse().unwrap());
        headers.insert("X-TC-Version", version.parse().unwrap());
        
        if !region.is_empty() {
            headers.insert("X-TC-Region", region.parse().unwrap());
        }
        
        // 发送请求
        let url = format!("https://{}", host);
        debug!("发送请求到: {}", url);
        
        let response = self.http_client
            .post(&url)
            .headers(headers)
            .body(payload)
            .send()
            .await?;
            
        let status = response.status();
        let response_text = response.text().await?;
        
        debug!("响应状态: {}", status);
        debug!("响应内容: {}", response_text);
        
        if !status.is_success() {
            error!("腾讯云API返回错误状态码 {}: {}", status, response_text);
            return Err(TencentAsrServiceError::ApiError(format!("HTTP状态码: {}, 响应: {}", status, response_text)));
        }
        
        // 解析响应
        let api_response: serde_json::Value = serde_json::from_str(&response_text)?;
        
        // 检查是否有错误
        if let Some(error) = api_response.get("Response").and_then(|r| r.get("Error")) {
            let error_code = error.get("Code").and_then(|c| c.as_str()).unwrap_or("Unknown");
            let error_message = error.get("Message").and_then(|m| m.as_str()).unwrap_or("Unknown error");
            error!("腾讯云API返回业务错误: {} - {}", error_code, error_message);
            return Err(TencentAsrServiceError::ApiError(format!("{}: {}", error_code, error_message)));
        }
        
        // 提取响应数据
        let response_data = api_response.get("Response")
            .ok_or_else(|| TencentAsrServiceError::ApiError("响应格式错误：缺少Response字段".to_string()))?;

        // 打印响应数据用于调试
        debug!("腾讯云响应数据: {}", serde_json::to_string_pretty(response_data).unwrap_or_default());

        let result: CreateRecTaskResponse = serde_json::from_value(response_data.clone())?;
        
        info!("语音识别任务创建成功，任务ID: {}", result.data.task_id);
        Ok(result)
    }

    /// 获取日期字符串 (YYYY-MM-DD)
    pub fn get_date(&self, timestamp: i64) -> String {
        let datetime = DateTime::from_timestamp(timestamp, 0).unwrap();
        datetime.format("%Y-%m-%d").to_string()
    }

    /// 计算SHA256哈希
    pub fn get_hash(&self, message: &str) -> String {
        let mut hasher = Sha256::new();
        hasher.update(message.as_bytes());
        hex::encode(hasher.finalize())
    }

    /// 计算HMAC-SHA256
    fn sha256_hmac(&self, message: &str, secret: &[u8]) -> Result<Vec<u8>, TencentAsrServiceError> {
        type HmacSha256 = Hmac<Sha256>;
        let mut mac = HmacSha256::new_from_slice(secret)
            .map_err(|e| TencentAsrServiceError::SignatureError(format!("HMAC初始化失败: {}", e)))?;
        mac.update(message.as_bytes());
        Ok(mac.finalize().into_bytes().to_vec())
    }

    /// 计算授权头
    fn calculate_authorization(
        &self,
        host: &str,
        service: &str,
        region: &str,
        action: &str,
        version: &str,
        timestamp: i64,
        date: &str,
        payload: &str,
    ) -> Result<String, TencentAsrServiceError> {
        // 步骤1：拼接规范请求串
        let signed_headers = "content-type;host";
        let hashed_request_payload = self.get_hash(payload);
        let http_request_method = "POST";
        let canonical_uri = "/";
        let canonical_query_string = "";
        let canonical_headers = format!(
            "content-type:application/json; charset=utf-8\nhost:{}\n",
            host
        );

        let canonical_request = format!(
            "{}\n{}\n{}\n{}\n{}\n{}",
            http_request_method,
            canonical_uri,
            canonical_query_string,
            canonical_headers,
            signed_headers,
            hashed_request_payload
        );

        // 步骤2：拼接待签名字符串
        let algorithm = "TC3-HMAC-SHA256";
        let hashed_canonical_request = self.get_hash(&canonical_request);
        let credential_scope = format!("{}/{}/tc3_request", date, service);
        let string_to_sign = format!(
            "{}\n{}\n{}\n{}",
            algorithm, timestamp, credential_scope, hashed_canonical_request
        );

        // 步骤3：计算签名
        let secret_key = format!("TC3{}", self.config.secret_key);
        let k_date = self.sha256_hmac(date, secret_key.as_bytes())?;
        let k_service = self.sha256_hmac(service, &k_date)?;
        let k_signing = self.sha256_hmac("tc3_request", &k_service)?;
        let signature_bytes = self.sha256_hmac(&string_to_sign, &k_signing)?;
        let signature = hex::encode(signature_bytes);

        // 步骤4：拼接Authorization
        let authorization = format!(
            "{} Credential={}/{}, SignedHeaders={}, Signature={}",
            algorithm, self.config.secret_id, credential_scope, signed_headers, signature
        );

        Ok(authorization)
    }
}
