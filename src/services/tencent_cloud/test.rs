#[cfg(test)]
mod tests {
    use crate::services::tencent_cloud::{TencentAsrService, TencentAsrServiceError, CreateRecTaskRequest};
    use crate::config::AppConfig;

    #[tokio::test]
    async fn test_tencent_asr_service_direct() {
        // 初始化日志
        let _ = env_logger::builder().is_test(true).try_init();

        // 创建配置
        let config = AppConfig::new();
        
        // 创建腾讯云语音识别服务
        let asr_service = TencentAsrService::new(&config);
        
        // 构造测试请求
        let request = CreateRecTaskRequest {
            source_type: 0,
            engine_model_type: "16k_zh_video".to_string(),
            channel_num: 1,
            customization_id: Some("".to_string()),
            hotword_id: Some("".to_string()),
            url: "https://cn-jsnt-ct-01-30.bilivideo.com/upgcxcode/30/10/25806511030/25806511030-1-16.mp4?e=ig8euxZM2rNcNbRVhwdVhwdlhWdVhwdVhoNvNC8BqJIzNbfq9rVEuxTEnE8L5F6VnEsSTx0vkX8fqJeYTj_lta53NCM=&uipk=5&nbs=1&deadline=1750328665&gen=playurlv2&os=bcache&oi=0&trid=0000a34726e228c8484791527e4facff845dh&mid=0&platform=html5&og=hw&upsig=9bf6103ed17bfc1f6a8c3979e3399960&uparams=e,uipk,nbs,deadline,gen,os,oi,trid,mid,platform,og&cdnid=9924&bvc=vod&nettype=0&f=h_0_0&bw=29271&logo=80000000".to_string(),
            speaker_number: 0,
            callback_url: Some("https://bb3d-240e-46c-7331-e090-480e-f0a6-74e1-dbe4.ngrok-free.app/tencent_cloud/asr/callback".to_string()),
            res_text_format: 3,
        };
        
        println!("🚀 开始测试腾讯云语音识别服务...");
        println!("📋 配置信息:");
        println!("  SECRET_ID: {}", config.tencent_cloud.secret_id);
        println!("  SECRET_KEY: {}****{}",
            &config.tencent_cloud.secret_key[..4],
            &config.tencent_cloud.secret_key[config.tencent_cloud.secret_key.len()-4..]);
        
        println!("📝 请求参数:");
        println!("  版本: 2019-06-14 (在HTTP头中)");
        println!("  引擎类型: {}", request.engine_model_type);
        println!("  音频URL: {}...", &request.url[..100]);
        if let Some(ref callback_url) = request.callback_url {
            println!("  回调URL: {}", callback_url);
        }
        
        // 调用腾讯云服务
        match asr_service.create_rec_task(request).await {
            Ok(response) => {
                println!("✅ 测试成功！");
                println!("📊 响应结果:");
                println!("  任务ID: {}", response.data.task_id);
                println!("  请求ID: {}", response.request_id);
            }
            Err(e) => {
                println!("❌ 测试失败: {:?}", e);
                
                // 详细分析错误类型
                match &e {
                    TencentAsrServiceError::ApiError(msg) => {
                        println!("🔍 API错误详情: {}", msg);
                    }
                    TencentAsrServiceError::HttpError(err) => {
                        println!("🔍 HTTP错误详情: {}", err);
                    }
                    TencentAsrServiceError::JsonError(err) => {
                        println!("🔍 JSON解析错误详情: {}", err);
                    }
                    TencentAsrServiceError::SignatureError(msg) => {
                        println!("🔍 签名错误详情: {}", msg);
                    }
                    TencentAsrServiceError::ParameterError(msg) => {
                        println!("🔍 参数错误详情: {}", msg);
                    }
                }
                
                // 不panic，只是记录错误
                eprintln!("腾讯云语音识别API调用失败，但这可能是正常的（配置、网络等问题）");
            }
        }
    }

    #[test]
    fn test_signature_components() {
        // 测试签名计算的各个组件
        let config = AppConfig::new();
        let asr_service = TencentAsrService::new(&config);
        
        println!("🔧 测试签名计算组件...");
        
        // 测试哈希计算
        let test_message = "test message";
        let hash_result = asr_service.get_hash(test_message);
        println!("SHA256哈希测试: {} -> {}", test_message, hash_result);
        assert!(!hash_result.is_empty());
        assert_eq!(hash_result.len(), 64); // SHA256 hex编码长度
        
        // 测试日期格式
        let timestamp = 1734518702; // 2024-12-18 14:25:02 UTC
        let date = asr_service.get_date(timestamp);
        println!("日期格式测试: {} -> {}", timestamp, date);
        assert_eq!(date.len(), 10); // YYYY-MM-DD 格式
        assert_eq!(date, "2024-12-18");
        
        println!("✅ 签名组件测试通过");
    }
}
