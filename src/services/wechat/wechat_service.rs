use std::sync::Arc;
use log::{debug, error, info};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use thiserror::Error;

use crate::config::{AppConfig, WechatConfig};

/// 微信服务错误类型
#[derive(Debug, Error)]
pub enum WechatServiceError {
    #[error("HTTP请求错误: {0}")]
    HttpError(#[from] reqwest::Error),

    #[error("微信API返回错误: {0}")]
    ApiError(String),

    #[error("JSON解析错误: {0}")]
    JsonError(#[from] serde_json::Error),

    #[error("参数错误: {0}")]
    ParameterError(String),

    #[error("用户未找到")]
    UserNotFound,
}

/// 微信获取访问令牌响应
#[derive(Debug, Deserialize, Serialize)]
pub struct WechatAccessTokenResponse {
    /// 访问令牌
    pub access_token: Option<String>,
    /// 过期时间（秒）
    pub expires_in: Option<i64>,
    /// 刷新令牌
    pub refresh_token: Option<String>,
    /// 用户OpenID
    pub openid: Option<String>,
    /// 授权范围
    pub scope: Option<String>,
    /// 用户统一标识（如果请求scope为snsapi_userinfo，则此字段有值）
    pub unionid: Option<String>,
    /// 错误码
    pub errcode: Option<i32>,
    /// 错误信息
    pub errmsg: Option<String>,
}

/// 微信用户信息响应
#[derive(Debug, Deserialize, Serialize)]
pub struct WechatUserInfoResponse {
    /// 用户OpenID
    pub openid: Option<String>,
    /// 用户昵称
    pub nickname: Option<String>,
    /// 用户性别（1为男性，2为女性，0为未知）
    pub sex: Option<i32>,
    /// 用户所在省份
    pub province: Option<String>,
    /// 用户所在城市
    pub city: Option<String>,
    /// 用户所在国家
    pub country: Option<String>,
    /// 用户头像URL
    pub headimgurl: Option<String>,
    /// 用户特权信息
    pub privilege: Option<Vec<String>>,
    /// 用户统一标识
    pub unionid: Option<String>,
    /// 错误码
    pub errcode: Option<i32>,
    /// 错误信息
    pub errmsg: Option<String>,
}

/// 微信服务，处理微信相关的API调用
pub struct WechatService {
    config: WechatConfig,
    http_client: Client,
}

impl WechatService {
    /// 创建新的微信服务实例
    pub fn new(config: &Arc<AppConfig>) -> Self {
        let http_client = Client::builder()
            .timeout(std::time::Duration::from_secs(10))
            .build()
            .expect("创建HTTP客户端失败");

        Self {
            config: config.wechat.clone(),
            http_client,
        }
    }

    /// 使用授权码获取访问令牌和用户信息
    pub async fn get_access_token(&self, code: &str) -> Result<WechatAccessTokenResponse, WechatServiceError> {
        let url = format!(
            "https://api.weixin.qq.com/sns/oauth2/access_token?appid={}&secret={}&code={}&grant_type=authorization_code",
            self.config.app_id, self.config.app_secret, code
        );

        debug!("请求微信访问令牌: {}", url);
        
        let response = self.http_client.get(&url).send().await?;
        let status = response.status();
        
        if !status.is_success() {
            let error_text = response.text().await.unwrap_or_else(|_| "无法获取错误详情".to_string());
            error!("微信API返回错误状态码 {}: {}", status, error_text);
            return Err(WechatServiceError::ApiError(format!("HTTP状态码: {}", status)));
        }
        
        let token_response: WechatAccessTokenResponse = response.json().await?;
        
        // 检查微信API是否返回错误
        if let Some(errcode) = token_response.errcode {
            if errcode != 0 {
                let error_msg = token_response.errmsg.unwrap_or_else(|| "未知错误".to_string());
                error!("微信API返回错误: {} - {}", errcode, error_msg);
                return Err(WechatServiceError::ApiError(format!("错误码: {}, 错误信息: {}", errcode, error_msg)));
            }
        }
        
        info!("成功获取微信访问令牌");
        Ok(token_response)
    }

    /// 获取微信用户信息
    pub async fn get_user_info(&self, access_token: &str, openid: &str) -> Result<WechatUserInfoResponse, WechatServiceError> {
        let url = format!(
            "https://api.weixin.qq.com/sns/userinfo?access_token={}&openid={}",
            access_token, openid
        );

        debug!("请求微信用户信息: {}", url);
        
        let response = self.http_client.get(&url).send().await?;
        let status = response.status();
        
        if !status.is_success() {
            let error_text = response.text().await.unwrap_or_else(|_| "无法获取错误详情".to_string());
            error!("微信API返回错误状态码 {}: {}", status, error_text);
            return Err(WechatServiceError::ApiError(format!("HTTP状态码: {}", status)));
        }
        
        let user_info: WechatUserInfoResponse = response.json().await?;
        
        // 检查微信API是否返回错误
        if let Some(errcode) = user_info.errcode {
            if errcode != 0 {
                let error_msg = user_info.errmsg.unwrap_or_else(|| "未知错误".to_string());
                error!("微信API返回错误: {} - {}", errcode, error_msg);
                return Err(WechatServiceError::ApiError(format!("错误码: {}, 错误信息: {}", errcode, error_msg)));
            }
        }
        
        info!("成功获取微信用户信息");
        Ok(user_info)
    }
}
