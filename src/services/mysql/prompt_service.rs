use std::sync::Arc;
use sqlx::{MySql, Pool};
use thiserror::Error;

use crate::{
    config::AppConfig,
    db::utils::mysql_helper,
    db::DbConnections,
    models::mysql::MySqlPrompt,
};

/// MySQL提示词服务错误类型
#[derive(Debug, Error)]
pub enum MySqlPromptServiceError {
    /// 数据库错误
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] sqlx::Error),

    /// 提示词不存在
    #[error("提示词不存在")]
    PromptNotFound,

    /// 提示词已存在
    #[error("提示词已存在")]
    PromptAlreadyExists,

    /// 数据库连接不可用
    #[error("数据库连接不可用")]
    DatabaseNotAvailable,

    /// 提示词标题无效
    #[error("提示词标题无效")]
    InvalidPromptTitle,

    /// 提示词内容无效
    #[error("提示词内容无效")]
    InvalidPromptContent,

    /// 提示词数量超过限制
    #[error("提示词数量已达上限，每个用户最多创建100个提示词")]
    PromptLimitExceeded,
}

/// MySQL提示词服务，处理提示词相关的业务逻辑
pub struct MySqlPromptService {
    pool: Arc<Pool<MySql>>,
}

impl MySqlPromptService {
    /**
     * 创建新的MySQL提示词服务实例
     *
     * @param db_connections 数据库连接管理器
     * @param config 应用配置
     * @return 返回 MySqlPromptService 实例或错误
     */
    pub fn new(
        db_connections: &DbConnections,
        _config: &Arc<AppConfig>,
    ) -> Result<Self, MySqlPromptServiceError> {
        // 获取MySQL连接池
        let pool = mysql_helper::get_mysql_pool(db_connections)
            .ok_or(MySqlPromptServiceError::DatabaseNotAvailable)?;

        Ok(Self {
            pool: Arc::new(pool.clone()),
        })
    }

    /**
     * 创建新提示词
     *
     * @param user_id 用户ID
     * @param title 提示词标题
     * @param content 提示词内容
     * @param category 提示词分类（可选）
     * @param tags 提示词标签（可选）
     * @param is_public 是否公开（可选，默认私有）
     * @return 成功返回新创建的提示词，失败返回错误
     */
    pub async fn create_prompt(
        &self,
        user_id: u64,
        title: String,
        content: String,
        category: Option<String>,
        tags: Option<String>,
        is_public: Option<i8>,
    ) -> Result<MySqlPrompt, MySqlPromptServiceError> {
        // 验证提示词标题
        if title.trim().is_empty() || title.len() > 255 {
            return Err(MySqlPromptServiceError::InvalidPromptTitle);
        }

        // 验证提示词内容
        if content.trim().is_empty() {
            return Err(MySqlPromptServiceError::InvalidPromptContent);
        }

        // 检查用户提示词数量是否已达上限（100个）
        let user_prompt_count = self.get_user_prompt_count(user_id).await?;
        if user_prompt_count >= 100 {
            return Err(MySqlPromptServiceError::PromptLimitExceeded);
        }

        // 检查提示词是否已存在（同一用户下标题不能重复）
        if self.prompt_exists(user_id, &title).await? {
            return Err(MySqlPromptServiceError::PromptAlreadyExists);
        }

        // 插入提示词
        let prompt_id = sqlx::query(
            "INSERT INTO prompts (user_id, title, content, category, tags, is_public) VALUES (?, ?, ?, ?, ?, ?)"
        )
        .bind(user_id)
        .bind(&title)
        .bind(&content)
        .bind(&category)
        .bind(&tags)
        .bind(is_public.unwrap_or(0))
        .execute(&*self.pool)
        .await?
        .last_insert_id();

        // 获取新创建的提示词
        let prompt = sqlx::query_as::<_, MySqlPrompt>(
            "SELECT * FROM prompts WHERE id = ?"
        )
        .bind(prompt_id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(prompt)
    }

    /**
     * 根据ID获取提示词
     *
     * @param prompt_id 提示词ID
     * @return 成功返回提示词，不存在返回None，失败返回错误
     */
    pub async fn get_prompt_by_id(&self, prompt_id: u64) -> Result<Option<MySqlPrompt>, MySqlPromptServiceError> {
        let prompt = sqlx::query_as::<_, MySqlPrompt>(
            "SELECT * FROM prompts WHERE id = ?"
        )
        .bind(prompt_id)
        .fetch_optional(&*self.pool)
        .await?;

        Ok(prompt)
    }

    /**
     * 获取用户的所有提示词
     *
     * @param user_id 用户ID
     * @param page 页码（从1开始）
     * @param page_size 每页大小
     * @return 成功返回提示词列表，失败返回错误
     */
    pub async fn get_user_prompts(&self, user_id: u64, page: u64, page_size: u64) -> Result<Vec<MySqlPrompt>, MySqlPromptServiceError> {
        let offset = (page - 1) * page_size;
        
        let prompts = sqlx::query_as::<_, MySqlPrompt>(
            "SELECT * FROM prompts WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?"
        )
        .bind(user_id)
        .bind(page_size)
        .bind(offset)
        .fetch_all(&*self.pool)
        .await?;

        Ok(prompts)
    }

    /**
     * 获取用户提示词总数
     *
     * @param user_id 用户ID
     * @return 成功返回提示词数量，失败返回错误
     */
    pub async fn get_user_prompt_count(&self, user_id: u64) -> Result<u64, MySqlPromptServiceError> {
        let count: (i64,) = sqlx::query_as(
            "SELECT COUNT(*) FROM prompts WHERE user_id = ?"
        )
        .bind(user_id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(count.0 as u64)
    }

    /**
     * 检查提示词是否存在
     *
     * @param user_id 用户ID
     * @param title 提示词标题
     * @return 成功返回是否存在，失败返回错误
     */
    async fn prompt_exists(&self, user_id: u64, title: &str) -> Result<bool, MySqlPromptServiceError> {
        let count: (i64,) = sqlx::query_as(
            "SELECT COUNT(*) FROM prompts WHERE user_id = ? AND title = ?"
        )
        .bind(user_id)
        .bind(title)
        .fetch_one(&*self.pool)
        .await?;

        Ok(count.0 > 0)
    }

    /**
     * 更新提示词
     *
     * @param prompt_id 提示词ID
     * @param title 新标题（可选）
     * @param content 新内容（可选）
     * @param category 新分类（可选）
     * @param tags 新标签（可选）
     * @param is_public 是否公开（可选）
     * @return 成功返回更新后的提示词，失败返回错误
     */
    pub async fn update_prompt(
        &self,
        prompt_id: u64,
        title: Option<String>,
        content: Option<String>,
        category: Option<String>,
        tags: Option<String>,
        is_public: Option<i8>,
    ) -> Result<MySqlPrompt, MySqlPromptServiceError> {
        // 检查提示词是否存在
        let _prompt = self.get_prompt_by_id(prompt_id).await?
            .ok_or(MySqlPromptServiceError::PromptNotFound)?;

        // 验证输入参数
        if let Some(ref title) = title {
            if title.trim().is_empty() || title.len() > 255 {
                return Err(MySqlPromptServiceError::InvalidPromptTitle);
            }
        }

        if let Some(ref content) = content {
            if content.trim().is_empty() {
                return Err(MySqlPromptServiceError::InvalidPromptContent);
            }
        }

        // 构建更新SQL - 使用简单的分别更新方式
        let mut has_updates = false;

        if let Some(title) = title {
            sqlx::query("UPDATE prompts SET title = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?")
                .bind(&title)
                .bind(prompt_id)
                .execute(&*self.pool)
                .await?;
            has_updates = true;
        }

        if let Some(content) = content {
            sqlx::query("UPDATE prompts SET content = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?")
                .bind(&content)
                .bind(prompt_id)
                .execute(&*self.pool)
                .await?;
            has_updates = true;
        }

        if let Some(category) = category {
            sqlx::query("UPDATE prompts SET category = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?")
                .bind(&category)
                .bind(prompt_id)
                .execute(&*self.pool)
                .await?;
            has_updates = true;
        }

        if let Some(tags) = tags {
            sqlx::query("UPDATE prompts SET tags = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?")
                .bind(&tags)
                .bind(prompt_id)
                .execute(&*self.pool)
                .await?;
            has_updates = true;
        }

        if let Some(is_public) = is_public {
            sqlx::query("UPDATE prompts SET is_public = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?")
                .bind(is_public)
                .bind(prompt_id)
                .execute(&*self.pool)
                .await?;
            has_updates = true;
        }

        if !has_updates {
            // 没有更新内容，直接返回原提示词
            return self.get_prompt_by_id(prompt_id).await?.ok_or(MySqlPromptServiceError::PromptNotFound);
        }

        // 获取更新后的提示词
        let updated_prompt = self.get_prompt_by_id(prompt_id).await?
            .ok_or(MySqlPromptServiceError::PromptNotFound)?;

        Ok(updated_prompt)
    }

    /**
     * 删除提示词
     *
     * @param prompt_id 提示词ID
     * @return 成功返回()，失败返回错误
     */
    pub async fn delete_prompt(&self, prompt_id: u64) -> Result<(), MySqlPromptServiceError> {
        // 检查提示词是否存在
        let _prompt = self.get_prompt_by_id(prompt_id).await?
            .ok_or(MySqlPromptServiceError::PromptNotFound)?;

        // 删除提示词
        sqlx::query("DELETE FROM prompts WHERE id = ?")
            .bind(prompt_id)
            .execute(&*self.pool)
            .await?;

        Ok(())
    }

    /**
     * 增加提示词使用次数
     *
     * @param prompt_id 提示词ID
     * @return 成功返回()，失败返回错误
     */
    pub async fn increment_usage_count(&self, prompt_id: u64) -> Result<(), MySqlPromptServiceError> {
        sqlx::query("UPDATE prompts SET usage_count = usage_count + 1 WHERE id = ?")
            .bind(prompt_id)
            .execute(&*self.pool)
            .await?;

        Ok(())
    }
}
