use std::sync::Arc;
use log::{debug, error};
use sqlx::{MySql, Pool};
use thiserror::Error;

use crate::models::mysql::MySqlNoteDraft;

/// MySQL笔记草稿服务错误
#[derive(Debug, Error)]
pub enum MySqlNoteDraftServiceError {
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] sqlx::Error),

    #[error("数据库不可用")]
    DatabaseNotAvailable,

    #[error("草稿不存在")]
    DraftNotFound,

    #[error("参数无效")]
    InvalidParameter,

    #[error("权限不足")]
    PermissionDenied,

    #[error("草稿已存在")]
    DraftAlreadyExists,
}

/// MySQL笔记草稿服务
pub struct MySqlNoteDraftService {
    /// MySQL连接池
    pool: Arc<Pool<MySql>>,
}

impl MySqlNoteDraftService {
    /// 创建新的MySQL笔记草稿服务实例
    pub fn new(pool: Arc<Pool<MySql>>) -> Self {
        Self { pool }
    }

    /**
     * 创建笔记草稿
     * 允许同一用户有多个草稿，但同一笔记只能有一个草稿
     *
     * @param note_id 笔记ID
     * @param user_id 用户ID
     * @param content 草稿内容
     * @return 成功返回新创建的草稿，失败返回错误
     */
    pub async fn create_draft(
        &self,
        note_id: u64,
        user_id: u64,
        content: Option<String>,
    ) -> Result<MySqlNoteDraft, MySqlNoteDraftServiceError> {
        // 检查该笔记是否已存在草稿
        if self.draft_exists(note_id).await? {
            return Err(MySqlNoteDraftServiceError::DraftAlreadyExists);
        }

        // 插入草稿（移除用户草稿限制检查，允许同一用户有多个草稿）
        let draft_id = sqlx::query(
            "INSERT INTO note_drafts (note_id, user_id, content) VALUES (?, ?, ?)"
        )
        .bind(note_id)
        .bind(user_id)
        .bind(&content)
        .execute(&*self.pool)
        .await?
        .last_insert_id();

        // 获取新创建的草稿
        let draft = sqlx::query_as::<_, MySqlNoteDraft>(
            "SELECT * FROM note_drafts WHERE id = ?"
        )
        .bind(draft_id)
        .fetch_one(&*self.pool)
        .await?;

        debug!("创建笔记草稿成功，草稿ID: {}", draft_id);
        Ok(draft)
    }

    /**
     * 创建或更新笔记草稿
     * 如果草稿已存在，则更新内容；如果不存在，则创建新草稿
     * 允许同一用户有多个草稿，但同一笔记只能有一个草稿
     *
     * @param note_id 笔记ID
     * @param user_id 用户ID
     * @param content 草稿内容
     * @return 成功返回草稿（创建或更新后的），失败返回错误
     */
    pub async fn create_or_update_draft(
        &self,
        note_id: u64,
        user_id: u64,
        content: Option<String>,
    ) -> Result<MySqlNoteDraft, MySqlNoteDraftServiceError> {
        // 检查该笔记是否已存在草稿
        if self.draft_exists(note_id).await? {
            // 如果草稿已存在，更新内容
            debug!("笔记草稿已存在，更新内容，笔记ID: {}", note_id);
            return self.update_draft(note_id, content).await;
        }

        // 插入新草稿（移除用户草稿限制检查，允许同一用户有多个草稿）
        let draft_id = sqlx::query(
            "INSERT INTO note_drafts (note_id, user_id, content) VALUES (?, ?, ?)"
        )
        .bind(note_id)
        .bind(user_id)
        .bind(&content)
        .execute(&*self.pool)
        .await?
        .last_insert_id();

        // 获取新创建的草稿
        let draft = sqlx::query_as::<_, MySqlNoteDraft>(
            "SELECT * FROM note_drafts WHERE id = ?"
        )
        .bind(draft_id)
        .fetch_one(&*self.pool)
        .await?;

        debug!("创建笔记草稿成功，草稿ID: {}", draft_id);
        Ok(draft)
    }

    /**
     * 根据笔记ID获取草稿
     *
     * @param note_id 笔记ID
     * @return 成功返回草稿，失败返回错误
     */
    pub async fn get_draft_by_note_id(
        &self,
        note_id: u64,
    ) -> Result<Option<MySqlNoteDraft>, MySqlNoteDraftServiceError> {
        let draft = sqlx::query_as::<_, MySqlNoteDraft>(
            "SELECT * FROM note_drafts WHERE note_id = ?"
        )
        .bind(note_id)
        .fetch_optional(&*self.pool)
        .await?;

        match draft {
            Some(draft) => {
                debug!("获取笔记草稿成功，笔记ID: {}", note_id);
                Ok(Some(draft))
            }
            None => {
                debug!("笔记草稿不存在，笔记ID: {}", note_id);
                Ok(None)
            }
        }
    }



    /**
     * 更新草稿内容
     *
     * @param note_id 笔记ID
     * @param content 新的草稿内容
     * @return 成功返回更新后的草稿，失败返回错误
     */
    pub async fn update_draft(
        &self,
        note_id: u64,
        content: Option<String>,
    ) -> Result<MySqlNoteDraft, MySqlNoteDraftServiceError> {
        // 更新草稿
        let rows_affected = sqlx::query(
            "UPDATE note_drafts SET content = ? WHERE note_id = ?"
        )
        .bind(&content)
        .bind(note_id)
        .execute(&*self.pool)
        .await?
        .rows_affected();

        if rows_affected == 0 {
            return Err(MySqlNoteDraftServiceError::DraftNotFound);
        }

        // 获取更新后的草稿
        let draft = self.get_draft_by_note_id(note_id).await?;
        debug!("更新笔记草稿成功，笔记ID: {}", note_id);
        match draft {
            Some(draft) => Ok(draft),
            None => Err(MySqlNoteDraftServiceError::DraftNotFound),
        }
    }

    /**
     * 删除草稿
     *
     * @param note_id 笔记ID
     * @return 成功返回true，失败返回错误
     */
    pub async fn delete_draft(
        &self,
        note_id: u64,
    ) -> Result<bool, MySqlNoteDraftServiceError> {
        let rows_affected = sqlx::query(
            "DELETE FROM note_drafts WHERE note_id = ?"
        )
        .bind(note_id)
        .execute(&*self.pool)
        .await?
        .rows_affected();

        if rows_affected == 0 {
            debug!("删除笔记草稿失败，草稿不存在，笔记ID: {}", note_id);
            return Err(MySqlNoteDraftServiceError::DraftNotFound);
        }

        debug!("删除笔记草稿成功，笔记ID: {}", note_id);
        Ok(true)
    }

    /**
     * 检查草稿是否存在
     *
     * @param note_id 笔记ID
     * @return 成功返回是否存在，失败返回错误
     */
    async fn draft_exists(&self, note_id: u64) -> Result<bool, MySqlNoteDraftServiceError> {
        let count: (i64,) = sqlx::query_as(
            "SELECT COUNT(*) FROM note_drafts WHERE note_id = ?"
        )
        .bind(note_id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(count.0 > 0)
    }



    /**
     * 获取用户的草稿列表（带分页）
     *
     * @param user_id 用户ID
     * @param page 页码，从1开始
     * @param page_size 每页数量
     * @return 成功返回草稿列表和总数，失败返回错误
     */
    pub async fn get_drafts_by_user_id(
        &self,
        user_id: u64,
        page: u32,
        page_size: u32,
    ) -> Result<(Vec<MySqlNoteDraft>, u64), MySqlNoteDraftServiceError> {
        // 计算偏移量
        let offset = (page - 1) * page_size;

        // 获取总数
        let total: (i64,) = sqlx::query_as(
            "SELECT COUNT(*) FROM note_drafts WHERE user_id = ?"
        )
        .bind(user_id)
        .fetch_one(&*self.pool)
        .await?;

        // 获取草稿列表
        let drafts = sqlx::query_as::<_, MySqlNoteDraft>(
            "SELECT * FROM note_drafts WHERE user_id = ? ORDER BY update_time DESC LIMIT ? OFFSET ?"
        )
        .bind(user_id)
        .bind(page_size)
        .bind(offset)
        .fetch_all(&*self.pool)
        .await?;

        debug!("获取用户草稿列表成功，用户ID: {}，共 {} 条", user_id, total.0);
        Ok((drafts, total.0 as u64))
    }
}
