use std::sync::Arc;
use sqlx::{MySql, Pool};
use thiserror::Error;

use crate::{
    config::AppConfig,
    db::utils::mysql_helper,
    db::DbConnections,
    models::mysql::{MySqlTag, TagWithCount},
};

/// MySQL标签服务错误类型
#[derive(Debug, Error)]
pub enum MySqlTagServiceError {
    /// 数据库错误
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] sqlx::Error),

    /// 标签不存在
    #[error("标签不存在")]
    TagNotFound,

    /// 标签已存在
    #[error("标签已存在")]
    TagAlreadyExists,

    /// 数据库连接不可用
    #[error("数据库连接不可用")]
    DatabaseNotAvailable,

    /// 标签名称无效
    #[error("标签名称无效")]
    InvalidTagName,

    /// 标签颜色无效
    #[error("标签颜色无效")]
    InvalidTagColor,

    /// 标签数量超过限制
    #[error("标签数量已达上限，每个用户最多创建50个标签")]
    TagLimitExceeded,
}

/// MySQL标签服务，处理标签相关的业务逻辑
pub struct MySqlTagService {
    pool: Arc<Pool<MySql>>,
}

impl MySqlTagService {
    /**
     * 创建新的MySQL标签服务实例
     *
     * @param db_connections 数据库连接管理器
     * @param config 应用配置
     * @return 返回 MySqlTagService 实例或错误
     */
    pub fn new(
        db_connections: &DbConnections,
        _config: &Arc<AppConfig>,
    ) -> Result<Self, MySqlTagServiceError> {
        // 获取MySQL连接池
        let pool = mysql_helper::get_mysql_pool(db_connections)
            .ok_or(MySqlTagServiceError::DatabaseNotAvailable)?;

        Ok(Self {
            pool: Arc::new(pool.clone()),
        })
    }

    /**
     * 创建新标签
     *
     * @param user_id 用户ID
     * @param name 标签名称
     * @param background_color 标签背景颜色（可选）
     * @param text_color 标签文字颜色（可选）
     * @return 成功返回新创建的标签，失败返回错误
     */
    pub async fn create_tag(
        &self,
        user_id: u64,
        name: String,
        background_color: Option<String>,
        text_color: Option<String>,
    ) -> Result<MySqlTag, MySqlTagServiceError> {
        use crate::utils::color::ColorUtils;

        // 验证标签名称
        if name.trim().is_empty() || name.len() > 50 {
            return Err(MySqlTagServiceError::InvalidTagName);
        }

        // 验证颜色格式
        let background_color = background_color.unwrap_or_else(ColorUtils::default_background_color);
        let text_color = text_color.unwrap_or_else(ColorUtils::default_text_color);

        if !ColorUtils::is_valid_hex_color(&background_color) {
            return Err(MySqlTagServiceError::InvalidTagColor);
        }
        if !ColorUtils::is_valid_hex_color(&text_color) {
            return Err(MySqlTagServiceError::InvalidTagColor);
        }

        // 检查用户标签数量是否已达上限（50个）
        let user_tag_count = self.get_user_tag_count(user_id).await?;
        if user_tag_count >= 50 {
            return Err(MySqlTagServiceError::TagLimitExceeded);
        }

        // 检查标签是否已存在
        if self.tag_exists(user_id, &name).await? {
            return Err(MySqlTagServiceError::TagAlreadyExists);
        }

        // 插入标签
        let tag_id = sqlx::query(
            "INSERT INTO tags (user_id, name, background_color, text_color) VALUES (?, ?, ?, ?)"
        )
        .bind(user_id)
        .bind(&name)
        .bind(&background_color)
        .bind(&text_color)
        .execute(&*self.pool)
        .await?
        .last_insert_id();

        // 获取新创建的标签
        let tag = sqlx::query_as::<_, MySqlTag>(
            "SELECT * FROM tags WHERE id = ?"
        )
        .bind(tag_id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(tag)
    }

    /**
     * 根据ID获取标签
     *
     * @param tag_id 标签ID
     * @return 成功返回标签，不存在返回None，失败返回错误
     */
    pub async fn get_tag_by_id(&self, tag_id: u64) -> Result<Option<MySqlTag>, MySqlTagServiceError> {
        let tag = sqlx::query_as::<_, MySqlTag>(
            "SELECT * FROM tags WHERE id = ?"
        )
        .bind(tag_id)
        .fetch_optional(&*self.pool)
        .await?;

        Ok(tag)
    }

    /**
     * 获取用户的所有标签
     *
     * @param user_id 用户ID
     * @return 成功返回标签列表，失败返回错误
     */
    pub async fn get_user_tags(&self, user_id: u64) -> Result<Vec<MySqlTag>, MySqlTagServiceError> {
        let tags = sqlx::query_as::<_, MySqlTag>(
            "SELECT * FROM tags WHERE user_id = ? ORDER BY name ASC"
        )
        .bind(user_id)
        .fetch_all(&*self.pool)
        .await?;

        Ok(tags)
    }

    /**
     * 获取用户标签数量
     *
     * @param user_id 用户ID
     * @return 成功返回标签数量，失败返回错误
     */
    pub async fn get_user_tag_count(&self, user_id: u64) -> Result<u64, MySqlTagServiceError> {
        let count: (i64,) = sqlx::query_as(
            "SELECT COUNT(*) FROM tags WHERE user_id = ?"
        )
        .bind(user_id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(count.0 as u64)
    }

    /**
     * 获取用户标签及使用统计
     *
     * @param user_id 用户ID
     * @return 成功返回标签及统计列表，失败返回错误
     */
    pub async fn get_user_tags_with_count(&self, user_id: u64) -> Result<Vec<TagWithCount>, MySqlTagServiceError> {
        // 先获取用户的所有标签
        let tags = self.get_user_tags(user_id).await?;

        // 为每个标签获取使用统计
        let mut tags_with_count = Vec::new();
        for tag in tags {
            let count: (i64,) = sqlx::query_as(
                "SELECT COUNT(*) FROM bookmark_tags WHERE tag_id = ?"
            )
            .bind(tag.id)
            .fetch_one(&*self.pool)
            .await?;

            tags_with_count.push(TagWithCount::new(tag, count.0 as u64));
        }

        // 按使用次数降序，名称升序排序
        tags_with_count.sort_by(|a, b| {
            match b.count.cmp(&a.count) {
                std::cmp::Ordering::Equal => a.tag.name.cmp(&b.tag.name),
                other => other,
            }
        });

        Ok(tags_with_count)
    }

    /**
     * 更新标签
     *
     * @param tag_id 标签ID
     * @param name 新名称（可选）
     * @param background_color 新背景颜色（可选）
     * @param text_color 新文字颜色（可选）
     * @return 成功返回更新后的标签，失败返回错误
     */
    pub async fn update_tag(
        &self,
        tag_id: u64,
        name: Option<String>,
        background_color: Option<String>,
        text_color: Option<String>,
    ) -> Result<MySqlTag, MySqlTagServiceError> {
        use crate::utils::color::ColorUtils;

        // 检查标签是否存在
        let _tag = self.get_tag_by_id(tag_id).await?
            .ok_or(MySqlTagServiceError::TagNotFound)?;

        // 验证输入参数
        if let Some(ref name) = name {
            if name.trim().is_empty() || name.len() > 50 {
                return Err(MySqlTagServiceError::InvalidTagName);
            }
        }

        if let Some(ref bg_color) = background_color {
            if !ColorUtils::is_valid_hex_color(bg_color) {
                return Err(MySqlTagServiceError::InvalidTagColor);
            }
        }

        if let Some(ref txt_color) = text_color {
            if !ColorUtils::is_valid_hex_color(txt_color) {
                return Err(MySqlTagServiceError::InvalidTagColor);
            }
        }

        // 根据提供的参数执行不同的更新
        match (name, background_color, text_color) {
            (Some(name), Some(bg_color), Some(txt_color)) => {
                // 更新所有字段
                sqlx::query("UPDATE tags SET name = ?, background_color = ?, text_color = ? WHERE id = ?")
                    .bind(&name)
                    .bind(&bg_color)
                    .bind(&txt_color)
                    .bind(tag_id)
                    .execute(&*self.pool)
                    .await?;
            },
            (Some(name), Some(bg_color), None) => {
                // 更新名称和背景颜色
                sqlx::query("UPDATE tags SET name = ?, background_color = ? WHERE id = ?")
                    .bind(&name)
                    .bind(&bg_color)
                    .bind(tag_id)
                    .execute(&*self.pool)
                    .await?;
            },
            (Some(name), None, Some(txt_color)) => {
                // 更新名称和文字颜色
                sqlx::query("UPDATE tags SET name = ?, text_color = ? WHERE id = ?")
                    .bind(&name)
                    .bind(&txt_color)
                    .bind(tag_id)
                    .execute(&*self.pool)
                    .await?;
            },
            (None, Some(bg_color), Some(txt_color)) => {
                // 更新背景颜色和文字颜色
                sqlx::query("UPDATE tags SET background_color = ?, text_color = ? WHERE id = ?")
                    .bind(&bg_color)
                    .bind(&txt_color)
                    .bind(tag_id)
                    .execute(&*self.pool)
                    .await?;
            },
            (Some(name), None, None) => {
                // 只更新名称
                sqlx::query("UPDATE tags SET name = ? WHERE id = ?")
                    .bind(&name)
                    .bind(tag_id)
                    .execute(&*self.pool)
                    .await?;
            },
            (None, Some(bg_color), None) => {
                // 只更新背景颜色
                sqlx::query("UPDATE tags SET background_color = ? WHERE id = ?")
                    .bind(&bg_color)
                    .bind(tag_id)
                    .execute(&*self.pool)
                    .await?;
            },
            (None, None, Some(txt_color)) => {
                // 只更新文字颜色
                sqlx::query("UPDATE tags SET text_color = ? WHERE id = ?")
                    .bind(&txt_color)
                    .bind(tag_id)
                    .execute(&*self.pool)
                    .await?;
            },
            (None, None, None) => {
                // 没有更新内容，直接返回原标签
                return self.get_tag_by_id(tag_id).await?.ok_or(MySqlTagServiceError::TagNotFound);
            }
        }

        // 获取更新后的标签
        let updated_tag = self.get_tag_by_id(tag_id).await?
            .ok_or(MySqlTagServiceError::TagNotFound)?;

        Ok(updated_tag)
    }

    /**
     * 删除标签
     *
     * @param tag_id 标签ID
     * @return 成功返回()，失败返回错误
     */
    pub async fn delete_tag(&self, tag_id: u64) -> Result<(), MySqlTagServiceError> {
        // 检查标签是否存在
        let _tag = self.get_tag_by_id(tag_id).await?
            .ok_or(MySqlTagServiceError::TagNotFound)?;

        // 删除标签（级联删除会自动删除相关的bookmark_tags记录）
        sqlx::query("DELETE FROM tags WHERE id = ?")
            .bind(tag_id)
            .execute(&*self.pool)
            .await?;

        Ok(())
    }

    /**
     * 搜索标签
     *
     * @param user_id 用户ID
     * @param keyword 搜索关键词
     * @return 成功返回匹配的标签列表，失败返回错误
     */
    pub async fn search_tags(&self, user_id: u64, keyword: &str) -> Result<Vec<MySqlTag>, MySqlTagServiceError> {
        let tags = sqlx::query_as::<_, MySqlTag>(
            "SELECT * FROM tags WHERE user_id = ? AND name LIKE ? ORDER BY name ASC LIMIT 20"
        )
        .bind(user_id)
        .bind(format!("%{}%", keyword))
        .fetch_all(&*self.pool)
        .await?;

        Ok(tags)
    }

    /**
     * 检查标签是否存在
     *
     * @param user_id 用户ID
     * @param name 标签名称
     * @return 存在返回true，不存在返回false
     */
    async fn tag_exists(&self, user_id: u64, name: &str) -> Result<bool, MySqlTagServiceError> {
        let count: (i64,) = sqlx::query_as(
            "SELECT COUNT(*) FROM tags WHERE user_id = ? AND name = ?"
        )
        .bind(user_id)
        .bind(name)
        .fetch_one(&*self.pool)
        .await?;

        Ok(count.0 > 0)
    }


}
