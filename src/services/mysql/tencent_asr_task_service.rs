use std::sync::Arc;
use sqlx::{MySql, Pool};
use thiserror::Error;
use log::{error, info};

use crate::models::mysql::{MySqlTencentAsrTask, AsrTaskStatus};

/// MySQL腾讯云语音识别任务服务错误
#[derive(Debug, Error)]
pub enum MySqlTencentAsrTaskServiceError {
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] sqlx::Error),

    #[error("任务不存在")]
    TaskNotFound,

    #[error("任务已存在")]
    TaskAlreadyExists,
}

/// MySQL腾讯云语音识别任务服务
pub struct MySqlTencentAsrTaskService {
    /// MySQL连接池
    pool: Arc<Pool<MySql>>,
}

impl MySqlTencentAsrTaskService {
    /// 创建新的MySQL腾讯云语音识别任务服务实例
    pub fn new(pool: Pool<MySql>) -> Self {
        Self {
            pool: Arc::new(pool),
        }
    }

    /// 创建新任务
    pub async fn create_task(&self, task_id: u64, request_id: String) -> Result<MySqlTencentAsrTask, MySqlTencentAsrTaskServiceError> {
        self.create_task_with_related(task_id, request_id, None).await
    }

    /// 创建新任务（带关联任务ID）
    pub async fn create_task_with_related(&self, task_id: u64, request_id: String, related_task_id: Option<u64>) -> Result<MySqlTencentAsrTask, MySqlTencentAsrTaskServiceError> {
        info!("创建腾讯云语音识别任务: task_id={}, request_id={}, related_task_id={:?}", task_id, request_id, related_task_id);

        // 检查任务是否已存在
        if self.task_exists(task_id).await? {
            return Err(MySqlTencentAsrTaskServiceError::TaskAlreadyExists);
        }

        let task = MySqlTencentAsrTask::new_with_related_task(task_id, request_id, related_task_id);

        let result = sqlx::query(
            r#"
            INSERT INTO tencent_asr_tasks (task_id, request_id, related_task_id, status, result, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(task.task_id)
        .bind(&task.request_id)
        .bind(task.related_task_id)
        .bind(i8::from(task.status))
        .bind(&task.result)
        .bind(task.created_at)
        .bind(task.updated_at)
        .execute(&*self.pool)
        .await?;

        let id = result.last_insert_id();
        self.get_task_by_id(id).await
    }

    /// 根据ID获取任务
    pub async fn get_task_by_id(&self, id: u64) -> Result<MySqlTencentAsrTask, MySqlTencentAsrTaskServiceError> {
        let task = sqlx::query_as::<_, MySqlTencentAsrTask>(
            "SELECT * FROM tencent_asr_tasks WHERE id = ?"
        )
        .bind(id)
        .fetch_optional(&*self.pool)
        .await?
        .ok_or(MySqlTencentAsrTaskServiceError::TaskNotFound)?;

        Ok(task)
    }

    /// 根据任务ID获取任务
    pub async fn get_task_by_task_id(&self, task_id: u64) -> Result<MySqlTencentAsrTask, MySqlTencentAsrTaskServiceError> {
        let task = sqlx::query_as::<_, MySqlTencentAsrTask>(
            "SELECT * FROM tencent_asr_tasks WHERE task_id = ?"
        )
        .bind(task_id)
        .fetch_optional(&*self.pool)
        .await?
        .ok_or(MySqlTencentAsrTaskServiceError::TaskNotFound)?;

        Ok(task)
    }

    /// 更新任务状态和结果
    pub async fn update_task_result(&self, task_id: u64, status: AsrTaskStatus, result: Option<String>) -> Result<MySqlTencentAsrTask, MySqlTencentAsrTaskServiceError> {
        info!("更新腾讯云语音识别任务结果: task_id={}, status={:?}", task_id, status);

        let now = chrono::Utc::now();
        
        sqlx::query(
            r#"
            UPDATE tencent_asr_tasks 
            SET status = ?, result = ?, updated_at = ?
            WHERE task_id = ?
            "#
        )
        .bind(i8::from(status))
        .bind(&result)
        .bind(now)
        .bind(task_id)
        .execute(&*self.pool)
        .await?;

        self.get_task_by_task_id(task_id).await
    }

    /// 检查任务是否存在
    pub async fn task_exists(&self, task_id: u64) -> Result<bool, MySqlTencentAsrTaskServiceError> {
        let count: (i64,) = sqlx::query_as(
            "SELECT COUNT(*) FROM tencent_asr_tasks WHERE task_id = ?"
        )
        .bind(task_id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(count.0 > 0)
    }

    /// 获取所有处理中的任务
    pub async fn get_processing_tasks(&self) -> Result<Vec<MySqlTencentAsrTask>, MySqlTencentAsrTaskServiceError> {
        let tasks = sqlx::query_as::<_, MySqlTencentAsrTask>(
            "SELECT * FROM tencent_asr_tasks WHERE status = ? ORDER BY created_at DESC"
        )
        .bind(i8::from(AsrTaskStatus::Processing))
        .fetch_all(&*self.pool)
        .await?;

        Ok(tasks)
    }
}
