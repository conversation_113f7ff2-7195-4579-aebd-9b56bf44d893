use std::sync::Arc;
use sqlx::{MySql, Pool};
use thiserror::Error;

use crate::{
    config::AppConfig,
    db::utils::mysql_helper,
    db::DbConnections,
    models::mysql::{MySqlFavorite, new_favorite_order},
};

/// MySQL收藏夹服务错误类型
#[derive(Debug, Error)]
pub enum MySqlFavoriteServiceError {
    /// 数据库错误
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] sqlx::Error),

    /// 收藏夹不存在
    #[error("收藏夹不存在")]
    FavoriteNotFound,

    /// 收藏夹已存在
    #[error("收藏夹已存在")]
    FavoriteAlreadyExists,

    /// 数据库连接不可用
    #[error("数据库连接不可用")]
    DatabaseNotAvailable,
}

/// MySQL收藏夹服务，处理收藏夹相关的业务逻辑
pub struct MySqlFavoriteService {
    pool: Arc<Pool<MySql>>,
}

impl MySqlFavoriteService {
    /**
     * 创建新的MySQL收藏夹服务实例
     *
     * @param db_connections 数据库连接管理器
     * @param config 应用配置
     * @return 返回 MySqlFavoriteService 实例或错误
     */
    pub fn new(db_connections: &DbConnections, _config: &Arc<AppConfig>) -> Result<Self, MySqlFavoriteServiceError> {
        // 获取MySQL连接池
        let pool = mysql_helper::get_mysql_pool(db_connections)
            .ok_or(MySqlFavoriteServiceError::DatabaseNotAvailable)?;

        Ok(Self {
            pool: Arc::new(pool.clone()),
        })
    }

    /**
     * 创建新收藏夹
     *
     * @param user_id 用户ID
     * @param name 收藏夹名称
     * @param cover 封面图片URL
     * @return 成功返回新创建的收藏夹，失败返回错误
     */
    pub async fn create_favorite(&self, user_id: u64, name: String, cover: String) -> Result<MySqlFavorite, MySqlFavoriteServiceError> {
        // 获取用户的所有收藏夹，用于设置新收藏夹的排序值
        let order = new_favorite_order();

        // 插入收藏夹
        let favorite_id = sqlx::query(
            "INSERT INTO favorites (user_id, name, cover, `order`) VALUES (?, ?, ?, ?)"
        )
        .bind(user_id)
        .bind(&name)
        .bind(&cover)
        .bind(order)
        .execute(&*self.pool)
        .await?
        .last_insert_id();

        // 获取新创建的收藏夹
        let favorite = sqlx::query_as::<_, MySqlFavorite>(
            "SELECT * FROM favorites WHERE id = ?"
        )
        .bind(favorite_id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(favorite)
    }

    /**
     * 获取用户的所有收藏夹
     *
     * @param user_id 用户ID
     * @return 成功返回收藏夹列表，失败返回错误
     */
    pub async fn get_user_favorites(&self, user_id: u64) -> Result<Vec<MySqlFavorite>, MySqlFavoriteServiceError> {
        let favorites = sqlx::query_as::<_, MySqlFavorite>(
            "SELECT * FROM favorites WHERE user_id = ? ORDER BY `order` ASC"
        )
        .bind(user_id)
        .fetch_all(&*self.pool)
        .await?;

        Ok(favorites)
    }

    /**
     * 获取用户的收藏夹列表（带分页）
     *
     * @param user_id 用户ID
     * @param page 页码，从1开始
     * @param page_size 每页数量
     * @return 成功返回收藏夹列表和总数，失败返回错误
     */
    pub async fn get_favorites_by_user_id(
        &self,
        user_id: u64,
        page: u32,
        page_size: u32
    ) -> Result<(Vec<MySqlFavorite>, u64), MySqlFavoriteServiceError> {
        // 计算偏移量
        let offset = (page - 1) * page_size;

        // 获取总数
        let total: (i64,) = sqlx::query_as(
            "SELECT COUNT(*) FROM favorites WHERE user_id = ?"
        )
        .bind(user_id)
        .fetch_one(&*self.pool)
        .await?;

        // 获取收藏夹列表
        let favorites = sqlx::query_as::<_, MySqlFavorite>(
            "SELECT * FROM favorites WHERE user_id = ? ORDER BY `order` ASC LIMIT ? OFFSET ?"
        )
        .bind(user_id)
        .bind(page_size)
        .bind(offset)
        .fetch_all(&*self.pool)
        .await?;

        Ok((favorites, total.0 as u64))
    }

    /**
     * 根据ID获取收藏夹
     *
     * @param id 收藏夹ID
     * @return 成功返回收藏夹选项（如果存在），失败返回错误
     */
    pub async fn get_favorite_by_id(&self, id: u64) -> Result<Option<MySqlFavorite>, MySqlFavoriteServiceError> {
        let favorite = sqlx::query_as::<_, MySqlFavorite>(
            "SELECT * FROM favorites WHERE id = ?"
        )
        .bind(id)
        .fetch_optional(&*self.pool)
        .await?;

        Ok(favorite)
    }

    /**
     * 更新收藏夹
     *
     * @param id 收藏夹ID
     * @param name 新名称（可选）
     * @param cover 新封面（可选）
     * @param order 新排序值（可选）
     * @return 成功返回更新后的收藏夹，失败返回错误
     */
    pub async fn update_favorite(
        &self,
        id: u64,
        name: Option<String>,
        cover: Option<String>,
        order: Option<i32>
    ) -> Result<MySqlFavorite, MySqlFavoriteServiceError> {
        // 检查收藏夹是否存在
        let _favorite = self.get_favorite_by_id(id).await?
            .ok_or(MySqlFavoriteServiceError::FavoriteNotFound)?;

        // 构建更新SQL
        let mut sql = String::from("UPDATE favorites SET ");
        let mut params = Vec::new();
        let mut has_updates = false;

        // 添加名称更新
        if let Some(name_value) = name {
            sql.push_str("name = ?");
            params.push(name_value);
            has_updates = true;
        }

        // 添加封面更新
        if let Some(cover_value) = cover {
            if has_updates {
                sql.push_str(", ");
            }
            sql.push_str("cover = ?");
            params.push(cover_value);
            has_updates = true;
        }

        // 添加排序值更新
        if let Some(_order_value) = order {
            if has_updates {
                sql.push_str(", ");
            }
            sql.push_str("`order` = ?");
            has_updates = true;
        }

        // 如果有更新，执行SQL
        if has_updates {
            // 完成SQL语句
            sql.push_str(" WHERE id = ?");

            // 执行更新
            let mut query = sqlx::query(&sql);

            // 绑定名称和封面参数（如果有）
            for param in params {
                query = query.bind(param);
            }

            // 绑定排序值（如果有）
            if let Some(order_value) = order {
                query = query.bind(order_value);
            }

            // 绑定ID
            query = query.bind(id);

            // 执行查询
            query.execute(&*self.pool).await?;
        }

        // 获取更新后的收藏夹
        let updated_favorite = sqlx::query_as::<_, MySqlFavorite>(
            "SELECT * FROM favorites WHERE id = ?"
        )
        .bind(id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(updated_favorite)
    }

    /**
     * 删除收藏夹
     *
     * @param id 收藏夹ID
     * @return 成功返回空元组，失败返回错误
     */
    pub async fn delete_favorite(&self, id: u64) -> Result<(), MySqlFavoriteServiceError> {
        // 检查收藏夹是否存在
        let _favorite = self.get_favorite_by_id(id).await?
            .ok_or(MySqlFavoriteServiceError::FavoriteNotFound)?;

        // 删除收藏夹
        sqlx::query(
            "DELETE FROM favorites WHERE id = ?"
        )
        .bind(id)
        .execute(&*self.pool)
        .await?;

        Ok(())
    }

    /**
     * 交换两个收藏夹的位置
     *
     * @param user_id 用户ID
     * @param source_id 源收藏夹ID
     * @param target_id 目标收藏夹ID
     * @return 成功返回空元组，失败返回错误
     */
    pub async fn swap_favorite_positions(
        &self,
        user_id: u64,
        source_id: u64,
        target_id: u64
    ) -> Result<(), MySqlFavoriteServiceError> {
        // 检查源收藏夹是否存在
        let source_favorite = self.get_favorite_by_id(source_id).await?
            .ok_or(MySqlFavoriteServiceError::FavoriteNotFound)?;

        // 检查目标收藏夹是否存在
        let target_favorite = self.get_favorite_by_id(target_id).await?
            .ok_or(MySqlFavoriteServiceError::FavoriteNotFound)?;

        // 确保两个收藏夹都属于同一个用户
        if source_favorite.user_id != user_id || target_favorite.user_id != user_id {
            return Err(MySqlFavoriteServiceError::FavoriteNotFound);
        }

        // 获取源收藏夹和目标收藏夹的排序值
        let source_order = source_favorite.order;
        let target_order = target_favorite.order;

        // 更新源收藏夹的排序值为目标收藏夹的排序值
        sqlx::query(
            "UPDATE favorites SET `order` = ? WHERE id = ?"
        )
        .bind(target_order)
        .bind(source_id)
        .execute(&*self.pool)
        .await?;

        // 更新目标收藏夹的排序值为源收藏夹的排序值
        sqlx::query(
            "UPDATE favorites SET `order` = ? WHERE id = ?"
        )
        .bind(source_order)
        .bind(target_id)
        .execute(&*self.pool)
        .await?;

        Ok(())
    }
}
