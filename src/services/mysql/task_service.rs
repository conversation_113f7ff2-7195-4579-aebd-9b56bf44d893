use std::sync::Arc;
use sqlx::{MySql, Pool};
use thiserror::Error;
use log::{error, info};

use crate::models::mysql::{MySqlTask, TaskType, TaskStatus};

/// MySQL任务服务错误
#[derive(Debug, Error)]
pub enum MySqlTaskServiceError {
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] sqlx::Error),

    #[error("任务不存在")]
    TaskNotFound,

    #[error("任务已存在")]
    TaskAlreadyExists,
}

/// MySQL任务服务
pub struct MySqlTaskService {
    /// MySQL连接池
    pool: Arc<Pool<MySql>>,
}

impl MySqlTaskService {
    /// 创建新的MySQL任务服务实例
    pub fn new(pool: Pool<MySql>) -> Self {
        Self {
            pool: Arc::new(pool),
        }
    }

    /// 创建新任务
    pub async fn create_task(&self, user_id: u64, task_type: TaskType, title: String, platform: String, url: String) -> Result<MySqlTask, MySqlTaskServiceError> {
        self.create_task_with_note_id(user_id, task_type, title, platform, url, None).await
    }

    /// 创建新任务（带笔记ID）
    pub async fn create_task_with_note_id(&self, user_id: u64, task_type: TaskType, title: String, platform: String, url: String, note_id: Option<u64>) -> Result<MySqlTask, MySqlTaskServiceError> {
        info!("创建任务: user_id={}, type={:?}, title={}, platform={}, url={}, note_id={:?}", user_id, task_type, title, platform, url, note_id);

        let task = MySqlTask::new_with_note_id(user_id, task_type, title, platform, url, note_id);

        let result = sqlx::query(
            r#"
            INSERT INTO tasks (user_id, task_type, title, platform, url, status, result, note_id, create_time, update_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(task.user_id)
        .bind(i8::from(task.task_type))
        .bind(&task.title)
        .bind(&task.platform)
        .bind(&task.url)
        .bind(i8::from(task.status))
        .bind(&task.result)
        .bind(task.note_id)
        .bind(task.create_time)
        .bind(task.update_time)
        .execute(&*self.pool)
        .await?;

        let id = result.last_insert_id();
        self.get_task_by_id(id).await
    }

    /// 根据ID获取任务
    pub async fn get_task_by_id(&self, id: u64) -> Result<MySqlTask, MySqlTaskServiceError> {
        let task = sqlx::query_as::<_, MySqlTask>(
            "SELECT * FROM tasks WHERE id = ?"
        )
        .bind(id)
        .fetch_optional(&*self.pool)
        .await?
        .ok_or(MySqlTaskServiceError::TaskNotFound)?;

        Ok(task)
    }

    /// 更新任务状态
    pub async fn update_task_status(&self, id: u64, status: TaskStatus) -> Result<MySqlTask, MySqlTaskServiceError> {
        info!("更新任务状态: id={}, status={:?}", id, status);

        let result = sqlx::query(
            "UPDATE tasks SET status = ?, update_time = NOW() WHERE id = ?"
        )
        .bind(i8::from(status))
        .bind(id)
        .execute(&*self.pool)
        .await?;

        if result.rows_affected() == 0 {
            return Err(MySqlTaskServiceError::TaskNotFound);
        }

        self.get_task_by_id(id).await
    }

    /// 根据状态获取任务列表
    pub async fn get_tasks_by_status(&self, status: TaskStatus) -> Result<Vec<MySqlTask>, MySqlTaskServiceError> {
        let tasks = sqlx::query_as::<_, MySqlTask>(
            "SELECT * FROM tasks WHERE status = ? ORDER BY create_time DESC"
        )
        .bind(i8::from(status))
        .fetch_all(&*self.pool)
        .await?;

        Ok(tasks)
    }

    /// 根据类型获取任务列表
    pub async fn get_tasks_by_type(&self, task_type: TaskType) -> Result<Vec<MySqlTask>, MySqlTaskServiceError> {
        let tasks = sqlx::query_as::<_, MySqlTask>(
            "SELECT * FROM tasks WHERE task_type = ? ORDER BY create_time DESC"
        )
        .bind(i8::from(task_type))
        .fetch_all(&*self.pool)
        .await?;

        Ok(tasks)
    }

    /// 根据平台获取任务列表
    pub async fn get_tasks_by_platform(&self, platform: &str) -> Result<Vec<MySqlTask>, MySqlTaskServiceError> {
        let tasks = sqlx::query_as::<_, MySqlTask>(
            "SELECT * FROM tasks WHERE platform = ? ORDER BY create_time DESC"
        )
        .bind(platform)
        .fetch_all(&*self.pool)
        .await?;

        Ok(tasks)
    }

    /// 获取所有任务
    pub async fn get_all_tasks(&self) -> Result<Vec<MySqlTask>, MySqlTaskServiceError> {
        let tasks = sqlx::query_as::<_, MySqlTask>(
            "SELECT * FROM tasks ORDER BY create_time DESC"
        )
        .fetch_all(&*self.pool)
        .await?;

        Ok(tasks)
    }

    /// 删除任务
    pub async fn delete_task(&self, id: u64) -> Result<(), MySqlTaskServiceError> {
        info!("删除任务: id={}", id);

        let result = sqlx::query("DELETE FROM tasks WHERE id = ?")
            .bind(id)
            .execute(&*self.pool)
            .await?;

        if result.rows_affected() == 0 {
            return Err(MySqlTaskServiceError::TaskNotFound);
        }

        Ok(())
    }

    /// 分页获取任务列表（按用户ID过滤）
    pub async fn get_tasks_with_pagination(
        &self,
        user_id: u64,
        page: u64,
        page_size: u64,
        task_type: Option<TaskType>,
        platform: Option<String>,
        status: Option<TaskStatus>
    ) -> Result<(Vec<MySqlTask>, u64), MySqlTaskServiceError> {
        info!("分页获取任务列表: user_id={}, page={}, page_size={}, task_type={:?}, platform={:?}, status={:?}",
              user_id, page, page_size, task_type, platform, status);

        // 构建WHERE条件和参数（始终包含用户ID过滤）
        let mut conditions = vec!["user_id = ?"];

        if task_type.is_some() {
            conditions.push("task_type = ?");
        }
        if platform.is_some() {
            conditions.push("platform = ?");
        }
        if status.is_some() {
            conditions.push("status = ?");
        }

        let where_clause = format!("WHERE {}", conditions.join(" AND "));
        let count_where_clause = where_clause.clone();

        // 查询总数
        let count_sql = format!("SELECT COUNT(*) FROM tasks {}", count_where_clause);
        let mut count_query = sqlx::query_scalar::<_, i64>(&count_sql);

        // 绑定用户ID（始终是第一个参数）
        count_query = count_query.bind(user_id);

        if let Some(t_type) = task_type {
            count_query = count_query.bind(i8::from(t_type));
        }
        if let Some(ref plat) = platform {
            count_query = count_query.bind(plat);
        }
        if let Some(stat) = status {
            count_query = count_query.bind(i8::from(stat));
        }

        let total = count_query.fetch_one(&*self.pool).await? as u64;

        // 查询数据
        let offset = (page - 1) * page_size;
        let data_sql = format!(
            "SELECT * FROM tasks {} ORDER BY create_time DESC LIMIT ? OFFSET ?",
            where_clause
        );

        let mut data_query = sqlx::query_as::<_, MySqlTask>(&data_sql);

        // 绑定用户ID（始终是第一个参数）
        data_query = data_query.bind(user_id);

        if let Some(t_type) = task_type {
            data_query = data_query.bind(i8::from(t_type));
        }
        if let Some(ref plat) = platform {
            data_query = data_query.bind(plat);
        }
        if let Some(stat) = status {
            data_query = data_query.bind(i8::from(stat));
        }

        data_query = data_query.bind(page_size as i64).bind(offset as i64);
        let tasks = data_query.fetch_all(&*self.pool).await?;

        Ok((tasks, total))
    }
}
