use std::sync::Arc;
use log::{error, info};
use sqlx::{MySql, Pool, Row};
use thiserror::Error;

use crate::models::mysql::{MySqlMaterial, MaterialType, MaterialStatus};

/// MySQL素材服务错误
#[derive(Debug, Error)]
pub enum MySqlMaterialServiceError {
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] sqlx::Error),

    #[error("素材不存在")]
    MaterialNotFound,

    #[error("无效的素材类型")]
    InvalidMaterialType,

    #[error("URL不能为空")]
    EmptyUrl,
}

/// MySQL素材服务
pub struct MySqlMaterialService {
    /// MySQL连接池
    pool: Arc<Pool<MySql>>,
}

impl MySqlMaterialService {
    /// 创建新的素材服务实例
    pub fn new(pool: Pool<MySql>) -> Self {
        Self {
            pool: Arc::new(pool),
        }
    }

    /**
     * 添加素材
     *
     * @param user_id 用户ID
     * @param url OSS地址
     * @param material_type 素材类型
     * @return 成功返回素材信息，失败返回错误
     */
    pub async fn add_material(
        &self,
        user_id: u64,
        url: String,
        material_type: MaterialType,
    ) -> Result<MySqlMaterial, MySqlMaterialServiceError> {
        // 验证URL不为空
        if url.trim().is_empty() {
            return Err(MySqlMaterialServiceError::EmptyUrl);
        }

        // 创建新素材
        let material = MySqlMaterial::new(user_id, url, material_type);

        // 插入数据库
        let result = sqlx::query(
            r#"
            INSERT INTO materials (user_id, url, create_time, update_time, status, type)
            VALUES (?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(material.user_id)
        .bind(&material.url)
        .bind(material.create_time)
        .bind(material.update_time)
        .bind(i8::from(material.status))
        .bind(i8::from(material.r#type))
        .execute(&*self.pool)
        .await?;

        let material_id = result.last_insert_id();

        // 获取插入的素材
        let inserted_material = self.get_material_by_id(material_id).await?
            .ok_or(MySqlMaterialServiceError::MaterialNotFound)?;

        info!("用户 {} 添加素材成功: {}", user_id, material_id);
        Ok(inserted_material)
    }

    /**
     * 根据ID获取素材
     *
     * @param id 素材ID
     * @return 成功返回素材信息，失败返回错误
     */
    pub async fn get_material_by_id(&self, id: u64) -> Result<Option<MySqlMaterial>, MySqlMaterialServiceError> {
        let material = sqlx::query_as::<_, MySqlMaterial>(
            "SELECT id, user_id, url, create_time, update_time, status, type FROM materials WHERE id = ?"
        )
        .bind(id)
        .fetch_optional(&*self.pool)
        .await?;

        Ok(material)
    }

    /**
     * 软删除素材
     *
     * @param id 素材ID
     * @param user_id 用户ID（确保只能删除自己的素材）
     * @return 成功返回()，失败返回错误
     */
    pub async fn delete_material(&self, id: u64, user_id: u64) -> Result<(), MySqlMaterialServiceError> {
        // 检查素材是否存在且属于当前用户
        let material = self.get_material_by_id(id).await?
            .ok_or(MySqlMaterialServiceError::MaterialNotFound)?;

        if material.user_id != user_id {
            return Err(MySqlMaterialServiceError::MaterialNotFound);
        }

        // 软删除：更新状态为已删除
        sqlx::query(
            "UPDATE materials SET status = ?, update_time = NOW() WHERE id = ?"
        )
        .bind(i8::from(MaterialStatus::Deleted))
        .bind(id)
        .execute(&*self.pool)
        .await?;

        info!("用户 {} 删除素材成功: {}", user_id, id);
        Ok(())
    }

    /**
     * 获取用户的素材列表
     *
     * @param user_id 用户ID
     * @param page 页码（从1开始）
     * @param page_size 每页数量
     * @param material_type 素材类型过滤（可选）
     * @return 成功返回素材列表和总数，失败返回错误
     */
    pub async fn get_user_materials(
        &self,
        user_id: u64,
        page: u32,
        page_size: u32,
        material_type: Option<MaterialType>,
    ) -> Result<(Vec<MySqlMaterial>, u64), MySqlMaterialServiceError> {
        let offset = (page - 1) * page_size;

        // 构建查询条件
        let (where_clause, count_where_clause) = if let Some(mat_type) = material_type {
            (
                "WHERE user_id = ? AND status = ? AND type = ?",
                "WHERE user_id = ? AND status = ? AND type = ?"
            )
        } else {
            (
                "WHERE user_id = ? AND status = ?",
                "WHERE user_id = ? AND status = ?"
            )
        };

        // 查询总数
        let count_query = format!("SELECT COUNT(*) as count FROM materials {}", count_where_clause);
        let mut count_query_builder = sqlx::query(&count_query)
            .bind(user_id)
            .bind(i8::from(MaterialStatus::Normal));

        if let Some(mat_type) = material_type {
            count_query_builder = count_query_builder.bind(i8::from(mat_type));
        }

        let total: u64 = count_query_builder
            .fetch_one(&*self.pool)
            .await?
            .get::<i64, _>("count") as u64;

        // 查询素材列表
        let list_query = format!(
            "SELECT id, user_id, url, create_time, update_time, status, type FROM materials {} ORDER BY create_time DESC LIMIT ? OFFSET ?",
            where_clause
        );
        let mut list_query_builder = sqlx::query_as::<_, MySqlMaterial>(&list_query)
            .bind(user_id)
            .bind(i8::from(MaterialStatus::Normal));

        if let Some(mat_type) = material_type {
            list_query_builder = list_query_builder.bind(i8::from(mat_type));
        }

        let materials = list_query_builder
            .bind(page_size)
            .bind(offset)
            .fetch_all(&*self.pool)
            .await?;

        Ok((materials, total))
    }

    /**
     * 验证素材类型
     *
     * @param type_value 类型值
     * @return 成功返回素材类型，失败返回错误
     */
    pub fn validate_material_type(type_value: i8) -> Result<MaterialType, MySqlMaterialServiceError> {
        match type_value {
            1 => Ok(MaterialType::Image),
            2 => Ok(MaterialType::Audio),
            3 => Ok(MaterialType::Video),
            _ => Err(MySqlMaterialServiceError::InvalidMaterialType),
        }
    }
}
