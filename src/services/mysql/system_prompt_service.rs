use std::sync::Arc;
use log::{info, error};
use sqlx::{MySql, Pool};
use thiserror::Error;

use crate::config::AppConfig;
use crate::models::mysql::MySqlSystemPrompt;

/// MySQL系统提示词服务错误
#[derive(Debug, Error)]
pub enum MySqlSystemPromptServiceError {
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] sqlx::Error),

    #[error("系统提示词不存在")]
    SystemPromptNotFound,

    #[error("系统提示词标题无效")]
    InvalidSystemPromptTitle,

    #[error("系统提示词内容无效")]
    InvalidSystemPromptContent,

    #[error("系统提示词已存在")]
    SystemPromptAlreadyExists,
}

/// MySQL系统提示词服务
pub struct MySqlSystemPromptService {
    /// MySQL连接池
    pool: Arc<Pool<MySql>>,
    /// 配置
    _config: AppConfig,
}

impl MySqlSystemPromptService {
    /// 创建新的MySQL系统提示词服务
    pub fn new(pool: Pool<MySql>, config: &AppConfig) -> Result<Self, MySqlSystemPromptServiceError> {
        Ok(Self {
            pool: Arc::new(pool),
            _config: config.clone(),
        })
    }

    /**
     * 创建新系统提示词
     *
     * @param title 系统提示词标题（可选）
     * @param content 系统提示词内容（可选）
     * @param category 系统提示词分类（可选）
     * @param tags 系统提示词标签（可选）
     * @param is_enabled 是否启用（可选，默认启用）
     * @param sort_weight 排序权重（可选，默认为0）
     * @return 成功返回新创建的系统提示词，失败返回错误
     */
    pub async fn create_system_prompt(
        &self,
        title: Option<String>,
        content: Option<String>,
        category: Option<String>,
        tags: Option<String>,
        is_enabled: Option<i8>,
        sort_weight: Option<i32>,
    ) -> Result<MySqlSystemPrompt, MySqlSystemPromptServiceError> {
        // 验证系统提示词标题（如果提供）
        if let Some(ref t) = title {
            if t.trim().is_empty() || t.len() > 255 {
                return Err(MySqlSystemPromptServiceError::InvalidSystemPromptTitle);
            }
        }

        // 验证系统提示词内容（如果提供）
        if let Some(ref c) = content {
            if c.trim().is_empty() {
                return Err(MySqlSystemPromptServiceError::InvalidSystemPromptContent);
            }
        }

        // 检查系统提示词是否已存在（如果提供标题）
        if let Some(ref t) = title {
            if self.system_prompt_exists(t).await? {
                return Err(MySqlSystemPromptServiceError::SystemPromptAlreadyExists);
            }
        }

        // 插入系统提示词
        let system_prompt_id = sqlx::query(
            "INSERT INTO system_prompts (title, content, category, tags, is_enabled, sort_weight) VALUES (?, ?, ?, ?, ?, ?)"
        )
        .bind(&title)
        .bind(&content)
        .bind(&category)
        .bind(&tags)
        .bind(is_enabled.unwrap_or(1))
        .bind(sort_weight.unwrap_or(0))
        .execute(&*self.pool)
        .await?
        .last_insert_id();

        // 获取新创建的系统提示词
        let system_prompt = sqlx::query_as::<_, MySqlSystemPrompt>(
            "SELECT * FROM system_prompts WHERE id = ?"
        )
        .bind(system_prompt_id)
        .fetch_one(&*self.pool)
        .await?;

        info!("创建系统提示词成功: {:?}", title);
        Ok(system_prompt)
    }

    /**
     * 检查系统提示词是否存在
     *
     * @param title 系统提示词标题
     * @return 存在返回true，不存在返回false
     */
    async fn system_prompt_exists(&self, title: &str) -> Result<bool, MySqlSystemPromptServiceError> {
        let count: (i64,) = sqlx::query_as(
            "SELECT COUNT(*) FROM system_prompts WHERE title = ?"
        )
        .bind(title)
        .fetch_one(&*self.pool)
        .await?;

        Ok(count.0 > 0)
    }

    /**
     * 根据ID获取系统提示词
     *
     * @param system_prompt_id 系统提示词ID
     * @return 成功返回系统提示词，失败返回错误
     */
    pub async fn get_system_prompt_by_id(&self, system_prompt_id: u64) -> Result<Option<MySqlSystemPrompt>, MySqlSystemPromptServiceError> {
        let system_prompt = sqlx::query_as::<_, MySqlSystemPrompt>(
            "SELECT * FROM system_prompts WHERE id = ?"
        )
        .bind(system_prompt_id)
        .fetch_optional(&*self.pool)
        .await?;

        Ok(system_prompt)
    }

    /**
     * 获取系统提示词列表
     *
     * @param page 页码（从1开始）
     * @param page_size 每页大小
     * @param category 分类过滤（可选）
     * @param is_enabled 是否启用过滤（可选）
     * @return 成功返回系统提示词列表，失败返回错误
     */
    pub async fn get_system_prompts(
        &self,
        page: u64,
        page_size: u64,
        category: Option<String>,
        is_enabled: Option<i8>,
    ) -> Result<Vec<MySqlSystemPrompt>, MySqlSystemPromptServiceError> {
        let offset = (page - 1) * page_size;
        let limit = page_size.min(100); // 最大100条



        // 根据不同条件执行不同查询
        let system_prompts = match (category, is_enabled) {
            (Some(cat), Some(enabled)) => {
                sqlx::query_as::<_, MySqlSystemPrompt>(
                    "SELECT * FROM system_prompts WHERE category = ? AND is_enabled = ? ORDER BY sort_weight DESC, created_at DESC LIMIT ? OFFSET ?"
                )
                .bind(cat)
                .bind(enabled)
                .bind(limit)
                .bind(offset)
                .fetch_all(&*self.pool)
                .await?
            },
            (Some(cat), None) => {
                sqlx::query_as::<_, MySqlSystemPrompt>(
                    "SELECT * FROM system_prompts WHERE category = ? ORDER BY sort_weight DESC, created_at DESC LIMIT ? OFFSET ?"
                )
                .bind(cat)
                .bind(limit)
                .bind(offset)
                .fetch_all(&*self.pool)
                .await?
            },
            (None, Some(enabled)) => {
                sqlx::query_as::<_, MySqlSystemPrompt>(
                    "SELECT * FROM system_prompts WHERE is_enabled = ? ORDER BY sort_weight DESC, created_at DESC LIMIT ? OFFSET ?"
                )
                .bind(enabled)
                .bind(limit)
                .bind(offset)
                .fetch_all(&*self.pool)
                .await?
            },
            (None, None) => {
                sqlx::query_as::<_, MySqlSystemPrompt>(
                    "SELECT * FROM system_prompts ORDER BY sort_weight DESC, created_at DESC LIMIT ? OFFSET ?"
                )
                .bind(limit)
                .bind(offset)
                .fetch_all(&*self.pool)
                .await?
            },
        };

        Ok(system_prompts)
    }

    /**
     * 获取系统提示词总数
     *
     * @param category 分类过滤（可选）
     * @param is_enabled 是否启用过滤（可选）
     * @return 成功返回总数，失败返回错误
     */
    pub async fn get_system_prompt_count(
        &self,
        category: Option<String>,
        is_enabled: Option<i8>,
    ) -> Result<u64, MySqlSystemPromptServiceError> {
        let count: (i64,) = match (category, is_enabled) {
            (Some(cat), Some(enabled)) => {
                sqlx::query_as(
                    "SELECT COUNT(*) FROM system_prompts WHERE category = ? AND is_enabled = ?"
                )
                .bind(cat)
                .bind(enabled)
                .fetch_one(&*self.pool)
                .await?
            },
            (Some(cat), None) => {
                sqlx::query_as(
                    "SELECT COUNT(*) FROM system_prompts WHERE category = ?"
                )
                .bind(cat)
                .fetch_one(&*self.pool)
                .await?
            },
            (None, Some(enabled)) => {
                sqlx::query_as(
                    "SELECT COUNT(*) FROM system_prompts WHERE is_enabled = ?"
                )
                .bind(enabled)
                .fetch_one(&*self.pool)
                .await?
            },
            (None, None) => {
                sqlx::query_as(
                    "SELECT COUNT(*) FROM system_prompts"
                )
                .fetch_one(&*self.pool)
                .await?
            },
        };

        Ok(count.0 as u64)
    }

    /**
     * 更新系统提示词
     *
     * @param system_prompt_id 系统提示词ID
     * @param title 新标题（可选）
     * @param content 新内容（可选）
     * @param category 新分类（可选）
     * @param tags 新标签（可选）
     * @param is_enabled 是否启用（可选）
     * @param sort_weight 排序权重（可选）
     * @return 成功返回更新后的系统提示词，失败返回错误
     */
    pub async fn update_system_prompt(
        &self,
        system_prompt_id: u64,
        title: Option<String>,
        content: Option<String>,
        category: Option<String>,
        tags: Option<String>,
        is_enabled: Option<i8>,
        sort_weight: Option<i32>,
    ) -> Result<MySqlSystemPrompt, MySqlSystemPromptServiceError> {
        // 检查系统提示词是否存在
        let _system_prompt = self.get_system_prompt_by_id(system_prompt_id).await?
            .ok_or(MySqlSystemPromptServiceError::SystemPromptNotFound)?;

        // 验证输入参数
        if let Some(ref title) = title {
            if title.trim().is_empty() || title.len() > 255 {
                return Err(MySqlSystemPromptServiceError::InvalidSystemPromptTitle);
            }
        }

        if let Some(ref content) = content {
            if content.trim().is_empty() {
                return Err(MySqlSystemPromptServiceError::InvalidSystemPromptContent);
            }
        }

        // 构建更新SQL
        let mut update_fields = Vec::new();
        let mut params: Vec<Box<dyn sqlx::Encode<'_, MySql> + Send + Sync>> = Vec::new();

        if let Some(t) = &title {
            update_fields.push("title = ?");
            params.push(Box::new(t.clone()));
        }

        if let Some(c) = &content {
            update_fields.push("content = ?");
            params.push(Box::new(c.clone()));
        }

        if let Some(cat) = &category {
            update_fields.push("category = ?");
            params.push(Box::new(cat.clone()));
        }

        if let Some(t) = &tags {
            update_fields.push("tags = ?");
            params.push(Box::new(t.clone()));
        }

        if let Some(enabled) = is_enabled {
            update_fields.push("is_enabled = ?");
            params.push(Box::new(enabled));
        }

        if let Some(weight) = sort_weight {
            update_fields.push("sort_weight = ?");
            params.push(Box::new(weight));
        }

        if update_fields.is_empty() {
            // 没有字段需要更新，直接返回原数据
            return self.get_system_prompt_by_id(system_prompt_id).await?
                .ok_or(MySqlSystemPromptServiceError::SystemPromptNotFound);
        }

        // 简化版本：根据不同字段组合执行更新
        // 这里为了简化，我们分别处理不同的更新情况
        if let (Some(t), Some(c)) = (&title, &content) {
            sqlx::query(
                "UPDATE system_prompts SET title = ?, content = ?, category = ?, tags = ?, is_enabled = ?, sort_weight = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
            )
            .bind(t)
            .bind(c)
            .bind(&category)
            .bind(&tags)
            .bind(is_enabled.unwrap_or(1))
            .bind(sort_weight.unwrap_or(0))
            .bind(system_prompt_id)
            .execute(&*self.pool)
            .await?;
        } else {
            // 获取当前数据，然后更新
            let current = self.get_system_prompt_by_id(system_prompt_id).await?
                .ok_or(MySqlSystemPromptServiceError::SystemPromptNotFound)?;

            sqlx::query(
                "UPDATE system_prompts SET title = ?, content = ?, category = ?, tags = ?, is_enabled = ?, sort_weight = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
            )
            .bind(title.or(current.title))
            .bind(content.or(current.content))
            .bind(category.or(current.category))
            .bind(tags.or(current.tags))
            .bind(is_enabled.unwrap_or(current.is_enabled))
            .bind(sort_weight.unwrap_or(current.sort_weight))
            .bind(system_prompt_id)
            .execute(&*self.pool)
            .await?;
        }

        // 获取更新后的系统提示词
        let updated_system_prompt = self.get_system_prompt_by_id(system_prompt_id).await?
            .ok_or(MySqlSystemPromptServiceError::SystemPromptNotFound)?;

        info!("更新系统提示词成功: {}", system_prompt_id);
        Ok(updated_system_prompt)
    }

    /**
     * 删除系统提示词
     *
     * @param system_prompt_id 系统提示词ID
     * @return 成功返回()，失败返回错误
     */
    pub async fn delete_system_prompt(&self, system_prompt_id: u64) -> Result<(), MySqlSystemPromptServiceError> {
        let result = sqlx::query(
            "DELETE FROM system_prompts WHERE id = ?"
        )
        .bind(system_prompt_id)
        .execute(&*self.pool)
        .await?;

        if result.rows_affected() == 0 {
            return Err(MySqlSystemPromptServiceError::SystemPromptNotFound);
        }

        info!("删除系统提示词成功: {}", system_prompt_id);
        Ok(())
    }

    /**
     * 增加系统提示词使用次数
     *
     * @param system_prompt_id 系统提示词ID
     * @return 成功返回()，失败返回错误
     */
    pub async fn increment_usage_count(&self, system_prompt_id: u64) -> Result<(), MySqlSystemPromptServiceError> {
        sqlx::query(
            "UPDATE system_prompts SET usage_count = usage_count + 1 WHERE id = ?"
        )
        .bind(system_prompt_id)
        .execute(&*self.pool)
        .await?;

        Ok(())
    }
}
