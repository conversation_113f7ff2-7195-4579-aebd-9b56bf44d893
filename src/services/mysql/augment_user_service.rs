use std::sync::Arc;
use chrono::{DateTime, Utc};
use sqlx::{MySql, Pool, Row};
use thiserror::Error;
use log::{info, error};

use crate::models::mysql::MySqlAugmentUser;

/// Augment用户服务错误
#[derive(Debug, Error)]
pub enum MySqlAugmentUserServiceError {
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] sqlx::Error),

    #[error("用户不存在")]
    UserNotFound,

    #[error("账号已存在")]
    AccountAlreadyExists,

    #[error("密码错误")]
    InvalidPassword,

    #[error("会员已过期")]
    MembershipExpired,
}

/// Augment用户服务
pub struct MySqlAugmentUserService {
    pool: Arc<Pool<MySql>>,
}

impl MySqlAugmentUserService {
    /// 创建新的Augment用户服务
    pub fn new(pool: Arc<Pool<MySql>>) -> Self {
        Self { pool }
    }

    /// 创建新用户
    pub async fn create_user(&self, account: String, password: String) -> Result<MySqlAugmentUser, MySqlAugmentUserServiceError> {
        // 检查账号是否已存在
        if self.account_exists(&account).await? {
            return Err(MySqlAugmentUserServiceError::AccountAlreadyExists);
        }

        let user = MySqlAugmentUser::new(account, password);

        let result = sqlx::query(
            r#"
            INSERT INTO augment_users (account, password, is_member, member_expire_time, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&user.account)
        .bind(&user.password)
        .bind(user.is_member)
        .bind(user.member_expire_time)
        .bind(user.created_at)
        .bind(user.updated_at)
        .execute(&*self.pool)
        .await?;

        let user_id = result.last_insert_id();
        self.get_user_by_id(user_id).await
    }

    /// 根据ID获取用户
    pub async fn get_user_by_id(&self, id: u64) -> Result<MySqlAugmentUser, MySqlAugmentUserServiceError> {
        let user = sqlx::query_as::<_, MySqlAugmentUser>(
            "SELECT * FROM augment_users WHERE id = ?"
        )
        .bind(id)
        .fetch_optional(&*self.pool)
        .await?;

        user.ok_or(MySqlAugmentUserServiceError::UserNotFound)
    }

    /// 根据账号获取用户
    pub async fn get_user_by_account(&self, account: &str) -> Result<MySqlAugmentUser, MySqlAugmentUserServiceError> {
        let user = sqlx::query_as::<_, MySqlAugmentUser>(
            "SELECT * FROM augment_users WHERE account = ?"
        )
        .bind(account)
        .fetch_optional(&*self.pool)
        .await?;

        user.ok_or(MySqlAugmentUserServiceError::UserNotFound)
    }

    /// 验证用户登录
    pub async fn verify_login(&self, account: &str, password: &str) -> Result<MySqlAugmentUser, MySqlAugmentUserServiceError> {
        let user = self.get_user_by_account(account).await?;
        
        // 这里应该使用密码哈希验证，暂时使用简单比较
        if user.password != password {
            return Err(MySqlAugmentUserServiceError::InvalidPassword);
        }

        Ok(user)
    }

    /// 设置用户会员状态
    pub async fn set_membership(&self, user_id: u64, is_member: bool, expire_time: Option<DateTime<Utc>>) -> Result<MySqlAugmentUser, MySqlAugmentUserServiceError> {
        let now = Utc::now();
        
        sqlx::query(
            r#"
            UPDATE augment_users 
            SET is_member = ?, member_expire_time = ?, updated_at = ?
            WHERE id = ?
            "#
        )
        .bind(is_member)
        .bind(expire_time)
        .bind(now)
        .bind(user_id)
        .execute(&*self.pool)
        .await?;

        self.get_user_by_id(user_id).await
    }

    /// 检查用户是否为有效会员
    pub async fn is_valid_member(&self, user_id: u64) -> Result<bool, MySqlAugmentUserServiceError> {
        let user = self.get_user_by_id(user_id).await?;
        Ok(user.is_valid_member())
    }

    /// 获取所有用户列表（分页）
    pub async fn list_users(&self, page: u32, page_size: u32) -> Result<Vec<MySqlAugmentUser>, MySqlAugmentUserServiceError> {
        let offset = (page - 1) * page_size;
        
        let users = sqlx::query_as::<_, MySqlAugmentUser>(
            "SELECT * FROM augment_users ORDER BY created_at DESC LIMIT ? OFFSET ?"
        )
        .bind(page_size)
        .bind(offset)
        .fetch_all(&*self.pool)
        .await?;

        Ok(users)
    }

    /// 获取用户总数
    pub async fn count_users(&self) -> Result<u64, MySqlAugmentUserServiceError> {
        let result = sqlx::query("SELECT COUNT(*) as count FROM augment_users")
            .fetch_one(&*self.pool)
            .await?;

        let count: i64 = result.get("count");
        Ok(count as u64)
    }

    /// 删除用户
    pub async fn delete_user(&self, user_id: u64) -> Result<(), MySqlAugmentUserServiceError> {
        let result = sqlx::query("DELETE FROM augment_users WHERE id = ?")
            .bind(user_id)
            .execute(&*self.pool)
            .await?;

        if result.rows_affected() == 0 {
            return Err(MySqlAugmentUserServiceError::UserNotFound);
        }

        info!("用户 {} 已删除", user_id);
        Ok(())
    }

    /// 更新用户密码
    pub async fn update_password(&self, user_id: u64, new_password: String) -> Result<MySqlAugmentUser, MySqlAugmentUserServiceError> {
        let now = Utc::now();
        
        let result = sqlx::query(
            "UPDATE augment_users SET password = ?, updated_at = ? WHERE id = ?"
        )
        .bind(&new_password)
        .bind(now)
        .bind(user_id)
        .execute(&*self.pool)
        .await?;

        if result.rows_affected() == 0 {
            return Err(MySqlAugmentUserServiceError::UserNotFound);
        }

        self.get_user_by_id(user_id).await
    }

    /// 检查账号是否存在
    async fn account_exists(&self, account: &str) -> Result<bool, MySqlAugmentUserServiceError> {
        let result = sqlx::query("SELECT COUNT(*) as count FROM augment_users WHERE account = ?")
            .bind(account)
            .fetch_one(&*self.pool)
            .await?;

        let count: i64 = result.get("count");
        Ok(count > 0)
    }
}
