use std::sync::Arc;
use sqlx::{MySql, Pool};
use thiserror::Error;

use crate::{
    config::AppConfig,
    db::utils::mysql_helper,
    db::DbConnections,
    models::mysql::MySqlAppVersion,
};

/// MySQL应用版本服务错误类型
#[derive(Debug, Error)]
pub enum MySqlAppVersionServiceError {
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] sqlx::Error),

    #[error("版本不存在")]
    VersionNotFound,

    #[error("版本已存在")]
    VersionAlreadyExists,

    #[error("数据库连接不可用")]
    DatabaseNotAvailable,
}

/// MySQL应用版本服务，处理版本相关的业务逻辑
pub struct MySqlAppVersionService {
    pool: Arc<Pool<MySql>>,
}

impl MySqlAppVersionService {
    /**
     * 创建新的MySQL应用版本服务实例
     *
     * @param db_connections 数据库连接管理器
     * @param config 应用配置
     * @return 返回 MySqlAppVersionService 实例或错误
     */
    pub fn new(db_connections: &DbConnections, _config: &Arc<AppConfig>) -> Result<Self, MySqlAppVersionServiceError> {
        // 获取MySQL连接池
        let pool = mysql_helper::get_mysql_pool(db_connections)
            .ok_or(MySqlAppVersionServiceError::DatabaseNotAvailable)?;

        Ok(Self {
            pool: Arc::new(pool.clone()),
        })
    }

    /**
     * 获取当前版本信息
     *
     * @return 成功返回当前版本模型，失败返回错误
     */
    pub async fn get_current_version(&self) -> Result<MySqlAppVersion, MySqlAppVersionServiceError> {
        let version = sqlx::query_as::<_, MySqlAppVersion>(
            "SELECT * FROM app_versions WHERE is_current = 1 ORDER BY created_at DESC LIMIT 1"
        )
        .fetch_optional(&*self.pool)
        .await?
        .ok_or(MySqlAppVersionServiceError::VersionNotFound)?;

        Ok(version)
    }

    /**
     * 存储新版本
     *
     * @param version 版本号
     * @param update_description 更新描述
     * @param is_current 是否设置为当前版本
     * @return 成功返回新创建的版本模型，失败返回错误
     */
    pub async fn store_version(
        &self,
        version: String,
        update_description: Vec<String>,
        is_current: bool,
    ) -> Result<MySqlAppVersion, MySqlAppVersionServiceError> {
        // 检查版本是否已存在
        let existing_version = sqlx::query_as::<_, MySqlAppVersion>(
            "SELECT * FROM app_versions WHERE version = ?"
        )
        .bind(&version)
        .fetch_optional(&*self.pool)
        .await?;

        if existing_version.is_some() {
            return Err(MySqlAppVersionServiceError::VersionAlreadyExists);
        }

        // 如果设置为当前版本，先将其他版本设置为非当前版本
        if is_current {
            sqlx::query("UPDATE app_versions SET is_current = 0")
                .execute(&*self.pool)
                .await?;
        }

        // 创建新版本
        let update_description_json = serde_json::Value::Array(
            update_description
                .into_iter()
                .map(serde_json::Value::String)
                .collect()
        );

        // 插入版本到数据库
        let version_id = sqlx::query(
            "INSERT INTO app_versions (version, update_description, is_current) VALUES (?, ?, ?)"
        )
        .bind(&version)
        .bind(&update_description_json)
        .bind(is_current)
        .execute(&*self.pool)
        .await?
        .last_insert_id();

        // 获取新创建的版本
        let new_version = sqlx::query_as::<_, MySqlAppVersion>(
            "SELECT * FROM app_versions WHERE id = ?"
        )
        .bind(version_id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(new_version)
    }

    /**
     * 根据版本号查找版本
     *
     * @param version 版本号
     * @return 成功返回版本模型选项（如果存在），失败返回错误
     */
    pub async fn find_by_version(&self, version: &str) -> Result<Option<MySqlAppVersion>, MySqlAppVersionServiceError> {
        let version_info = sqlx::query_as::<_, MySqlAppVersion>(
            "SELECT * FROM app_versions WHERE version = ?"
        )
        .bind(version)
        .fetch_optional(&*self.pool)
        .await?;

        Ok(version_info)
    }

    /**
     * 获取所有版本列表
     *
     * @return 成功返回版本列表，失败返回错误
     */
    pub async fn get_all_versions(&self) -> Result<Vec<MySqlAppVersion>, MySqlAppVersionServiceError> {
        let versions = sqlx::query_as::<_, MySqlAppVersion>(
            "SELECT * FROM app_versions ORDER BY created_at DESC"
        )
        .fetch_all(&*self.pool)
        .await?;

        Ok(versions)
    }

    /**
     * 设置指定版本为当前版本
     *
     * @param version_id 版本ID
     * @return 成功返回空元组，失败返回错误
     */
    pub async fn set_current_version(&self, version_id: u64) -> Result<(), MySqlAppVersionServiceError> {
        // 先将所有版本设置为非当前版本
        sqlx::query("UPDATE app_versions SET is_current = 0")
            .execute(&*self.pool)
            .await?;

        // 设置指定版本为当前版本
        let affected_rows = sqlx::query("UPDATE app_versions SET is_current = 1 WHERE id = ?")
            .bind(version_id)
            .execute(&*self.pool)
            .await?
            .rows_affected();

        if affected_rows == 0 {
            return Err(MySqlAppVersionServiceError::VersionNotFound);
        }

        Ok(())
    }
}
