use std::sync::Arc;
use sqlx::{MySql, Pool};
use thiserror::Error;

use crate::{
    config::AppConfig,
    db::utils::mysql_helper,
    db::DbConnections,
    models::mysql::{MySqlBookmark, MySqlTag, MySqlBookmarkTag, BookmarkTagDetail},
    services::mysql::{MySqlFavoriteService, MySqlTagService},
    enums::PlatformType,
};

/// MySQL书签服务错误类型
#[derive(Debug, Error)]
pub enum MySqlBookmarkServiceError {
    /// 数据库错误
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] sqlx::Error),

    /// 书签不存在
    #[error("书签不存在")]
    BookmarkNotFound,

    /// 书签已存在
    #[error("书签已存在")]
    BookmarkAlreadyExists,

    /// 数据库连接不可用
    #[error("数据库连接不可用")]
    DatabaseNotAvailable,

    /// 收藏夹不存在
    #[error("收藏夹不存在")]
    FavoriteNotFound,

    /// 收藏夹服务错误
    #[error("收藏夹服务错误: {0}")]
    FavoriteServiceError(#[from] crate::services::mysql::MySqlFavoriteServiceError),

    /// 标签服务错误
    #[error("标签服务错误: {0}")]
    TagServiceError(#[from] crate::services::mysql::MySqlTagServiceError),

    /// 标签不存在
    #[error("标签不存在")]
    TagNotFound,
}

/// MySQL书签服务，处理书签相关的业务逻辑
pub struct MySqlBookmarkService {
    pool: Arc<Pool<MySql>>,
    favorite_service: Option<Arc<MySqlFavoriteService>>,
    tag_service: Option<Arc<MySqlTagService>>,
}

impl MySqlBookmarkService {
    /**
     * 创建新的MySQL书签服务实例
     *
     * @param db_connections 数据库连接管理器
     * @param config 应用配置
     * @param favorite_service 收藏夹服务（可选）
     * @param tag_service 标签服务（可选）
     * @return 返回 MySqlBookmarkService 实例或错误
     */
    pub fn new(
        db_connections: &DbConnections,
        _config: &Arc<AppConfig>,
        favorite_service: Option<Arc<MySqlFavoriteService>>,
        tag_service: Option<Arc<MySqlTagService>>,
    ) -> Result<Self, MySqlBookmarkServiceError> {
        // 获取MySQL连接池
        let pool = mysql_helper::get_mysql_pool(db_connections)
            .ok_or(MySqlBookmarkServiceError::DatabaseNotAvailable)?;

        Ok(Self {
            pool: Arc::new(pool.clone()),
            favorite_service,
            tag_service,
        })
    }

    /**
     * 创建新书签
     *
     * @param user_id 用户ID
     * @param favorite_id 收藏夹ID
     * @param influencer_name 博主名称
     * @param influencer_avatar 博主头像
     * @param cover 封面
     * @param title 标题
     * @param desc 简介
     * @param scheme_url 原生跳转链接
     * @param tag_names 标签名称列表（可选，如果为空则创建默认标签）
     * @param platform_type 平台类型（可选）
     * @return 成功返回新创建的书签，失败返回错误
     */
    pub async fn create_bookmark(
        &self,
        user_id: u64,
        favorite_id: u64,
        influencer_name: String,
        influencer_avatar: Option<String>,
        cover: String,
        title: String,
        desc: String,
        scheme_url: String,
        tag_names: Option<Vec<String>>,
        platform_type: Option<PlatformType>,
    ) -> Result<MySqlBookmark, MySqlBookmarkServiceError> {
        // 处理标签：如果没有提供标签，则为空
        let final_tag_names = match tag_names {
            Some(tags) if !tags.is_empty() => Some(tags),
            _ => None // 没有提供标签或标签为空，保持为空
        };
        // 如果有收藏夹服务，检查收藏夹是否存在
        let mut update_favorite_cover = false;
        if let Some(favorite_service) = &self.favorite_service {
            let favorite_opt = favorite_service.get_favorite_by_id(favorite_id).await?;
            if let Some(favorite) = favorite_opt {
                // 检查收藏夹是否有封面，如果没有或为空字符串，则使用书签封面
                if favorite.cover.trim().is_empty() {
                    update_favorite_cover = true;
                    log::info!("收藏夹 {} 没有封面，将使用书签封面: {}", favorite_id, cover);
                }
            } else {
                return Err(MySqlBookmarkServiceError::FavoriteNotFound);
            }
        }

        // 插入书签
        let bookmark_id = sqlx::query(
            "INSERT INTO bookmarks (user_id, favorite_id, influencer_name, influencer_avatar, cover, title, `desc`, scheme_url, platform_type)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)"
        )
        .bind(user_id)
        .bind(favorite_id)
        .bind(&influencer_name)
        .bind(&influencer_avatar)
        .bind(&cover)
        .bind(&title)
        .bind(&desc)
        .bind(&scheme_url)
        .bind(&platform_type)
        .execute(&*self.pool)
        .await?
        .last_insert_id();

        // 获取新创建的书签
        let bookmark = sqlx::query_as::<_, MySqlBookmark>(
            "SELECT * FROM bookmarks WHERE id = ?"
        )
        .bind(bookmark_id)
        .fetch_one(&*self.pool)
        .await?;

        // 为书签添加标签（如果有标签的话）
        if let Some(tags) = final_tag_names {
            self.add_tags_to_bookmark(bookmark.id, tags, user_id).await?;
        }

        // 如果需要更新收藏夹封面
        if update_favorite_cover {
            if let Some(favorite_service) = &self.favorite_service {
                match favorite_service.update_favorite(
                    favorite_id,
                    None, // 不更新名称
                    Some(cover.clone()), // 更新封面
                    None, // 不更新排序值
                ).await {
                    Ok(_) => {
                        log::info!("已将书签封面设置为收藏夹 {} 的封面", favorite_id);
                    },
                    Err(e) => {
                        log::error!("更新收藏夹封面失败: {}", e);
                        // 不影响书签创建的结果
                    }
                }
            }
        }

        Ok(bookmark)
    }

    /**
     * 获取收藏夹中的所有书签
     *
     * @param user_id 用户ID
     * @param favorite_id 收藏夹ID
     * @return 成功返回书签列表，失败返回错误
     */
    pub async fn get_bookmarks_by_favorite(
        &self,
        user_id: u64,
        favorite_id: u64
    ) -> Result<Vec<MySqlBookmark>, MySqlBookmarkServiceError> {
        let bookmarks = sqlx::query_as::<_, MySqlBookmark>(
            "SELECT * FROM bookmarks WHERE user_id = ? AND favorite_id = ? ORDER BY created_at DESC"
        )
        .bind(user_id)
        .bind(favorite_id)
        .fetch_all(&*self.pool)
        .await?;

        Ok(bookmarks)
    }

    /**
     * 获取收藏夹中的书签（带分页）
     *
     * @param user_id 用户ID
     * @param favorite_id 收藏夹ID
     * @param page 页码，从1开始
     * @param page_size 每页数量
     * @return 成功返回书签列表和总数，失败返回错误
     */
    pub async fn get_bookmarks_by_favorite_id(
        &self,
        user_id: u64,
        favorite_id: u64,
        page: u32,
        page_size: u32
    ) -> Result<(Vec<MySqlBookmark>, u64), MySqlBookmarkServiceError> {
        self.get_bookmarks_by_favorite_id_with_platform_filter(user_id, favorite_id, page, page_size, None).await
    }

    /**
     * 获取收藏夹中的书签（带分页和平台类型过滤）
     *
     * @param user_id 用户ID
     * @param favorite_id 收藏夹ID
     * @param page 页码，从1开始
     * @param page_size 每页数量
     * @param platform_type 平台类型过滤（可选）
     * @return 成功返回书签列表和总数，失败返回错误
     */
    pub async fn get_bookmarks_by_favorite_id_with_platform_filter(
        &self,
        user_id: u64,
        favorite_id: u64,
        page: u32,
        page_size: u32,
        platform_type: Option<PlatformType>,
    ) -> Result<(Vec<MySqlBookmark>, u64), MySqlBookmarkServiceError> {
        // 计算偏移量
        let offset = (page - 1) * page_size;

        // 构建查询条件
        let (count_sql, data_sql) = if platform_type.is_some() {
            (
                "SELECT COUNT(*) FROM bookmarks WHERE user_id = ? AND favorite_id = ? AND platform_type = ?",
                "SELECT * FROM bookmarks WHERE user_id = ? AND favorite_id = ? AND platform_type = ? ORDER BY created_at DESC LIMIT ? OFFSET ?"
            )
        } else {
            (
                "SELECT COUNT(*) FROM bookmarks WHERE user_id = ? AND favorite_id = ?",
                "SELECT * FROM bookmarks WHERE user_id = ? AND favorite_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?"
            )
        };

        // 获取总数
        let total: (i64,) = if let Some(platform) = &platform_type {
            sqlx::query_as(count_sql)
                .bind(user_id)
                .bind(favorite_id)
                .bind(platform)
                .fetch_one(&*self.pool)
                .await?
        } else {
            sqlx::query_as(count_sql)
                .bind(user_id)
                .bind(favorite_id)
                .fetch_one(&*self.pool)
                .await?
        };

        // 获取书签列表
        let bookmarks = if let Some(platform) = &platform_type {
            sqlx::query_as::<_, MySqlBookmark>(data_sql)
                .bind(user_id)
                .bind(favorite_id)
                .bind(platform)
                .bind(page_size)
                .bind(offset)
                .fetch_all(&*self.pool)
                .await?
        } else {
            sqlx::query_as::<_, MySqlBookmark>(data_sql)
                .bind(user_id)
                .bind(favorite_id)
                .bind(page_size)
                .bind(offset)
                .fetch_all(&*self.pool)
                .await?
        };

        Ok((bookmarks, total.0 as u64))
    }

    /**
     * 根据ID获取书签
     *
     * @param id 书签ID
     * @return 成功返回书签选项（如果存在），失败返回错误
     */
    pub async fn get_bookmark_by_id(&self, id: u64) -> Result<Option<MySqlBookmark>, MySqlBookmarkServiceError> {
        let bookmark = sqlx::query_as::<_, MySqlBookmark>(
            "SELECT * FROM bookmarks WHERE id = ?"
        )
        .bind(id)
        .fetch_optional(&*self.pool)
        .await?;

        Ok(bookmark)
    }

    /**
     * 更新书签
     *
     * @param id 书签ID
     * @param title 新标题
     * @param desc 新简介
     * @return 成功返回更新后的书签，失败返回错误
     */
    pub async fn update_bookmark(
        &self,
        id: u64,
        title: String,
        desc: String
    ) -> Result<MySqlBookmark, MySqlBookmarkServiceError> {
        // 检查书签是否存在
        let _bookmark = self.get_bookmark_by_id(id).await?
            .ok_or(MySqlBookmarkServiceError::BookmarkNotFound)?;

        // 更新书签
        sqlx::query(
            "UPDATE bookmarks SET title = ?, `desc` = ? WHERE id = ?"
        )
        .bind(&title)
        .bind(&desc)
        .bind(id)
        .execute(&*self.pool)
        .await?;

        // 获取更新后的书签
        let updated_bookmark = sqlx::query_as::<_, MySqlBookmark>(
            "SELECT * FROM bookmarks WHERE id = ?"
        )
        .bind(id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(updated_bookmark)
    }

    /**
     * 获取收藏夹中最新的一个书签
     *
     * @param favorite_id 收藏夹ID
     * @return 成功返回书签选项（如果存在），失败返回错误
     */
    pub async fn get_latest_bookmark_in_favorite(&self, favorite_id: u64) -> Result<Option<MySqlBookmark>, MySqlBookmarkServiceError> {
        let bookmark = sqlx::query_as::<_, MySqlBookmark>(
            "SELECT * FROM bookmarks WHERE favorite_id = ? ORDER BY created_at DESC LIMIT 1"
        )
        .bind(favorite_id)
        .fetch_optional(&*self.pool)
        .await?;

        Ok(bookmark)
    }

    /**
     * 删除书签
     *
     * @param id 书签ID
     * @return 成功返回空元组，失败返回错误
     */
    pub async fn delete_bookmark(&self, id: u64) -> Result<(), MySqlBookmarkServiceError> {
        // 检查书签是否存在
        let bookmark = self.get_bookmark_by_id(id).await?
            .ok_or(MySqlBookmarkServiceError::BookmarkNotFound)?;

        let favorite_id = bookmark.favorite_id;
        let bookmark_cover = bookmark.cover.clone();

        // 检查是否需要更新收藏夹封面
        let mut need_update_favorite_cover = false;
        if let Some(favorite_service) = &self.favorite_service {
            if let Ok(Some(favorite)) = favorite_service.get_favorite_by_id(favorite_id).await {
                // 如果书签的封面与收藏夹的封面相同，则需要更新收藏夹封面
                if favorite.cover == bookmark_cover {
                    need_update_favorite_cover = true;
                    log::info!("删除的书签封面与收藏夹 {} 的封面相同，将更新收藏夹封面", favorite_id);
                }
            }
        }

        // 删除书签
        sqlx::query(
            "DELETE FROM bookmarks WHERE id = ?"
        )
        .bind(id)
        .execute(&*self.pool)
        .await?;

        // 如果需要更新收藏夹封面
        if need_update_favorite_cover && self.favorite_service.is_some() {
            // 查找收藏夹中最新的书签
            if let Ok(Some(latest_bookmark)) = self.get_latest_bookmark_in_favorite(favorite_id).await {
                // 使用最新书签的封面更新收藏夹封面
                if let Some(favorite_service) = &self.favorite_service {
                    match favorite_service.update_favorite(
                        favorite_id,
                        None, // 不更新名称
                        Some(latest_bookmark.cover.clone()), // 更新封面为最新书签的封面
                        None, // 不更新排序值
                    ).await {
                        Ok(_) => {
                            log::info!("已将收藏夹 {} 的封面更新为最新书签的封面", favorite_id);
                        },
                        Err(e) => {
                            log::error!("更新收藏夹封面失败: {}", e);
                            // 不影响书签删除的结果
                        }
                    }
                }
            } else {
                // 如果没有找到其他书签，将收藏夹封面设置为空
                if let Some(favorite_service) = &self.favorite_service {
                    match favorite_service.update_favorite(
                        favorite_id,
                        None, // 不更新名称
                        Some("".to_string()), // 设置空封面
                        None, // 不更新排序值
                    ).await {
                        Ok(_) => {
                            log::info!("收藏夹 {} 中没有其他书签，已将封面设置为空", favorite_id);
                        },
                        Err(e) => {
                            log::error!("更新收藏夹封面失败: {}", e);
                            // 不影响书签删除的结果
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /**
     * 删除收藏夹下的所有书签
     *
     * @param favorite_id 收藏夹ID
     * @return 成功返回删除的书签数量，失败返回错误
     */
    pub async fn delete_bookmarks_by_favorite_id(&self, favorite_id: u64) -> Result<u64, MySqlBookmarkServiceError> {
        // 删除书签
        let result = sqlx::query(
            "DELETE FROM bookmarks WHERE favorite_id = ?"
        )
        .bind(favorite_id)
        .execute(&*self.pool)
        .await?;

        Ok(result.rows_affected())
    }

    /**
     * 搜索书签
     *
     * @param user_id 用户ID
     * @param keyword 搜索关键词
     * @param page 页码，从1开始
     * @param page_size 每页数量
     * @return 成功返回书签列表和总数，失败返回错误
     */
    pub async fn search_bookmarks(
        &self,
        user_id: u64,
        keyword: &str,
        page: u32,
        page_size: u32
    ) -> Result<(Vec<MySqlBookmark>, u64), MySqlBookmarkServiceError> {
        // 如果关键词为空，返回空结果
        if keyword.trim().is_empty() {
            log::info!("用户 {} 搜索书签，关键词为空，返回空结果", user_id);
            return Ok((Vec::new(), 0));
        }

        // 计算偏移量
        let offset = (page - 1) * page_size;

        // 直接使用LIKE查询，跳过全文索引
        log::info!("用户 {} 搜索书签，关键词: '{}', 使用LIKE查询", user_id, keyword);
        self.search_bookmarks_like(user_id, keyword, page, page_size, offset).await
    }



    /// 使用LIKE查询书签
    async fn search_bookmarks_like(
        &self,
        user_id: u64,
        keyword: &str,
        _page: u32,
        page_size: u32,
        offset: u32
    ) -> Result<(Vec<MySqlBookmark>, u64), MySqlBookmarkServiceError> {
        // 构建搜索条件
        let search_condition = format!("%{}%", keyword);

        // 获取总数
        let total: (i64,) = sqlx::query_as(
            "SELECT COUNT(*) FROM bookmarks WHERE user_id = ? AND (title LIKE ? OR `desc` LIKE ?)"
        )
        .bind(user_id)
        .bind(&search_condition)
        .bind(&search_condition)
        .fetch_one(&*self.pool)
        .await?;

        // 获取书签列表
        let bookmarks = sqlx::query_as::<_, MySqlBookmark>(
            "SELECT * FROM bookmarks WHERE user_id = ? AND (title LIKE ? OR `desc` LIKE ?)
             ORDER BY created_at DESC LIMIT ? OFFSET ?"
        )
        .bind(user_id)
        .bind(&search_condition)
        .bind(&search_condition)
        .bind(page_size)
        .bind(offset)
        .fetch_all(&*self.pool)
        .await?;

        log::info!("用户 {} 使用LIKE查询书签，关键词: '{}', 找到 {} 条结果",
                  user_id, keyword, total.0);

        Ok((bookmarks, total.0 as u64))
    }

    /**
     * 获取书签的标签列表
     *
     * @param bookmark_ids 书签ID列表
     * @return 成功返回书签标签详情列表，失败返回错误
     */
    pub async fn get_tags_by_bookmark_ids(&self, bookmark_ids: &[u64]) -> Result<Vec<BookmarkTagDetail>, MySqlBookmarkServiceError> {
        if bookmark_ids.is_empty() {
            return Ok(Vec::new());
        }

        // 构建IN查询的占位符
        let placeholders = bookmark_ids.iter().map(|_| "?").collect::<Vec<_>>().join(",");
        let sql = format!(
            "SELECT bt.bookmark_id, bt.tag_id, t.name as tag_name,
                    t.background_color as tag_background_color, t.text_color as tag_text_color
             FROM bookmark_tags bt
             JOIN tags t ON bt.tag_id = t.id
             WHERE bt.bookmark_id IN ({})
             ORDER BY bt.bookmark_id, t.name",
            placeholders
        );

        let mut query = sqlx::query_as::<_, BookmarkTagDetail>(&sql);
        for bookmark_id in bookmark_ids {
            query = query.bind(bookmark_id);
        }

        let bookmark_tags = query.fetch_all(&*self.pool).await?;
        Ok(bookmark_tags)
    }

    /**
     * 为书签添加标签
     *
     * @param bookmark_id 书签ID
     * @param tag_names 标签名称列表
     * @param user_id 用户ID（用于创建不存在的标签）
     * @return 成功返回()，失败返回错误
     */
    pub async fn add_tags_to_bookmark(
        &self,
        bookmark_id: u64,
        tag_names: Vec<String>,
        user_id: u64,
    ) -> Result<(), MySqlBookmarkServiceError> {
        if tag_names.is_empty() {
            return Ok(()); // 如果没有标签，直接返回成功
        }

        let tag_service = self.tag_service.as_ref()
            .ok_or(MySqlBookmarkServiceError::TagNotFound)?;

        // 开始事务
        let mut tx = self.pool.begin().await?;

        for tag_name in tag_names {
            // 查找或创建标签
            let tag = match tag_service.search_tags(user_id, &tag_name).await? {
                tags if !tags.is_empty() && tags[0].name == tag_name => tags[0].clone(),
                _ => {
                    // 标签不存在，创建新标签
                    tag_service.create_tag(user_id, tag_name, None, None).await?
                }
            };

            // 检查书签标签关联是否已存在
            let exists: (i64,) = sqlx::query_as(
                "SELECT COUNT(*) FROM bookmark_tags WHERE bookmark_id = ? AND tag_id = ?"
            )
            .bind(bookmark_id)
            .bind(tag.id)
            .fetch_one(&mut *tx)
            .await?;

            if exists.0 == 0 {
                // 创建书签标签关联
                sqlx::query(
                    "INSERT INTO bookmark_tags (bookmark_id, tag_id) VALUES (?, ?)"
                )
                .bind(bookmark_id)
                .bind(tag.id)
                .execute(&mut *tx)
                .await?;
            }
        }

        // 提交事务
        tx.commit().await?;
        Ok(())
    }

    /**
     * 从书签移除标签
     *
     * @param bookmark_id 书签ID
     * @param tag_ids 标签ID列表
     * @return 成功返回()，失败返回错误
     */
    pub async fn remove_tags_from_bookmark(
        &self,
        bookmark_id: u64,
        tag_ids: Vec<u64>,
    ) -> Result<(), MySqlBookmarkServiceError> {
        if tag_ids.is_empty() {
            return Ok(());
        }

        // 构建IN查询的占位符
        let placeholders = tag_ids.iter().map(|_| "?").collect::<Vec<_>>().join(",");
        let sql = format!(
            "DELETE FROM bookmark_tags WHERE bookmark_id = ? AND tag_id IN ({})",
            placeholders
        );

        let mut query = sqlx::query(&sql).bind(bookmark_id);
        for tag_id in tag_ids {
            query = query.bind(tag_id);
        }

        query.execute(&*self.pool).await?;
        Ok(())
    }

    /**
     * 更新书签的标签
     *
     * @param bookmark_id 书签ID
     * @param tag_names 新的标签名称列表（可以为空）
     * @param user_id 用户ID
     * @return 成功返回()，失败返回错误
     */
    pub async fn update_bookmark_tags(
        &self,
        bookmark_id: u64,
        tag_names: Vec<String>,
        user_id: u64,
    ) -> Result<(), MySqlBookmarkServiceError> {
        // 开始事务
        let mut tx = self.pool.begin().await?;

        // 删除现有的标签关联
        sqlx::query("DELETE FROM bookmark_tags WHERE bookmark_id = ?")
            .bind(bookmark_id)
            .execute(&mut *tx)
            .await?;

        // 提交事务
        tx.commit().await?;

        // 添加新的标签（如果有的话）
        if !tag_names.is_empty() {
            self.add_tags_to_bookmark(bookmark_id, tag_names, user_id).await?;
        }
        Ok(())
    }

    /**
     * 按标签搜索书签（支持收藏夹过滤）
     *
     * @param user_id 用户ID
     * @param tag_names 标签名称列表
     * @param favorite_id 收藏夹ID（可选，如果为None则搜索全量书签）
     * @param page 页码，从1开始
     * @param page_size 每页数量
     * @return 成功返回书签列表和总数，失败返回错误
     */
    pub async fn search_bookmarks_by_tags(
        &self,
        user_id: u64,
        tag_names: &[String],
        favorite_id: Option<u64>,
        page: u32,
        page_size: u32
    ) -> Result<(Vec<MySqlBookmark>, u64), MySqlBookmarkServiceError> {
        if tag_names.is_empty() {
            log::info!("用户 {} 按标签搜索书签，标签列表为空，返回空结果", user_id);
            return Ok((Vec::new(), 0));
        }

        // 计算偏移量
        let offset = (page - 1) * page_size;

        // 构建标签名称的占位符
        let tag_placeholders = tag_names.iter().map(|_| "?").collect::<Vec<_>>().join(",");

        // 构建基础查询条件
        let mut base_conditions = format!(
            "b.user_id = ? AND bt.tag_id IN (
                SELECT t.id FROM tags t WHERE t.user_id = ? AND t.name IN ({})
            )",
            tag_placeholders
        );

        // 如果指定了收藏夹ID，添加收藏夹过滤条件
        if favorite_id.is_some() {
            base_conditions.push_str(" AND b.favorite_id = ?");
        }

        // 构建计数查询
        let count_sql = format!(
            "SELECT COUNT(DISTINCT b.id) FROM bookmarks b
             JOIN bookmark_tags bt ON b.id = bt.bookmark_id
             WHERE {}",
            base_conditions
        );

        // 构建数据查询
        let data_sql = format!(
            "SELECT DISTINCT b.* FROM bookmarks b
             JOIN bookmark_tags bt ON b.id = bt.bookmark_id
             WHERE {}
             ORDER BY b.created_at DESC LIMIT ? OFFSET ?",
            base_conditions
        );

        // 执行计数查询
        let mut count_query = sqlx::query_as::<_, (i64,)>(&count_sql)
            .bind(user_id)
            .bind(user_id);

        for tag_name in tag_names {
            count_query = count_query.bind(tag_name);
        }

        if let Some(fav_id) = favorite_id {
            count_query = count_query.bind(fav_id);
        }

        let total: (i64,) = count_query.fetch_one(&*self.pool).await?;

        // 执行数据查询
        let mut data_query = sqlx::query_as::<_, MySqlBookmark>(&data_sql)
            .bind(user_id)
            .bind(user_id);

        for tag_name in tag_names {
            data_query = data_query.bind(tag_name);
        }

        if let Some(fav_id) = favorite_id {
            data_query = data_query.bind(fav_id);
        }

        data_query = data_query.bind(page_size).bind(offset);

        let bookmarks = data_query.fetch_all(&*self.pool).await?;

        log::info!(
            "用户 {} 按标签 {:?} 搜索书签成功，收藏夹ID: {:?}，共找到 {} 个结果",
            user_id, tag_names, favorite_id, total.0
        );

        Ok((bookmarks, total.0 as u64))
    }

    /**
     * 获取用户的所有书签（带分页）
     *
     * @param user_id 用户ID
     * @param page 页码，从1开始
     * @param page_size 每页数量
     * @return 成功返回书签列表和总数，失败返回错误
     */
    pub async fn get_all_bookmarks_by_user(
        &self,
        user_id: u64,
        page: u32,
        page_size: u32
    ) -> Result<(Vec<MySqlBookmark>, u64), MySqlBookmarkServiceError> {
        self.get_all_bookmarks_by_user_with_platform_filter(user_id, page, page_size, None).await
    }

    /**
     * 获取用户的所有书签（带分页和平台类型过滤）
     *
     * @param user_id 用户ID
     * @param page 页码，从1开始
     * @param page_size 每页数量
     * @param platform_type 平台类型过滤（可选）
     * @return 成功返回书签列表和总数，失败返回错误
     */
    pub async fn get_all_bookmarks_by_user_with_platform_filter(
        &self,
        user_id: u64,
        page: u32,
        page_size: u32,
        platform_type: Option<PlatformType>,
    ) -> Result<(Vec<MySqlBookmark>, u64), MySqlBookmarkServiceError> {
        // 计算偏移量
        let offset = (page - 1) * page_size;

        // 构建查询条件
        let (count_sql, data_sql) = if platform_type.is_some() {
            (
                "SELECT COUNT(*) FROM bookmarks WHERE user_id = ? AND platform_type = ?",
                "SELECT * FROM bookmarks WHERE user_id = ? AND platform_type = ? ORDER BY created_at DESC LIMIT ? OFFSET ?"
            )
        } else {
            (
                "SELECT COUNT(*) FROM bookmarks WHERE user_id = ?",
                "SELECT * FROM bookmarks WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?"
            )
        };

        // 获取总数
        let total: (i64,) = if let Some(platform) = &platform_type {
            sqlx::query_as(count_sql)
                .bind(user_id)
                .bind(platform)
                .fetch_one(&*self.pool)
                .await?
        } else {
            sqlx::query_as(count_sql)
                .bind(user_id)
                .fetch_one(&*self.pool)
                .await?
        };

        // 获取书签列表
        let bookmarks = if let Some(platform) = &platform_type {
            sqlx::query_as::<_, MySqlBookmark>(data_sql)
                .bind(user_id)
                .bind(platform)
                .bind(page_size)
                .bind(offset)
                .fetch_all(&*self.pool)
                .await?
        } else {
            sqlx::query_as::<_, MySqlBookmark>(data_sql)
                .bind(user_id)
                .bind(page_size)
                .bind(offset)
                .fetch_all(&*self.pool)
                .await?
        };

        log::info!("用户 {} 获取所有书签成功，共 {} 个", user_id, total.0);

        Ok((bookmarks, total.0 as u64))
    }
}
