use serde::{Deserialize, Serialize};
use sqlx::{Type, Encode, Decode, MySql, database::HasValueRef, error::BoxDynError};

/// 平台类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum PlatformType {
    /// 小红书
    #[serde(rename = "xiaohongshu")]
    XiaoHongShu,

    /// 抖音
    #[serde(rename = "douyin")]
    DouYin,

    /// B站
    #[serde(rename = "bilibili")]
    BiliBili,

    /// 微信
    #[serde(rename = "wechat")]
    WeChat,

    /// 豆瓣
    #[serde(rename = "douban")]
    DouBan,

    /// 拼多多
    #[serde(rename = "pinduoduo")]
    PinDuoDuo,

    /// 淘宝
    #[serde(rename = "taobao")]
    TaoBao,

    /// 京东
    #[serde(rename = "jingdong")]
    JingDong,
}

impl PlatformType {
    /// 获取平台类型的显示名称
    pub fn display_name(&self) -> &'static str {
        match self {
            PlatformType::XiaoHongShu => "小红书",
            PlatformType::DouYin => "抖音",
            PlatformType::BiliBili => "B站",
            PlatformType::WeChat => "微信",
            PlatformType::DouBan => "豆瓣",
            PlatformType::PinDuoDuo => "拼多多",
            PlatformType::TaoBao => "淘宝",
            PlatformType::JingDong => "京东",
        }
    }

    /// 从字符串解析平台类型
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "xiaohongshu" => Some(PlatformType::XiaoHongShu),
            "douyin" => Some(PlatformType::DouYin),
            "bilibili" => Some(PlatformType::BiliBili),
            "wechat" => Some(PlatformType::WeChat),
            "douban" => Some(PlatformType::DouBan),
            "pinduoduo" => Some(PlatformType::PinDuoDuo),
            "taobao" => Some(PlatformType::TaoBao),
            "jingdong" => Some(PlatformType::JingDong),
            _ => None,
        }
    }

    /// 转换为字符串
    pub fn to_string(&self) -> String {
        match self {
            PlatformType::XiaoHongShu => "xiaohongshu".to_string(),
            PlatformType::DouYin => "douyin".to_string(),
            PlatformType::BiliBili => "bilibili".to_string(),
            PlatformType::WeChat => "wechat".to_string(),
            PlatformType::DouBan => "douban".to_string(),
            PlatformType::PinDuoDuo => "pinduoduo".to_string(),
            PlatformType::TaoBao => "taobao".to_string(),
            PlatformType::JingDong => "jingdong".to_string(),
        }
    }
}

impl Default for PlatformType {
    fn default() -> Self {
        PlatformType::XiaoHongShu
    }
}

// 实现sqlx的Type trait
impl Type<MySql> for PlatformType {
    fn type_info() -> <MySql as sqlx::Database>::TypeInfo {
        <String as Type<MySql>>::type_info()
    }
}

// 实现sqlx的Encode trait
impl<'q> Encode<'q, MySql> for PlatformType {
    fn encode_by_ref(&self, buf: &mut <MySql as sqlx::database::HasArguments<'q>>::ArgumentBuffer) -> sqlx::encode::IsNull {
        let value = match self {
            PlatformType::XiaoHongShu => "xiaohongshu",
            PlatformType::DouYin => "douyin",
            PlatformType::BiliBili => "bilibili",
            PlatformType::WeChat => "wechat",
            PlatformType::DouBan => "douban",
            PlatformType::PinDuoDuo => "pinduoduo",
            PlatformType::TaoBao => "taobao",
            PlatformType::JingDong => "jingdong",
        };
        <&str as Encode<'q, MySql>>::encode_by_ref(&value, buf)
    }
}

// 实现sqlx的Decode trait
impl<'r> Decode<'r, MySql> for PlatformType {
    fn decode(value: <MySql as HasValueRef<'r>>::ValueRef) -> Result<Self, BoxDynError> {
        let s = <String as Decode<MySql>>::decode(value)?;
        match s.as_str() {
            "xiaohongshu" => Ok(PlatformType::XiaoHongShu),
            "douyin" => Ok(PlatformType::DouYin),
            "bilibili" => Ok(PlatformType::BiliBili),
            "wechat" => Ok(PlatformType::WeChat),
            "douban" => Ok(PlatformType::DouBan),
            "pinduoduo" => Ok(PlatformType::PinDuoDuo),
            "taobao" => Ok(PlatformType::TaoBao),
            "jingdong" => Ok(PlatformType::JingDong),
            _ => Ok(PlatformType::XiaoHongShu), // 默认为小红书
        }
    }
}
