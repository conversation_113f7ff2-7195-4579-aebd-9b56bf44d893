use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// MySQL合集模型
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct MySqlCollection {
    /// 合集ID
    pub id: u64,
    
    /// 用户ID
    pub user_id: u64,
    
    /// 合集名称
    pub name: String,
    
    /// 合集封面
    pub cover: String,
    
    /// 合集描述
    pub desc: String,
    
    /// 创建时间
    pub create_time: DateTime<Utc>,
    
    /// 更新时间
    pub update_time: DateTime<Utc>,
}

impl MySqlCollection {
    /// 创建新合集
    pub fn new(user_id: u64, name: String, cover: String, desc: String) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            user_id,
            name,
            cover,
            desc,
            create_time: now,
            update_time: now,
        }
    }

    /// 获取表名
    pub fn table_name() -> &'static str {
        "collections"
    }
}
