use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// 系统提示词MySQL模型
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct MySqlSystemPrompt {
    /// 系统提示词ID
    pub id: u64,

    /// 系统提示词标题
    pub title: Option<String>,

    /// 系统提示词内容
    pub content: Option<String>,

    /// 系统提示词分类
    pub category: Option<String>,

    /// 系统提示词标签，多个标签用逗号分隔
    pub tags: Option<String>,

    /// 是否启用：0禁用 1启用
    pub is_enabled: i8,

    /// 排序权重，数值越大排序越靠前
    pub sort_weight: i32,

    /// 使用次数
    pub usage_count: u64,

    /// 创建时间
    pub created_at: DateTime<Utc>,

    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

impl MySqlSystemPrompt {
    /// 创建新系统提示词
    pub fn new(
        title: Option<String>,
        content: Option<String>,
        category: Option<String>,
        tags: Option<String>,
        is_enabled: Option<i8>,
        sort_weight: Option<i32>,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            title,
            content,
            category,
            tags,
            is_enabled: is_enabled.unwrap_or(1), // 默认启用
            sort_weight: sort_weight.unwrap_or(0), // 默认权重为0
            usage_count: 0,
            created_at: now,
            updated_at: now,
        }
    }

    /// 获取表名
    pub fn table_name() -> &'static str {
        "system_prompts"
    }
}
