use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// MySQL标签模型
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct MySqlTag {
    /// 标签ID
    pub id: u64,

    /// 标签名称
    pub name: String,

    /// 标签背景颜色（十六进制颜色值）
    pub background_color: String,

    /// 标签文字颜色（十六进制颜色值）
    pub text_color: String,

    /// 用户ID
    pub user_id: u64,

    /// 创建时间
    pub created_at: DateTime<Utc>,

    /// 最后更新时间
    pub updated_at: DateTime<Utc>,
}

impl MySqlTag {
    /// 创建新标签
    pub fn new(
        user_id: u64,
        name: String,
        background_color: Option<String>,
        text_color: Option<String>
    ) -> Self {
        use crate::utils::color::ColorUtils;

        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            user_id,
            name,
            background_color: background_color.unwrap_or_else(ColorUtils::default_background_color),
            text_color: text_color.unwrap_or_else(ColorUtils::default_text_color),
            created_at: now,
            updated_at: now,
        }
    }

    /// 获取表名
    pub fn table_name() -> &'static str {
        "tags"
    }
}

/// 标签统计信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TagWithCount {
    /// 标签信息
    #[serde(flatten)]
    pub tag: MySqlTag,

    /// 使用次数
    pub count: u64,
}

impl TagWithCount {
    /// 从标签和计数创建
    pub fn new(tag: MySqlTag, count: u64) -> Self {
        Self { tag, count }
    }
}
