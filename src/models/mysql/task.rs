use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// 任务类型枚举
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum TaskType {
    /// 提取文案
    ExtractContent = 1,
    /// 下载视频
    DownloadVideo = 2,
    /// 创建笔记
    CreateNote = 3,
    /// 更新笔记
    UpdateNote = 4,
}

impl From<i8> for TaskType {
    fn from(value: i8) -> Self {
        match value {
            1 => TaskType::ExtractContent,
            2 => TaskType::DownloadVideo,
            3 => TaskType::CreateNote,
            4 => TaskType::UpdateNote,
            _ => TaskType::ExtractContent, // 默认为提取文案
        }
    }
}

impl From<TaskType> for i8 {
    fn from(task_type: TaskType) -> Self {
        task_type as i8
    }
}

/// 任务状态枚举
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum TaskStatus {
    /// 新创建
    Created = 1,
    /// 已完成
    Completed = 2,
    /// 失败
    Failed = 3,
}

impl From<i8> for TaskStatus {
    fn from(value: i8) -> Self {
        match value {
            1 => TaskStatus::Created,
            2 => TaskStatus::Completed,
            3 => TaskStatus::Failed,
            _ => TaskStatus::Created, // 默认为新创建
        }
    }
}

impl From<TaskStatus> for i8 {
    fn from(status: TaskStatus) -> Self {
        status as i8
    }
}

/// MySQL任务模型
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct MySqlTask {
    /// 任务ID（主键）
    pub id: u64,

    /// 用户ID（外键）
    pub user_id: u64,

    /// 任务类型：1提取文案 2下载视频 3创建笔记 4更新笔记
    #[sqlx(try_from = "i8")]
    pub task_type: TaskType,

    /// 任务标题
    pub title: String,

    /// 平台信息
    pub platform: String,

    /// 提取的地址URL
    pub url: String,

    /// 任务状态：1新创建 2已完成 3失败
    #[sqlx(try_from = "i8")]
    pub status: TaskStatus,

    /// 任务结果
    pub result: Option<String>,

    /// 笔记ID（外键，可选，当task_type为4时存在）
    pub note_id: Option<u64>,

    /// 创建时间
    pub create_time: DateTime<Utc>,

    /// 更新时间
    pub update_time: DateTime<Utc>,
}

impl MySqlTask {
    /// 创建新任务
    pub fn new(user_id: u64, task_type: TaskType, title: String, platform: String, url: String) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            user_id,
            task_type,
            title,
            platform,
            url,
            status: TaskStatus::Created,
            result: None,
            note_id: None,
            create_time: now,
            update_time: now,
        }
    }

    /// 创建新任务（带笔记ID）
    pub fn new_with_note_id(user_id: u64, task_type: TaskType, title: String, platform: String, url: String, note_id: Option<u64>) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            user_id,
            task_type,
            title,
            platform,
            url,
            status: TaskStatus::Created,
            result: None,
            note_id,
            create_time: now,
            update_time: now,
        }
    }

    /// 更新任务状态
    pub fn update_status(&mut self, status: TaskStatus) {
        self.status = status;
        self.update_time = Utc::now();
    }

    /// 获取表名
    pub fn table_name() -> &'static str {
        "tasks"
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_task_type_update_note() {
        // 测试TaskType::UpdateNote的值
        assert_eq!(TaskType::UpdateNote as i8, 4);

        // 测试从i8转换为TaskType
        let task_type_from_i8: TaskType = 4i8.into();
        assert_eq!(task_type_from_i8, TaskType::UpdateNote);

        // 测试从TaskType转换为i8
        let i8_from_task_type: i8 = TaskType::UpdateNote.into();
        assert_eq!(i8_from_task_type, 4);
    }

    #[test]
    fn test_task_type_all_variants() {
        // 测试所有任务类型的值
        assert_eq!(TaskType::ExtractContent as i8, 1);
        assert_eq!(TaskType::DownloadVideo as i8, 2);
        assert_eq!(TaskType::CreateNote as i8, 3);
        assert_eq!(TaskType::UpdateNote as i8, 4);
    }

    #[test]
    fn test_task_type_from_i8_conversion() {
        // 测试所有有效的i8值转换
        assert_eq!(TaskType::from(1), TaskType::ExtractContent);
        assert_eq!(TaskType::from(2), TaskType::DownloadVideo);
        assert_eq!(TaskType::from(3), TaskType::CreateNote);
        assert_eq!(TaskType::from(4), TaskType::UpdateNote);

        // 测试无效值默认为ExtractContent
        assert_eq!(TaskType::from(0), TaskType::ExtractContent);
        assert_eq!(TaskType::from(5), TaskType::ExtractContent);
        assert_eq!(TaskType::from(-1), TaskType::ExtractContent);
    }

    #[test]
    fn test_task_type_equality() {
        // 测试任务类型相等性比较
        assert_eq!(TaskType::UpdateNote, TaskType::UpdateNote);
        assert_ne!(TaskType::UpdateNote, TaskType::CreateNote);
        assert_ne!(TaskType::UpdateNote, TaskType::DownloadVideo);
        assert_ne!(TaskType::UpdateNote, TaskType::ExtractContent);
    }
}
