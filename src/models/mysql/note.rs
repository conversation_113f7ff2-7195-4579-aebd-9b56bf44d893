use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// MySQL笔记模型
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct MySqlNote {
    /// 笔记ID
    pub id: u64,
    
    /// 合集ID（外键，可为空）
    pub parent_id: Option<u64>,
    
    /// 用户ID
    pub user_id: u64,
    
    /// 笔记标题
    pub title: String,
    
    /// 笔记封面
    pub cover: String,
    
    /// 笔记描述
    pub desc: String,
    
    /// 笔记内容（可选）
    pub content: Option<String>,

    /// HTML内容
    pub html: String,

    /// 创建时间
    pub create_time: DateTime<Utc>,
    
    /// 更新时间
    pub update_time: DateTime<Utc>,
}

impl MySqlNote {
    /// 创建新笔记
    pub fn new(
        parent_id: Option<u64>,
        user_id: u64,
        title: String,
        cover: String,
        desc: String,
        content: Option<String>,
        html: String,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            parent_id,
            user_id,
            title,
            cover,
            desc,
            content,
            html,
            create_time: now,
            update_time: now,
        }
    }

    /// 获取表名
    pub fn table_name() -> &'static str {
        "notes"
    }
}
