use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// 笔记状态枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum NoteStatus {
    /// 正常
    Normal = 1,
    /// 已删除
    Deleted = 2,
}

impl From<NoteStatus> for i8 {
    fn from(status: NoteStatus) -> Self {
        status as i8
    }
}

impl TryFrom<i8> for NoteStatus {
    type Error = String;

    fn try_from(value: i8) -> Result<Self, Self::Error> {
        match value {
            1 => Ok(NoteStatus::Normal),
            2 => Ok(NoteStatus::Deleted),
            _ => Err(format!("无效的笔记状态值: {}", value)),
        }
    }
}



/// MySQL笔记模型
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct MySqlNote {
    /// 笔记ID
    pub id: u64,
    
    /// 合集ID（外键，可为空）
    pub parent_id: Option<u64>,
    
    /// 用户ID
    pub user_id: u64,
    
    /// 笔记标题
    pub title: String,
    
    /// 笔记封面
    pub cover: String,
    
    /// 笔记描述
    pub desc: String,
    
    /// 笔记内容（可选）
    pub content: Option<String>,

    /// HTML内容
    pub html: String,

    /// 笔记状态：1正常 2已删除
    pub status: Option<i8>,

    /// 创建时间
    pub create_time: DateTime<Utc>,

    /// 更新时间
    pub update_time: DateTime<Utc>,
}

impl MySqlNote {
    /// 创建新笔记
    pub fn new(
        parent_id: Option<u64>,
        user_id: u64,
        title: String,
        cover: String,
        desc: String,
        content: Option<String>,
        html: String,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            parent_id,
            user_id,
            title,
            cover,
            desc,
            content,
            html,
            status: Some(1), // 默认为正常状态
            create_time: now,
            update_time: now,
        }
    }

    /// 获取表名
    pub fn table_name() -> &'static str {
        "notes"
    }
}
