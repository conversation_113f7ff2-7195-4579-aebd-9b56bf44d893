use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// MySQL收藏夹模型
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct MySqlFavorite {
    /// 收藏夹ID
    pub id: u64,
    
    /// 用户ID
    pub user_id: u64,
    
    /// 收藏夹名称
    pub name: String,
    
    /// 封面图片URL
    pub cover: String,
    
    /// 排序值，使用整数，默认为0
    /// 负数表示更高优先级（排在前面），正数或0表示正常优先级
    /// 新创建的收藏夹使用负数，确保排在最前面
    pub order: i32,
    
    /// 创建时间
    pub created_at: DateTime<Utc>,
    
    /// 最后更新时间
    pub updated_at: DateTime<Utc>,
    
    /// MongoDB的ObjectId，用于数据迁移
    pub mongo_id: Option<String>,
}

/// 默认排序值为0，表示正常优先级
#[allow(dead_code)]
pub fn default_order() -> i32 {
    0
}

/// 创建新收藏夹的默认排序值-1，表示比正常优先级更高一级
pub fn new_favorite_order() -> i32 {
    -1
}

impl MySqlFavorite {
    /// 创建新收藏夹
    pub fn new(user_id: u64, name: String, cover: String) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            user_id,
            name,
            cover,
            order: new_favorite_order(),
            created_at: now,
            updated_at: now,
            mongo_id: None,
        }
    }
    
    /// 获取表名
    pub fn table_name() -> &'static str {
        "favorites"
    }
}
