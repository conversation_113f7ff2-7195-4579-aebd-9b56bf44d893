use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// 素材状态枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum MaterialStatus {
    /// 正常
    Normal = 1,
    /// 已删除
    Deleted = 2,
}

impl From<i8> for MaterialStatus {
    fn from(value: i8) -> Self {
        match value {
            1 => MaterialStatus::Normal,
            2 => MaterialStatus::Deleted,
            _ => MaterialStatus::Normal, // 默认为正常状态
        }
    }
}

impl From<MaterialStatus> for i8 {
    fn from(status: MaterialStatus) -> Self {
        status as i8
    }
}

/// 素材类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum MaterialType {
    /// 图片
    Image = 1,
    /// 语音
    Audio = 2,
    /// 视频
    Video = 3,
}

impl From<i8> for MaterialType {
    fn from(value: i8) -> Self {
        match value {
            1 => MaterialType::Image,
            2 => MaterialType::Audio,
            3 => MaterialType::Video,
            _ => MaterialType::Image, // 默认为图片
        }
    }
}

impl From<MaterialType> for i8 {
    fn from(material_type: MaterialType) -> Self {
        material_type as i8
    }
}

/// MySQL素材模型
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct MySqlMaterial {
    /// 素材ID
    pub id: u64,

    /// 用户ID
    pub user_id: u64,

    /// OSS地址
    pub url: String,

    /// 创建时间
    pub create_time: DateTime<Utc>,

    /// 更新时间
    pub update_time: DateTime<Utc>,

    /// 状态：1正常 2已删除
    #[sqlx(try_from = "i8")]
    pub status: MaterialStatus,

    /// 类型：1图片 2语音 3视频
    #[sqlx(try_from = "i8")]
    pub r#type: MaterialType,
}

impl MySqlMaterial {
    /// 创建新素材
    pub fn new(
        user_id: u64,
        url: String,
        material_type: MaterialType,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            user_id,
            url,
            create_time: now,
            update_time: now,
            status: MaterialStatus::Normal,
            r#type: material_type,
        }
    }

    /// 获取表名
    pub fn table_name() -> &'static str {
        "materials"
    }
}
