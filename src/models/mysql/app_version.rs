use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// MySQL应用版本模型
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct MySqlAppVersion {
    /// 版本ID
    pub id: u64,

    /// 版本号
    pub version: String,

    /// 更新描述，JSON数组格式存储
    pub update_description: serde_json::Value,

    /// 是否为当前版本
    pub is_current: bool,

    /// 创建时间
    pub created_at: DateTime<Utc>,

    /// 最后更新时间
    pub updated_at: DateTime<Utc>,
}

impl MySqlAppVersion {
    /// 创建新版本
    pub fn new(version: String, update_description: Vec<String>) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            version,
            update_description: serde_json::Value::Array(
                update_description
                    .into_iter()
                    .map(serde_json::Value::String)
                    .collect()
            ),
            is_current: false, // 默认不是当前版本
            created_at: now,
            updated_at: now,
        }
    }

    /// 获取表名
    pub fn table_name() -> &'static str {
        "app_versions"
    }

    /// 获取更新描述为字符串数组
    pub fn get_update_description_as_strings(&self) -> Vec<String> {
        match &self.update_description {
            serde_json::Value::Array(arr) => {
                arr.iter()
                    .filter_map(|v| v.as_str())
                    .map(|s| s.to_string())
                    .collect()
            }
            _ => vec![]
        }
    }
}
