use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// MySQL书签标签关联模型
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct MySqlBookmarkTag {
    /// 关联ID
    pub id: u64,

    /// 书签ID
    pub bookmark_id: u64,

    /// 标签ID
    pub tag_id: u64,

    /// 创建时间
    pub created_at: DateTime<Utc>,
}

impl MySqlBookmarkTag {
    /// 创建新的书签标签关联
    pub fn new(bookmark_id: u64, tag_id: u64) -> Self {
        Self {
            id: 0, // 数据库会自动分配ID
            bookmark_id,
            tag_id,
            created_at: Utc::now(),
        }
    }

    /// 获取表名
    pub fn table_name() -> &'static str {
        "bookmark_tags"
    }
}

/// 书签标签详细信息（包含标签详情）
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct BookmarkTagDetail {
    /// 书签ID
    pub bookmark_id: u64,

    /// 标签ID
    pub tag_id: u64,

    /// 标签名称
    pub tag_name: String,

    /// 标签背景颜色
    pub tag_background_color: String,

    /// 标签文字颜色
    pub tag_text_color: String,
}
