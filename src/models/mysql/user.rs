use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::{FromRow, types::Json};

/// MySQL用户模型
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct MySqlUser {
    /// 用户ID
    pub id: u64,

    /// 手机号
    pub phone: String,

    /// 密码（应该存储哈希值而非明文）
    pub password: String,

    /// 创建时间
    pub created_at: DateTime<Utc>,

    /// 最后更新时间
    pub updated_at: DateTime<Utc>,

    /// MongoDB的ObjectId，用于数据迁移
    pub mongo_id: Option<String>,

    /// 微信OpenID
    pub openid: Option<String>,

    /// 微信统一ID
    pub unionid: Option<String>,

    /// 用户昵称
    pub nickname: Option<String>,

    /// 用户性别（1为男性，2为女性，0为未知）
    pub sex: Option<i8>,

    /// 用户所在省份
    pub province: Option<String>,

    /// 用户所在城市
    pub city: Option<String>,

    /// 用户所在国家
    pub country: Option<String>,

    /// 用户头像URL
    pub headimgurl: Option<String>,

    /// 用户特权信息，JSON格式
    #[serde(skip_serializing_if = "Option::is_none")]
    pub privilege: Option<serde_json::Value>,

    /// 用户头像（兼容旧字段）
    pub avatar: Option<String>,
}

impl MySqlUser {
    /// 创建新用户
    pub fn new(phone: String, password: String) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            phone,
            password,
            created_at: now,
            updated_at: now,
            mongo_id: None,
            openid: None,
            unionid: None,
            nickname: None,
            sex: None,
            province: None,
            city: None,
            country: None,
            headimgurl: None,
            privilege: None,
            avatar: None,
        }
    }

    /// 创建微信用户
    pub fn new_wechat_user(
        phone: String,
        password: String,
        openid: Option<String>,
        unionid: String,
        nickname: String,
        sex: Option<i8>,
        province: Option<String>,
        city: Option<String>,
        country: Option<String>,
        headimgurl: Option<String>,
        privilege: Option<serde_json::Value>,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            phone,
            password,
            created_at: now,
            updated_at: now,
            mongo_id: None,
            openid,
            unionid: Some(unionid),
            nickname: Some(nickname),
            sex,
            province,
            city,
            country,
            headimgurl: headimgurl.clone(),
            privilege,
            avatar: headimgurl, // 兼容旧字段，使用headimgurl作为avatar
        }
    }

    /// 创建简化版微信用户（兼容旧接口）
    pub fn new_simple_wechat_user(phone: String, password: String, unionid: String, nickname: String, avatar: Option<String>) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            phone,
            password,
            created_at: now,
            updated_at: now,
            mongo_id: None,
            openid: None,
            unionid: Some(unionid),
            nickname: Some(nickname),
            sex: None,
            province: None,
            city: None,
            country: None,
            headimgurl: avatar.clone(),
            privilege: None,
            avatar,
        }
    }

    /// 获取表名
    pub fn table_name() -> &'static str {
        "users"
    }
}
