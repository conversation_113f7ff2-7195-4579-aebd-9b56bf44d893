use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use crate::enums::PlatformType;

/// MySQL书签模型
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct MySqlBookmark {
    /// 书签ID
    pub id: u64,

    /// 用户ID
    pub user_id: u64,

    /// 收藏夹ID
    pub favorite_id: u64,

    /// 博主名称
    pub influencer_name: String,

    /// 博主头像
    pub influencer_avatar: Option<String>,

    /// 封面
    pub cover: String,

    /// 标题
    pub title: String,

    /// 简介
    pub desc: String,

    /// 原生跳转链接
    pub scheme_url: String,

    /// 创建时间
    pub created_at: DateTime<Utc>,

    /// 最后更新时间
    pub updated_at: DateTime<Utc>,

    /// MongoDB的ObjectId，用于数据迁移
    pub mongo_id: Option<String>,

    /// 平台类型（可选）
    pub platform_type: Option<PlatformType>,
}

impl MySqlBookmark {
    /// 创建新书签
    pub fn new(
        user_id: u64,
        favorite_id: u64,
        influencer_name: String,
        influencer_avatar: Option<String>,
        cover: String,
        title: String,
        desc: String,
        scheme_url: String,
        platform_type: Option<PlatformType>,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            user_id,
            favorite_id,
            influencer_name,
            influencer_avatar,
            cover,
            title,
            desc,
            scheme_url,
            created_at: now,
            updated_at: now,
            mongo_id: None,
            platform_type,
        }
    }

    /// 获取表名
    pub fn table_name() -> &'static str {
        "bookmarks"
    }
}
