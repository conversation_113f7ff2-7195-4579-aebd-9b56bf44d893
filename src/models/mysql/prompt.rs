use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// MySQL提示词模型
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct MySqlPrompt {
    /// 提示词ID
    pub id: u64,

    /// 用户ID
    pub user_id: u64,

    /// 提示词标题
    pub title: String,

    /// 提示词内容
    pub content: String,

    /// 提示词分类（可选）
    pub category: Option<String>,

    /// 提示词标签（可选）
    pub tags: Option<String>,

    /// 是否公开（0私有，1公开）
    pub is_public: i8,

    /// 使用次数
    pub usage_count: u64,

    /// 创建时间
    pub created_at: DateTime<Utc>,

    /// 最后更新时间
    pub updated_at: DateTime<Utc>,
}

impl MySqlPrompt {
    /// 创建新提示词
    pub fn new(
        user_id: u64,
        title: String,
        content: String,
        category: Option<String>,
        tags: Option<String>,
        is_public: Option<i8>,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            user_id,
            title,
            content,
            category,
            tags,
            is_public: is_public.unwrap_or(0), // 默认私有
            usage_count: 0,
            created_at: now,
            updated_at: now,
        }
    }

    /// 获取表名
    pub fn table_name() -> &'static str {
        "prompts"
    }
}
