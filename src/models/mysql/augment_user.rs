use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// Augment用户模型
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct MySqlAugmentUser {
    /// 用户ID
    pub id: u64,

    /// 账号
    pub account: String,

    /// 密码（应该存储哈希值而非明文）
    pub password: String,

    /// 是否是会员
    pub is_member: bool,

    /// 会员到期时间
    pub member_expire_time: Option<DateTime<Utc>>,

    /// 创建时间
    pub created_at: DateTime<Utc>,

    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

impl MySqlAugmentUser {
    /// 创建新的Augment用户
    pub fn new(account: String, password: String) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            account,
            password,
            is_member: false,
            member_expire_time: None,
            created_at: now,
            updated_at: now,
        }
    }

    /// 创建新的会员用户
    pub fn new_member(account: String, password: String, member_expire_time: DateTime<Utc>) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            account,
            password,
            is_member: true,
            member_expire_time: Some(member_expire_time),
            created_at: now,
            updated_at: now,
        }
    }

    /// 设置会员状态
    pub fn set_member(&mut self, is_member: bool, expire_time: Option<DateTime<Utc>>) {
        self.is_member = is_member;
        self.member_expire_time = expire_time;
        self.updated_at = Utc::now();
    }

    /// 检查会员是否有效
    pub fn is_valid_member(&self) -> bool {
        if !self.is_member {
            return false;
        }
        
        match self.member_expire_time {
            Some(expire_time) => expire_time > Utc::now(),
            None => true, // 永久会员
        }
    }

    /// 获取表名
    pub fn table_name() -> &'static str {
        "augment_users"
    }
}
