use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// 语音识别任务状态枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AsrTaskStatus {
    /// 识别中
    Processing = 1,
    /// 识别成功
    Success = 2,
    /// 识别失败
    Failed = 3,
}

impl From<i8> for AsrTaskStatus {
    fn from(value: i8) -> Self {
        match value {
            1 => AsrTaskStatus::Processing,
            2 => AsrTaskStatus::Success,
            3 => AsrTaskStatus::Failed,
            _ => AsrTaskStatus::Processing, // 默认为识别中
        }
    }
}

impl From<AsrTaskStatus> for i8 {
    fn from(status: AsrTaskStatus) -> Self {
        status as i8
    }
}

/// MySQL腾讯云语音识别任务模型
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct MySqlTencentAsrTask {
    /// 任务ID（主键）
    pub id: u64,

    /// 腾讯云返回的任务ID
    pub task_id: u64,

    /// 腾讯云返回的请求ID
    pub request_id: String,

    /// 关联的任务表ID（外键，可选）
    pub related_task_id: Option<u64>,

    /// 任务状态：1识别中 2识别成功 3识别失败
    #[sqlx(try_from = "i8")]
    pub status: AsrTaskStatus,

    /// 识别结果
    pub result: Option<String>,

    /// 创建时间
    pub created_at: DateTime<Utc>,

    /// 最后更新时间
    pub updated_at: DateTime<Utc>,
}

impl MySqlTencentAsrTask {
    /// 创建新的语音识别任务
    pub fn new(task_id: u64, request_id: String) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            task_id,
            request_id,
            related_task_id: None,
            status: AsrTaskStatus::Processing,
            result: None,
            created_at: now,
            updated_at: now,
        }
    }

    /// 创建新的语音识别任务（带关联任务ID）
    pub fn new_with_related_task(task_id: u64, request_id: String, related_task_id: Option<u64>) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            task_id,
            request_id,
            related_task_id,
            status: AsrTaskStatus::Processing,
            result: None,
            created_at: now,
            updated_at: now,
        }
    }

    /// 更新任务状态和结果
    pub fn update_result(&mut self, status: AsrTaskStatus, result: Option<String>) {
        self.status = status;
        self.result = result;
        self.updated_at = Utc::now();
    }

    /// 获取表名
    pub fn table_name() -> &'static str {
        "tencent_asr_tasks"
    }
}
