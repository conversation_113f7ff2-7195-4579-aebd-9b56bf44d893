use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// MySQL笔记草稿模型
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct MySqlNoteDraft {
    /// 草稿ID
    pub id: u64,

    /// 笔记ID（外键）
    pub note_id: u64,

    /// 用户ID（外键）
    pub user_id: u64,

    /// 草稿内容
    pub content: Option<String>,

    /// 创建时间
    pub create_time: DateTime<Utc>,

    /// 更新时间
    pub update_time: DateTime<Utc>,
}

impl MySqlNoteDraft {
    /// 创建新笔记草稿
    pub fn new(
        note_id: u64,
        user_id: u64,
        content: Option<String>,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            note_id,
            user_id,
            content,
            create_time: now,
            update_time: now,
        }
    }

    /// 获取表名
    pub fn table_name() -> &'static str {
        "note_drafts"
    }
}
