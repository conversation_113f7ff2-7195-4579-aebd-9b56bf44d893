use std::env;
use std::net::{IpAddr, Ipv4Addr, SocketAddr};
use std::sync::Arc;
use serde::{Deserialize, Serialize};

/// Redis配置
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct RedisConfig {
    pub host: String,
    pub port: u16,
    pub password: String,
}



/// MySQL配置
#[derive(<PERSON>lone, Debug, Serialize, Deserialize)]
pub struct MySqlConfig {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub database: String,
    pub max_connections: u32,
}

/// 阿里云OSS配置
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct OssConfig {
    pub region: String,
    pub access_key_id: String,
    pub access_key_secret: String,
    pub bucket: String,
    pub role_arn: String,
    pub sts_access_key_id: String,
    pub sts_access_key_secret: String,
}

/// 阿里云MNS配置
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct MnsConfig {
    pub endpoint: String,
    pub access_key_id: String,
    pub access_key_secret: String,
    /// 资源名称（队列名或主题名）
    pub resource_name: String,
    /// 资源类型（"queue"或"topic"）
    pub resource_type: String,
}

/// 微信应用配置
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct WechatConfig {
    /// 微信应用ID
    pub app_id: String,
    /// 微信应用密钥
    pub app_secret: String,
}

/// 腾讯云配置
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct TencentCloudConfig {
    /// 腾讯云SecretId（API密钥ID）
    pub secret_id: String,
    /// 腾讯云SecretKey（API密钥）
    pub secret_key: String,
}

/// LLM提供商配置
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct LlmProviderConfig {
    /// API密钥
    pub api_key: String,
    /// API基础URL（可选，某些提供商可能需要自定义）
    pub base_url: Option<String>,
    /// 模型名称（可选，默认模型）
    pub default_model: Option<String>,
    /// 是否启用
    pub enabled: bool,
}

/// LLM配置，支持多个提供商
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct LlmConfig {
    /// DeepSeek配置
    pub deepseek: Option<LlmProviderConfig>,
    /// OpenAI配置（预留）
    pub openai: Option<LlmProviderConfig>,
    /// 阿里云通义千问配置（预留）
    pub qwen: Option<LlmProviderConfig>,
    /// 百度文心一言配置（预留）
    pub ernie: Option<LlmProviderConfig>,
    /// 腾讯混元配置（预留）
    pub hunyuan: Option<LlmProviderConfig>,
}

impl LlmConfig {
    /// 获取启用的LLM提供商列表
    pub fn get_enabled_providers(&self) -> Vec<&str> {
        let mut providers = Vec::new();

        if let Some(ref config) = self.deepseek {
            if config.enabled {
                providers.push("deepseek");
            }
        }

        if let Some(ref config) = self.openai {
            if config.enabled {
                providers.push("openai");
            }
        }

        if let Some(ref config) = self.qwen {
            if config.enabled {
                providers.push("qwen");
            }
        }

        if let Some(ref config) = self.ernie {
            if config.enabled {
                providers.push("ernie");
            }
        }

        if let Some(ref config) = self.hunyuan {
            if config.enabled {
                providers.push("hunyuan");
            }
        }

        providers
    }

    /// 检查是否有任何LLM提供商可用
    pub fn has_enabled_provider(&self) -> bool {
        !self.get_enabled_providers().is_empty()
    }
}

/// 应用配置结构体，包含所有全局配置
#[derive(Clone, Debug)]
pub struct AppConfig {
    // 应用基本信息
    pub app_name: String,
    pub app_version: String,
    pub environment: String,
    pub debug: bool,

    // 服务器配置
    pub server_host: IpAddr,
    pub server_port: u16,
    pub workers: usize,

    // 数据库配置（可选）
    pub db_url: Option<String>,
    pub db_pool_size: u32,

    // Redis配置
    pub redis: RedisConfig,

    // MySQL配置
    pub mysql: MySqlConfig,

    // 阿里云OSS配置
    pub oss: OssConfig,

    // 阿里云MNS配置
    pub mns: MnsConfig,

    // 微信应用配置
    pub wechat: WechatConfig,

    // 腾讯云配置
    pub tencent_cloud: TencentCloudConfig,

    // LLM配置
    pub llm: LlmConfig,

    // 其他配置
    pub log_level: String,
    pub cors_enabled: bool,
}

impl AppConfig {
    /// 创建一个新的配置实例，从环境变量加载配置
    pub fn new() -> Arc<Self> {
        // 应用基本信息
        let app_name = env::var("APP_NAME").unwrap_or_else(|_| env!("CARGO_PKG_NAME").to_string());
        let app_version = env::var("APP_VERSION").unwrap_or_else(|_| env!("CARGO_PKG_VERSION").to_string());
        let environment = env::var("APP_ENV").unwrap_or_else(|_| "development".to_string());
        let debug = env::var("APP_DEBUG")
            .map(|val| val == "true" || val == "1")
            .unwrap_or(environment != "production");

        // 服务器配置
        let server_host = env::var("SERVER_HOST")
            .ok()
            .and_then(|host| host.parse().ok())
            .unwrap_or(IpAddr::V4(Ipv4Addr::new(0, 0, 0, 0))); // 监听所有网络接口

        let server_port = env::var("SERVER_PORT")
            .ok()
            .and_then(|port| port.parse().ok())
            .unwrap_or(8080);

        let workers = env::var("SERVER_WORKERS")
            .ok()
            .and_then(|workers| workers.parse().ok())
            .unwrap_or_else(|| num_cpus::get());

        // 数据库配置
        let db_url = env::var("DATABASE_URL").ok();

        let db_pool_size = env::var("DB_POOL_SIZE")
            .ok()
            .and_then(|size| size.parse().ok())
            .unwrap_or(5);

        // Redis配置
        let redis_host = env::var("REDIS_HOST")
            .unwrap_or_else(|_| "r-bp1ue7npgj2i78fvidpd.redis.rds.aliyuncs.com".to_string());
        let redis_port = env::var("REDIS_PORT")
            .ok()
            .and_then(|port| port.parse().ok())
            .unwrap_or(6379);
        let redis_password = env::var("REDIS_PASSWORD")
            .unwrap_or_else(|_| "Qtt$123456".to_string());



        // MySQL配置
        let mysql_host = env::var("MYSQL_HOST")
            .unwrap_or_else(|_| "rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com".to_string());
        let mysql_port = env::var("MYSQL_PORT")
            .ok()
            .and_then(|port| port.parse().ok())
            .unwrap_or(3306);
        let mysql_username = env::var("MYSQL_USERNAME")
            .unwrap_or_else(|_| "aishoucang_mysql".to_string());
        let mysql_password = env::var("MYSQL_PASSWORD")
            .unwrap_or_else(|_| "Qtt$123456".to_string());

        // 根据环境选择不同的数据库名称
        let mysql_database = env::var("MYSQL_DATABASE").unwrap_or_else(|_| {
            match environment.as_str() {
                "testing" => "aishoucang_develop".to_string(),
                "pre" => "aishoucang_pre".to_string(),
                "production" => "aishoucang".to_string(),
                _ => "aishoucang_develop".to_string() // 默认开发环境
            }
        });

        let mysql_max_connections = env::var("MYSQL_MAX_CONNECTIONS")
            .ok()
            .and_then(|size| size.parse().ok())
            .unwrap_or(5);

        // 阿里云OSS配置
        let oss_region = env::var("OSS_REGION")
            .unwrap_or_else(|_| "oss-cn-qingdao".to_string());
        let oss_access_key_id = env::var("OSS_ACCESS_KEY_ID")
            .unwrap_or_else(|_| "LTAIbUFLYWBXy4S8".to_string());
        let oss_access_key_secret = env::var("OSS_ACCESS_KEY_SECRET")
            .unwrap_or_else(|_| "******************************".to_string());
        let oss_bucket = env::var("OSS_BUCKET")
            .unwrap_or_else(|_| "assets-xunhe".to_string());
        let oss_role_arn = env::var("OSS_ROLE_ARN")
            .unwrap_or_else(|_| "acs:ram::1047429328316409:role/rustoss".to_string());

        let oss_sts_access_key_id: String = env::var("OSS_STS_ACCESS_KEY_ID")
            .unwrap_or_else(|_| "LTAI5t5mJhWV9sky8QvaZ8m3".to_string());
        let oss_sts_access_key_secret: String = env::var("OSS_STS_ACCESS_KEY_SECRET")
            .unwrap_or_else(|_| "******************************".to_string());

        // 阿里云MNS配置
        let mns_endpoint = env::var("MNS_ENDPOINT")
            .unwrap_or_else(|_| "https://1047429328316409.mns.cn-hangzhou.aliyuncs.com".to_string());
        let mns_resource_name = env::var("MNS_RESOURCE_NAME")
            .unwrap_or_else(|_| "changeToOss".to_string());
        let mns_resource_type = env::var("MNS_RESOURCE_TYPE")
            .unwrap_or_else(|_| "topic".to_string()); // 默认使用主题类型
        // 使用OSS STS的Access Key
        let mns_access_key_id = "LTAI5tQemkxcQw8hscY3QHCt".to_string();
        let mns_access_key_secret = "******************************".to_string();

        // 微信应用配置
        let wechat_app_id = env::var("WECHAT_APP_ID")
            .unwrap_or_else(|_| "wxb0872bb3945f31bc".to_string());
        let wechat_app_secret = env::var("WECHAT_APP_SECRET")
            .unwrap_or_else(|_| "b63d0ce7dd99d895351bbc6834765669".to_string());

        // 腾讯云配置
        let tencent_cloud_secret_id = env::var("TENCENT_CLOUD_SECRET_ID")
            .unwrap_or_else(|_| "AKIDPURFwSkqjP3Ys1zmOp8JJ8sV0MbiePzR".to_string());
        let tencent_cloud_secret_key = env::var("TENCENT_CLOUD_SECRET_KEY")
            .unwrap_or_else(|_| "LUSGzd6OYYM3czBBza0GJEyWu7UinkcN".to_string());

        // LLM配置
        let llm_config = Self::load_llm_config();

        // 其他配置
        let log_level = env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string());

        let cors_enabled = env::var("CORS_ENABLED")
            .map(|val| val == "true" || val == "1")
            .unwrap_or(false);

        Arc::new(Self {
            app_name,
            app_version,
            environment,
            debug,
            server_host,
            server_port,
            workers,
            db_url,
            db_pool_size,
            log_level,
            cors_enabled,
            redis: RedisConfig {
                host: redis_host,
                port: redis_port,
                password: redis_password,
            },
            mysql: MySqlConfig {
                host: mysql_host,
                port: mysql_port,
                username: mysql_username,
                password: mysql_password,
                database: mysql_database,
                max_connections: mysql_max_connections,
            },
            oss: OssConfig {
                region: oss_region,
                access_key_id: oss_access_key_id,
                access_key_secret: oss_access_key_secret,
                bucket: oss_bucket,
                role_arn: oss_role_arn,
                sts_access_key_id: oss_sts_access_key_id,
                sts_access_key_secret: oss_sts_access_key_secret,
            },
            mns: MnsConfig {
                endpoint: mns_endpoint,
                access_key_id: mns_access_key_id,
                access_key_secret: mns_access_key_secret,
                resource_name: mns_resource_name,
                resource_type: mns_resource_type,
            },
            wechat: WechatConfig {
                app_id: wechat_app_id,
                app_secret: wechat_app_secret,
            },
            tencent_cloud: TencentCloudConfig {
                secret_id: tencent_cloud_secret_id,
                secret_key: tencent_cloud_secret_key,
            },
            llm: llm_config,
        })
    }

    /// 加载LLM配置
    fn load_llm_config() -> LlmConfig {
        // DeepSeek配置
        let deepseek_config = if let Ok(api_key) = env::var("DEEPSEEK_API_KEY") {
            Some(LlmProviderConfig {
                api_key,
                base_url: env::var("DEEPSEEK_BASE_URL").ok(),
                default_model: env::var("DEEPSEEK_DEFAULT_MODEL").ok(),
                enabled: env::var("DEEPSEEK_ENABLED")
                    .map(|val| val == "true" || val == "1")
                    .unwrap_or(true), // 如果有API key，默认启用
            })
        } else {
            // 如果没有环境变量，使用提供的默认值
            Some(LlmProviderConfig {
                api_key: "sk-2d57fa52beac48df86dd1993310f4e3e".to_string(),
                base_url: Some("https://api.deepseek.com".to_string()),
                default_model: Some("deepseek-reasoner".to_string()),
                enabled: true,
            })
        };

        // OpenAI配置（预留）
        let openai_config = if let Ok(api_key) = env::var("OPENAI_API_KEY") {
            Some(LlmProviderConfig {
                api_key,
                base_url: env::var("OPENAI_BASE_URL").ok(),
                default_model: env::var("OPENAI_DEFAULT_MODEL").ok(),
                enabled: env::var("OPENAI_ENABLED")
                    .map(|val| val == "true" || val == "1")
                    .unwrap_or(true),
            })
        } else {
            None
        };

        // 阿里云通义千问配置（预留）
        let qwen_config = if let Ok(api_key) = env::var("QWEN_API_KEY") {
            Some(LlmProviderConfig {
                api_key,
                base_url: env::var("QWEN_BASE_URL").ok(),
                default_model: env::var("QWEN_DEFAULT_MODEL").ok(),
                enabled: env::var("QWEN_ENABLED")
                    .map(|val| val == "true" || val == "1")
                    .unwrap_or(true),
            })
        } else {
            None
        };

        // 百度文心一言配置（预留）
        let ernie_config = if let Ok(api_key) = env::var("ERNIE_API_KEY") {
            Some(LlmProviderConfig {
                api_key,
                base_url: env::var("ERNIE_BASE_URL").ok(),
                default_model: env::var("ERNIE_DEFAULT_MODEL").ok(),
                enabled: env::var("ERNIE_ENABLED")
                    .map(|val| val == "true" || val == "1")
                    .unwrap_or(true),
            })
        } else {
            None
        };

        // 腾讯混元配置（预留）
        let hunyuan_config = if let Ok(api_key) = env::var("HUNYUAN_API_KEY") {
            Some(LlmProviderConfig {
                api_key,
                base_url: env::var("HUNYUAN_BASE_URL").ok(),
                default_model: env::var("HUNYUAN_DEFAULT_MODEL").ok(),
                enabled: env::var("HUNYUAN_ENABLED")
                    .map(|val| val == "true" || val == "1")
                    .unwrap_or(true),
            })
        } else {
            None
        };

        LlmConfig {
            deepseek: deepseek_config,
            openai: openai_config,
            qwen: qwen_config,
            ernie: ernie_config,
            hunyuan: hunyuan_config,
        }
    }

    /// 获取服务器地址
    pub fn server_addr(&self) -> SocketAddr {
        SocketAddr::new(self.server_host, self.server_port)
    }
}
