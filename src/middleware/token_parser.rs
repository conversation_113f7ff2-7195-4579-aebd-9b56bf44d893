use std::future::{ready, Ready};
use std::rc::Rc;
use std::collections::HashSet;
use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    http::header,
    web, Error, HttpMessage, HttpResponse,
};
use futures_util::future::LocalBoxFuture;
use log::{debug, error, info};
use serde::{Deserialize, Serialize};
use serde_json::json;

use crate::AppState;
use crate::dto::error_code::ErrorCode;

/// Token 解析中间件
///
/// 此中间件用于解析请求中的 token 参数
/// 仅支持从请求头中提取 token: Authorization: Bearer xxx
///
/// 可以配置跳过 token 验证的路径，如注册、登录等不需要认证的接口
pub struct TokenParser {
    /// 无需 token 的路径列表
    pub exempt_paths: HashSet<String>,
}

impl Default for TokenParser {
    fn default() -> Self {
        // 默认跳过的路径：注册、登录、测试接口和直接上传接口
        let mut exempt_paths = HashSet::new();
        exempt_paths.insert("/users/regist".to_string());
        exempt_paths.insert("/users/login".to_string());
        exempt_paths.insert("/wechat/login".to_string()); // 添加微信登录接口到豁免列表
        exempt_paths.insert("/test/ping".to_string());
        exempt_paths.insert("/test/mns".to_string());
        exempt_paths.insert("/test/mysql".to_string());
        exempt_paths.insert("/test/mysql_services".to_string());
        exempt_paths.insert("/upload/direct".to_string()); // 添加直接上传接口到豁免列表
        exempt_paths.insert("/app/version/check".to_string()); // 添加版本检查接口到豁免列表
        exempt_paths.insert("/app/version/store".to_string()); // 添加版本存储接口到豁免列表
        exempt_paths.insert("/note/detail".to_string()); // 添加笔记详情接口到豁免列表

        exempt_paths.insert("/augment/users/login".to_string());
        exempt_paths.insert("/tencent_cloud/asr/callback".to_string());

        TokenParser { exempt_paths }
    }
}

// 中间件工厂实现
impl<S, B> Transform<S, ServiceRequest> for TokenParser
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type InitError = ();
    type Transform = TokenParserMiddleware<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(TokenParserMiddleware {
            service: Rc::new(service),
            exempt_paths: self.exempt_paths.clone(),
        }))
    }
}

// 中间件服务实现
pub struct TokenParserMiddleware<S> {
    service: Rc<S>,
    exempt_paths: HashSet<String>,
}

impl<S, B> Service<ServiceRequest> for TokenParserMiddleware<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let service = Rc::clone(&self.service);
        let exempt_paths = self.exempt_paths.clone();

        Box::pin(async move {
            // 获取请求路径
            let path = req.path().to_string();

            // 检查是否是跳过 token 验证的路径
            let is_exempt = exempt_paths.contains(&path);
            if is_exempt {
                info!("跳过 token 验证的路径: {}", path);
                return service.call(req).await;
            }

            // 从请求头中获取 token
            let mut token = None;

            // 检查 Authorization 请求头
            if let Some(auth_header) = req.headers().get(header::AUTHORIZATION) {
                if let Ok(auth_str) = auth_header.to_str() {
                    if auth_str.starts_with("Bearer ") {
                        token = Some(auth_str[7..].to_string());
                        debug!("从请求头中获取到 token: {}", token.as_ref().unwrap());
                    }
                }
            }

            // 如果没有找到 token，返回错误
            let token_value = match token {
                Some(t) => t,
                None => {
                    debug!("请求中未找到 token");
                    return Err(create_error_response(ErrorCode::TokenInvalid));
                }
            };

            // 验证 token 是否有效
            let app_state = req.app_data::<web::Data<AppState>>().cloned();
            if let Some(state) = app_state {
                if let Some(db) = &state.db {
                    // 检查 token 是否存在于 Redis 中
                    match verify_token(&db.redis, &token_value).await {
                        Ok(user_id) => {
                            // token 有效，将 token 和 user_id 存储在请求扩展中
                            debug!("token 验证成功，用户ID: {}", user_id);
                            req.extensions_mut().insert(ParsedToken(token_value));
                            req.extensions_mut().insert(TokenUserId(user_id));
                        }
                        Err(e) => {
                            // token 无效或已过期
                            error!("token 验证失败: {}", e);
                            let error_code = if e.contains("过期") {
                                ErrorCode::TokenExpired
                            } else {
                                ErrorCode::TokenInvalid
                            };
                            return Err(create_error_response(error_code));
                        }
                    }
                } else {
                    // 数据库连接不可用
                    error!("Redis 连接不可用，无法验证 token");
                    return Err(create_error_response(ErrorCode::ServiceUnavailable));
                }
            } else {
                // 应用状态不可用
                error!("应用状态不可用，无法验证 token");
                return Err(create_error_response(ErrorCode::SystemError));
            }

            // 继续处理请求
            let res = service.call(req).await?;
            Ok(res)
        })
    }
}

/// 解析出的 Token 包装器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParsedToken(pub String);

/// Token 对应的用户 ID
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenUserId(pub String);

/// 从请求扩展中获取 token 的辅助函数
pub fn get_token_from_request(req: &actix_web::HttpRequest) -> Option<String> {
    // 返回克隆的字符串，避免生命周期问题
    req.extensions()
        .get::<ParsedToken>()
        .map(|token| token.0.clone())
}

/// 从请求扩展中获取用户 ID 的辅助函数
pub fn get_user_id_from_request(req: &actix_web::HttpRequest) -> Option<String> {
    // 返回克隆的字符串，避免生命周期问题
    req.extensions()
        .get::<TokenUserId>()
        .map(|user_id| user_id.0.clone())
}

/// 验证 token 是否有效
async fn verify_token(redis_client: &crate::db::RedisClient, token: &str) -> Result<String, String> {
    // 从 Redis 中获取 token 对应的用户 ID
    match redis_client.get::<_, String>(token) {
        Ok(user_id) => Ok(user_id),
        Err(e) => {
            if e.is_timeout() {
                Err("令牌已过期".to_string())
            } else if e.is_connection_dropped() || e.is_io_error() {
                Err("Redis 连接错误".to_string())
            } else {
                Err("无效的令牌".to_string())
            }
        }
    }
}

/// 创建错误响应
fn create_error_response(error_code: ErrorCode) -> Error {
    // 创建自定义错误响应
    actix_web::error::ErrorUnauthorized(json!({
        "code": error_code.code(),
        "message": error_code.message(),
        "data": null
    }))
}
