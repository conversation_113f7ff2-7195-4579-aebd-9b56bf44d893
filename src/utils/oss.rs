use std::time::{Instant, SystemTime, UNIX_EPOCH};
use actix_multipart::Field;
use futures_util::StreamExt;
use log::{error, info};
use uuid::Uuid;
use reqwest::header::{HeaderMap, HeaderValue, CONTENT_TYPE, CONTENT_LENGTH, DATE, AUTHORIZATION};
use chrono::Utc;
use hmac::{Hmac, Mac};
use sha1::Sha1;
use base64::{Engine as _, engine::general_purpose};
use crate::config::OssConfig;

/// OSS 操作错误
#[derive(Debug, thiserror::Error)]
pub enum OssError {
    /// OSS API 错误
    #[error("OSS 操作错误: {0}")]
    OssClientError(String),

    /// 文件读取错误
    #[error("文件读取错误: {0}")]
    IoError(#[from] std::io::Error),

    /// 无效的文件类型
    #[error("无效的文件类型")]
    InvalidFileType,

    /// 文件太大
    #[error("文件太大，超过限制")]
    FileTooLarge,

    /// 未提供文件
    #[error("未提供文件")]
    NoFileProvided,
}

/// OSS 客户端封装
pub struct OssClient {
    config: OssConfig,
    http_client: reqwest::Client,
}

impl OssClient {
    /// 创建 OSS 客户端
    pub fn new(config: &OssConfig) -> Self {
        let http_client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .expect("创建HTTP客户端失败");

        Self {
            config: config.clone(),
            http_client,
        }
    }

    /// 上传文件到OSS（直接调用API）
    pub async fn upload_file(&self, field: &mut Field) -> Result<String, OssError> {
        // 记录总体开始时间
        let total_start_time = Instant::now();
        info!("开始处理文件上传请求");

        // 获取文件名
        let content_disposition = field.content_disposition();
        let filename = content_disposition
            .get_filename()
            .ok_or(OssError::NoFileProvided)?
            .to_string();

        // 检查文件类型
        let content_type = field.content_type()
            .map(|mime| mime.to_string())
            .unwrap_or_else(|| "application/octet-stream".to_string());

        // 生成唯一的文件名
        let file_ext = filename.split('.').last().unwrap_or("dat");
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        let uuid = Uuid::new_v4().to_string();
        let object_name = format!("uploads/{}/{}_{}.{}", timestamp % 100, uuid, timestamp, file_ext);

        info!("准备上传文件: {}, 类型: {}", filename, content_type);

        // 记录读取数据开始时间
        let read_start_time = Instant::now();
        info!("开始读取文件数据");

        // 读取全部数据
        let mut file_data = Vec::new();
        let mut chunk_count = 0;
        while let Some(chunk) = field.next().await {
            let data = chunk.map_err(|_| OssError::IoError(std::io::Error::new(
                std::io::ErrorKind::Other, "读取文件数据失败")))?;

            chunk_count += 1;

            // 如果文件过大（超过20MB），返回错误
            if file_data.len() + data.len() > 20 * 1024 * 1024 {
                error!("文件太大，超过20MB限制，当前大小: {} 字节", file_data.len() + data.len());
                return Err(OssError::FileTooLarge);
            }

            file_data.extend_from_slice(&data);
        }

        let read_duration = read_start_time.elapsed();
        info!("文件数据读取完成，耗时: {:?}，大小: {} 字节，分块数: {}",
              read_duration, file_data.len(), chunk_count);

        // 构建OSS请求
        let upload_start_time = Instant::now();
        let file_size = file_data.len();
        info!("开始通过直接API上传数据到OSS，文件大小: {} 字节", file_size);

        // 构建请求URL
        let host = format!("{}.{}.aliyuncs.com", self.config.bucket, self.config.region);
        let url = format!("https://{}/{}", host, object_name);

        // 准备签名所需的信息
        let date = Utc::now().format("%a, %d %b %Y %H:%M:%S GMT").to_string();
        let content_md5 = ""; // 可以计算文件的MD5并Base64编码，这里简化处理

        // 构建待签名字符串
        let string_to_sign = format!(
            "PUT\n{}\n{}\n{}\n/{}/{}",
            content_md5,
            content_type,
            date,
            self.config.bucket,
            object_name
        );

        // 计算签名
        let signature = self.hmac_sha1(&string_to_sign)
            .map_err(|e| OssError::OssClientError(e))?;

        // 构建Authorization头
        let authorization = format!("OSS {}:{}", self.config.access_key_id, signature);

        // 构建请求头
        let mut headers = HeaderMap::new();
        headers.insert(DATE, HeaderValue::from_str(&date).unwrap());
        headers.insert(CONTENT_TYPE, HeaderValue::from_str(&content_type).unwrap());
        headers.insert(CONTENT_LENGTH, HeaderValue::from_str(&file_size.to_string()).unwrap());
        headers.insert(AUTHORIZATION, HeaderValue::from_str(&authorization).unwrap());

        // 发送PUT请求
        let response = self.http_client
            .put(&url)
            .headers(headers)
            .body(file_data)
            .send()
            .await
            .map_err(|e| OssError::OssClientError(e.to_string()))?;

        // 检查响应
        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await
                .unwrap_or_else(|_| "无法获取错误详情".to_string());
            return Err(OssError::OssClientError(
                format!("OSS服务返回错误状态码 {}: {}", status, error_text)
            ));
        }

        let upload_duration = upload_start_time.elapsed();
        info!("直接API上传完成，耗时: {:?}，上传速度: {:.2} KB/s",
              upload_duration,
              (file_size as f64 / 1024.0) / upload_duration.as_secs_f64());

        // 构造可访问的URL
        let url = format!("https://{}/{}", host, object_name);

        let total_duration = total_start_time.elapsed();
        info!("文件上传成功: {}, 总耗时: {:?}, 读取耗时: {:?} ({:.1}%), 上传耗时: {:?} ({:.1}%)",
              url,
              total_duration,
              read_duration,
              (read_duration.as_secs_f64() / total_duration.as_secs_f64()) * 100.0,
              upload_duration,
              (upload_duration.as_secs_f64() / total_duration.as_secs_f64()) * 100.0);

        Ok(url)
    }

    /// 使用HMAC-SHA1算法计算签名
    fn hmac_sha1(&self, data: &str) -> Result<String, String> {
        type HmacSha1 = Hmac<Sha1>;

        let mut mac = HmacSha1::new_from_slice(self.config.access_key_secret.as_bytes())
            .map_err(|e| format!("HMAC初始化失败: {}", e))?;

        mac.update(data.as_bytes());
        let result = mac.finalize().into_bytes();

        // Base64编码
        Ok(general_purpose::STANDARD.encode(result))
    }
}