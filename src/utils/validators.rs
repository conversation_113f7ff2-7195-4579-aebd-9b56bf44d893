use regex::Regex;
use once_cell::sync::Lazy;

/// 中国大陆手机号正则表达式
/// 
/// 规则说明：
/// - 1开头
/// - 第二位：3-9
/// - 后面9位：0-9
/// 
/// 支持的号段：
/// - 13x：130-139
/// - 14x：145,147,148,149
/// - 15x：150-159
/// - 16x：162,165,166,167,168,169
/// - 17x：170-179
/// - 18x：180-189
/// - 19x：190-199
static CHINA_MOBILE_REGEX: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"^1[3-9]\d{9}$").unwrap()
});

/// 验证是否为有效的中国大陆手机号
pub fn is_valid_china_mobile(phone: &str) -> bool {
    CHINA_MOBILE_REGEX.is_match(phone)
}

/// 验证密码强度
/// 
/// 规则：
/// - 至少6位
/// - 不能超过20位
/// - 不能包含空格
pub fn is_valid_password(password: &str) -> bool {
    let length = password.len();
    length >= 6 && length <= 20 && !password.contains(' ')
}
