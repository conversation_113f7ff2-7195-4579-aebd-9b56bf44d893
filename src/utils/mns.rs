use std::time::Instant;
use chrono::Utc;
use hmac::{Hmac, Mac};
use sha1::Sha1;
use base64::{Engine as _, engine::general_purpose};
use log::{error, info};
use reqwest::{Client, header};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use crate::config::MnsConfig;
use quick_xml::de::from_str as xml_from_str;

/// MNS 操作错误
#[derive(Debug, thiserror::Error)]
pub enum MnsError {
    /// HTTP 请求错误
    #[error("HTTP请求错误: {0}")]
    HttpError(#[from] reqwest::Error),

    /// MNS 服务返回错误
    #[error("MNS服务错误: 代码={code}, 消息={message}, 请求ID={request_id}")]
    MnsServiceError {
        code: String,
        message: String,
        request_id: String,
    },

    /// 签名计算错误
    #[error("签名计算错误: {0}")]
    SignatureError(String),

    /// 其他错误
    #[error("MNS操作错误: {0}")]
    OtherError(String),
}

/// MNS 错误响应
#[derive(Debug, Deserialize)]
struct MnsErrorResponse {
    #[serde(rename = "Code")]
    code: String,
    #[serde(rename = "Message")]
    message: String,
    #[serde(rename = "RequestId")]
    request_id: String,
    #[serde(rename = "HostId")]
    host_id: Option<String>,
}

/// MNS 消息属性
#[derive(Debug, Serialize, Deserialize)]
pub struct MnsMessageAttributes {
    /// 消息ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message_id: Option<String>,

    /// 消息体MD5
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message_body_md5: Option<String>,

    /// 消息体类型
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message_body_type: Option<String>,

    /// 消息优先级 (1-16)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub priority: Option<i32>,

    /// 消息延迟时间（秒）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub delay_seconds: Option<i32>,
}

/// MNS 消息请求体
#[derive(Debug, Serialize)]
struct MnsMessageRequest {
    #[serde(rename = "MessageBody")]
    message_body: String,

    #[serde(rename = "DelaySeconds", skip_serializing_if = "Option::is_none")]
    delay_seconds: Option<i32>,

    #[serde(rename = "Priority", skip_serializing_if = "Option::is_none")]
    priority: Option<i32>,
}

impl MnsMessageRequest {
    /// 转换为XML格式
    fn to_xml(&self) -> String {
        let mut xml = String::from("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<Message xmlns=\"http://mns.aliyuncs.com/doc/v1/\">\n");

        // 对消息体进行Base64编码，避免XML特殊字符问题
        let encoded_message = general_purpose::STANDARD.encode(self.message_body.as_bytes());
        xml.push_str(&format!("  <MessageBody>{}</MessageBody>\n", encoded_message));

        // 添加可选参数
        if let Some(delay) = self.delay_seconds {
            xml.push_str(&format!("  <DelaySeconds>{}</DelaySeconds>\n", delay));
        }

        if let Some(priority) = self.priority {
            xml.push_str(&format!("  <Priority>{}</Priority>\n", priority));
        }

        xml.push_str("</Message>");
        xml
    }
}

/// MNS 消息响应
#[derive(Debug, Deserialize)]
#[serde(rename = "Message")]
pub struct MnsMessageResponse {
    #[serde(rename = "MessageId")]
    pub message_id: String,

    #[serde(rename = "MessageBodyMD5")]
    pub message_body_md5: String,
}

/// MNS 客户端
pub struct MnsClient {
    config: MnsConfig,
    http_client: Client,
}

impl MnsClient {
    /// 创建新的MNS客户端
    pub fn new(config: MnsConfig) -> Self {
        let http_client = Client::builder()
            .timeout(std::time::Duration::from_secs(10))
            .build()
            .expect("创建HTTP客户端失败");

        Self {
            config,
            http_client,
        }
    }

    /// 发送消息到MNS资源（队列或主题）
    pub async fn send_message(&self, message: &str, attributes: Option<MnsMessageAttributes>) -> Result<MnsMessageResponse, MnsError> {
        // 记录开始时间
        let start_time = Instant::now();

        // 根据资源类型构造请求URL
        let resource_type = if self.config.resource_type == "queue" { "queues" } else { "topics" };
        let url = format!("{}/{}/{}/messages", self.config.endpoint, resource_type, self.config.resource_name);

        // 构造请求体
        let request_body = MnsMessageRequest {
            message_body: message.to_string(),
            delay_seconds: attributes.as_ref().and_then(|attr| attr.delay_seconds),
            priority: attributes.as_ref().and_then(|attr| attr.priority),
        };

        // 转换为XML格式
        let request_body_str = request_body.to_xml();

        // 获取当前时间
        let date = Utc::now().format("%a, %d %b %Y %H:%M:%S GMT").to_string();

        // 生成请求ID
        let request_id = Uuid::new_v4().to_string();

        // 计算签名
        let signature = if self.config.resource_type == "queue" {
            self.calculate_signature("POST", &date, &request_id, &self.config.resource_name)?
        } else {
            self.calculate_signature_for_topic("POST", &date, &request_id, &self.config.resource_name)?
        };

        // 构造认证头
        let auth_header = format!("MNS {}:{}", self.config.access_key_id, signature);

        // 发送请求
        let response = self.http_client
            .post(&url)
            .header(header::CONTENT_TYPE, "text/xml;charset=utf-8")
            .header(header::DATE, &date)
            .header("x-mns-version", "2015-06-06")
            .header("x-mns-request-id", &request_id)
            .header(header::AUTHORIZATION, auth_header)
            .body(request_body_str)
            .send()
            .await?;

        // 获取响应状态
        let status = response.status();

        // 获取响应体
        let response_text = response.text().await?;

        // 检查响应状态
        if !status.is_success() {
            if let Ok(error_response) = xml_from_str::<MnsErrorResponse>(&response_text) {
                return Err(MnsError::MnsServiceError {
                    code: error_response.code,
                    message: error_response.message,
                    request_id: error_response.request_id,
                });
            } else {
                return Err(MnsError::OtherError(format!("MNS服务返回错误状态码 {}: {}", status, response_text)));
            }
        }

        // 直接解析响应
        let mns_response = match xml_from_str::<MnsMessageResponse>(&response_text) {
            Ok(response) => response,
            Err(e) => return Err(MnsError::OtherError(format!("解析MNS响应失败: {}, 响应内容: {}", e, response_text))),
        };

        let _duration = start_time.elapsed();
        info!("MNS消息发送成功，消息ID: {}", mns_response.message_id);

        Ok(mns_response)
    }

    /// 计算MNS队列请求签名
    fn calculate_signature(&self, http_method: &str, date: &str, request_id: &str, resource: &str) -> Result<String, MnsError> {
        // 构造待签名字符串
        let string_to_sign = format!(
            "{}\n\ntext/xml;charset=utf-8\n{}\nx-mns-request-id:{}\nx-mns-version:2015-06-06\n/queues/{}/messages",
            http_method, date, request_id, resource
        );

        // 使用HMAC-SHA1算法计算签名
        type HmacSha1 = Hmac<Sha1>;

        let key = format!("{}", self.config.access_key_secret);
        let mut mac = HmacSha1::new_from_slice(key.as_bytes())
            .map_err(|e| MnsError::SignatureError(format!("HMAC初始化失败: {}", e)))?;

        mac.update(string_to_sign.as_bytes());
        let result = mac.finalize().into_bytes();

        // Base64编码
        Ok(general_purpose::STANDARD.encode(result))
    }

    /// 计算MNS主题请求签名
    fn calculate_signature_for_topic(&self, http_method: &str, date: &str, request_id: &str, resource: &str) -> Result<String, MnsError> {
        // 构造待签名字符串 - 注意这里使用topics而不是queues
        let string_to_sign = format!(
            "{}\n\ntext/xml;charset=utf-8\n{}\nx-mns-request-id:{}\nx-mns-version:2015-06-06\n/topics/{}/messages",
            http_method, date, request_id, resource
        );

        // 使用HMAC-SHA1算法计算签名
        type HmacSha1 = Hmac<Sha1>;

        let key = format!("{}", self.config.access_key_secret);
        let mut mac = HmacSha1::new_from_slice(key.as_bytes())
            .map_err(|e| MnsError::SignatureError(format!("HMAC初始化失败: {}", e)))?;

        mac.update(string_to_sign.as_bytes());
        let result = mac.finalize().into_bytes();

        // Base64编码
        Ok(general_purpose::STANDARD.encode(result))
    }
}
