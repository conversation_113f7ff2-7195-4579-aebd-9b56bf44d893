/// 颜色工具模块
/// 提供标签颜色相关的工具函数

/// 颜色方案结构
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct ColorScheme {
    /// 背景颜色
    pub background_color: String,
    /// 文字颜色
    pub text_color: String,
}

impl ColorScheme {
    /// 创建新的颜色方案
    pub fn new(background_color: String, text_color: String) -> Self {
        Self {
            background_color,
            text_color,
        }
    }

    /// 获取默认颜色方案
    pub fn default() -> Self {
        Self::new("#1890ff".to_string(), "#ffffff".to_string())
    }
}

/// 颜色工具函数
pub struct ColorUtils;

impl ColorUtils {
    /// 验证十六进制颜色格式
    pub fn is_valid_hex_color(color: &str) -> bool {
        if color.len() != 7 || !color.starts_with('#') {
            return false;
        }
        color[1..].chars().all(|c| c.is_ascii_hexdigit())
    }

    /// 获取默认背景颜色
    pub fn default_background_color() -> String {
        "#1890ff".to_string()
    }

    /// 获取默认文字颜色
    pub fn default_text_color() -> String {
        "#ffffff".to_string()
    }

    /// 从旧的单一颜色值创建颜色方案
    pub fn create_scheme_from_legacy_color(legacy_color: Option<String>) -> ColorScheme {
        let background_color = legacy_color.unwrap_or_else(Self::default_background_color);
        let text_color = Self::default_text_color();

        ColorScheme::new(background_color, text_color)
    }
}
