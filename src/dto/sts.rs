use serde::{Deserialize, Serialize};

/// STS临时授权凭证
#[derive(Debug, Serialize, Deserialize)]
pub struct StsCredentials {
    /// 临时访问密钥ID
    pub access_key_id: String,
    
    /// 临时访问密钥
    pub access_key_secret: String,
    
    /// 安全令牌
    pub security_token: String,
    
    /// 过期时间（ISO 8601格式的UTC时间）
    pub expiration: String,
}

/// STS临时授权响应
#[derive(Debug, Serialize, Deserialize)]
pub struct StsTokenResponse {
    /// 临时访问凭证
    pub credentials: StsCredentials,
    
    /// 请求ID
    pub request_id: String,
    
    /// OSS区域
    pub region: String,
    
    /// OSS存储桶名称
    pub bucket: String,
}
