use serde::{Deserialize, Serialize};

/// 腾讯云语音识别回调请求DTO
#[derive(Debug, Deserialize)]
pub struct TencentAsrCallbackRequest {
    /// 任务状态码，0为成功，其他：失败
    pub code: i64,
    /// 失败原因文字描述，成功时此值为空
    pub message: Option<String>,
    /// 任务唯一标识，与录音识别请求中返回的 TaskId 一致
    #[serde(rename = "requestId")]
    pub request_id: u64,
    /// 腾讯云应用 ID
    pub appid: u64,
    /// 腾讯云项目 ID
    pub projectid: i64,
    /// 语音 url，如创建任务时为上传数据的方式，则不包含该字段
    #[serde(rename = "audioUrl")]
    pub audio_url: Option<String>,
    /// 识别出的结果文本
    pub text: Option<String>,
    /// 包含详细识别结果，如创建任务时 ResTextFormat 为0，则不包含该字段
    #[serde(rename = "resultDetail")]
    pub result_detail: Option<String>,
    /// 语音总时长
    #[serde(rename = "audioTime")]
    pub audio_time: Option<f64>,
}

/// 腾讯云语音识别回调响应DTO
#[derive(Debug, Serialize)]
pub struct TencentAsrCallbackResponse {
    /// 响应状态码
    pub code: i32,
    /// 响应消息
    pub message: String,
}
