use serde::{Deserialize, Serialize};

/// Augment用户登录请求数据结构
#[derive(Debug, Serialize, Deserialize)]
pub struct AugmentLoginRequest {
    /// 账号
    pub account: String,
    /// 密码
    pub password: String,
}

/// Augment用户登录响应数据
#[derive(Debug, Serialize, Deserialize)]
pub struct AugmentLoginData {
    /// 用户ID
    pub user_id: String,
    /// 账号
    pub account: String,
    /// 是否是会员
    pub is_member: bool,
    /// 会员到期时间（ISO 8601格式的UTC时间字符串）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub member_expire_time: Option<String>,
    /// 认证令牌
    pub token: String,
}
