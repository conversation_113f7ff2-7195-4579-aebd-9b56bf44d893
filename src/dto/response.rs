use serde::{Deserialize, Serialize};

/// 通用 API 响应结构体
/// 
/// 用于统一所有 API 的响应格式
/// - code: 状态码，0 表示成功，其他值表示错误
/// - data: 响应数据，可以是任何类型
/// - message: 响应消息
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub code: i32,
    pub data: T,
    pub message: String,
}

impl<T> ApiResponse<T> {
    /// 创建成功响应
    pub fn success(data: T, message: impl Into<String>) -> Self {
        Self {
            code: 0,
            data,
            message: message.into(),
        }
    }
    
    /// 创建错误响应
    pub fn error(code: i32, data: T, message: impl Into<String>) -> Self {
        Self {
            code,
            data,
            message: message.into(),
        }
    }
}

/// 空数据结构体，用于不需要返回数据的接口
#[derive(Debug, Serialize, Deserialize)]
pub struct EmptyData {}
