use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// 笔记详情响应DTO
#[derive(Debug, Serialize, Clone)]
pub struct NoteDetailResponse {
    /// 笔记ID
    pub id: String,
    
    /// 合集ID（可为空）
    pub parent_id: Option<String>,
    
    /// 用户ID
    pub user_id: String,
    
    /// 笔记标题
    pub title: String,
    
    /// 笔记封面
    pub cover: String,
    
    /// 笔记描述
    pub desc: String,
    
    /// 笔记内容（可选）
    pub content: Option<String>,

    /// HTML内容
    pub html: String,

    /// 笔记状态：1正常 2已删除
    pub status: Option<i8>,

    /// 创建时间
    pub create_time: DateTime<Utc>,

    /// 更新时间
    pub update_time: DateTime<Utc>,
}

impl NoteDetailResponse {
    /// 从MySQL笔记模型创建响应DTO
    pub fn from_mysql_model(note: &crate::models::mysql::MySqlNote) -> Self {
        Self {
            id: note.id.to_string(),
            parent_id: note.parent_id.map(|id| id.to_string()),
            user_id: note.user_id.to_string(),
            title: note.title.clone(),
            cover: note.cover.clone(),
            desc: note.desc.clone(),
            content: note.content.clone(),
            html: note.html.clone(),
            status: note.status.map(|s| s as i8),
            create_time: note.create_time,
            update_time: note.update_time,
        }
    }
}

/// 更新笔记请求DTO
#[derive(Debug, Deserialize)]
pub struct UpdateNoteRequest {
    /// 笔记ID
    pub id: String,

    /// 笔记标题（可选）
    #[serde(default)]
    pub title: Option<String>,

    /// 笔记封面（可选）
    #[serde(default)]
    pub cover: Option<String>,

    /// 笔记描述（可选）
    #[serde(default)]
    pub desc: Option<String>,

    /// 笔记内容（可选）
    #[serde(default)]
    pub content: Option<String>,

    /// HTML内容（可选）
    #[serde(default)]
    pub html: Option<String>,
}

/// 创建笔记请求DTO
#[derive(Debug, Deserialize)]
pub struct CreateNoteRequest {
    /// 合集ID（可选）
    #[serde(default)]
    pub parent_id: Option<String>,

    /// 笔记标题
    pub title: String,

    /// 笔记封面（可选）
    #[serde(default)]
    pub cover: String,

    /// 笔记描述（可选）
    #[serde(default)]
    pub desc: String,

    /// 笔记内容（可选）
    #[serde(default)]
    pub content: Option<String>,

    /// HTML内容（可选）
    #[serde(default)]
    pub html: String,
}

/// 创建笔记响应DTO
#[derive(Debug, Serialize)]
pub struct CreateNoteResponse {
    /// 笔记ID
    pub id: String,
}

/// 更新笔记响应DTO
#[derive(Debug, Serialize)]
pub struct UpdateNoteResponse {
    /// 笔记ID
    pub id: String,
}

/// 删除笔记请求DTO
#[derive(Debug, Deserialize)]
pub struct DeleteNoteRequest {
    /// 笔记ID
    pub id: String,
}

/// 删除笔记响应DTO
#[derive(Debug, Serialize)]
pub struct DeleteNoteResponse {
    /// 笔记ID
    pub id: String,
}

/// 笔记列表请求参数
#[derive(Debug, Deserialize)]
pub struct NoteListParams {
    /// 合集ID（可选，用于获取指定合集下的笔记）
    #[serde(default)]
    pub parent_id: Option<String>,

    /// 页码，从1开始
    #[serde(default = "default_page")]
    pub page: u32,

    /// 每页数量
    #[serde(default = "default_page_size")]
    pub page_size: u32,
}

/// 默认页码
#[inline]
fn default_page() -> u32 {
    1
}

/// 默认每页数量
#[inline]
fn default_page_size() -> u32 {
    10
}

/// 笔记列表响应DTO
#[derive(Debug, Serialize)]
pub struct NoteListResponse {
    /// 笔记列表
    pub notes: Vec<NoteDetailResponse>,

    /// 总数量
    pub total: u64,

    /// 当前页码
    pub page: u32,

    /// 每页数量
    pub page_size: u32,
}
