use serde::{Deserialize, Serialize};

/// 创建系统提示词请求DTO
#[derive(Debug, Deserialize)]
pub struct CreateSystemPromptRequest {
    /// 系统提示词标题（可选）
    #[serde(default)]
    pub title: Option<String>,

    /// 系统提示词内容（可选）
    #[serde(default)]
    pub content: Option<String>,

    /// 系统提示词分类（可选）
    #[serde(default)]
    pub category: Option<String>,

    /// 系统提示词标签（可选，多个标签用逗号分隔）
    #[serde(default)]
    pub tags: Option<String>,

    /// 是否启用（可选，0禁用 1启用，默认启用）
    #[serde(default)]
    pub is_enabled: Option<i8>,

    /// 排序权重（可选，默认为0）
    #[serde(default)]
    pub sort_weight: Option<i32>,
}

/// 系统提示词响应DTO
#[derive(Debug, Serialize, Clone)]
pub struct SystemPromptResponse {
    /// 系统提示词ID
    pub id: String,

    /// 系统提示词标题
    pub title: Option<String>,

    /// 系统提示词内容
    pub content: Option<String>,

    /// 系统提示词分类
    pub category: Option<String>,

    /// 系统提示词标签
    pub tags: Option<String>,

    /// 是否启用
    pub is_enabled: i8,

    /// 排序权重
    pub sort_weight: i32,

    /// 使用次数
    pub usage_count: u64,

    /// 创建时间
    pub created_at: i64,

    /// 更新时间
    pub updated_at: i64,
}

impl SystemPromptResponse {
    /// 从MySQL系统提示词模型创建响应DTO
    pub fn from_mysql_model(system_prompt: &crate::models::mysql::MySqlSystemPrompt) -> Self {
        Self {
            id: system_prompt.id.to_string(),
            title: system_prompt.title.clone(),
            content: system_prompt.content.clone(),
            category: system_prompt.category.clone(),
            tags: system_prompt.tags.clone(),
            is_enabled: system_prompt.is_enabled,
            sort_weight: system_prompt.sort_weight,
            usage_count: system_prompt.usage_count,
            created_at: system_prompt.created_at.timestamp_millis(),
            updated_at: system_prompt.updated_at.timestamp_millis(),
        }
    }
}

/// 更新系统提示词请求DTO
#[derive(Debug, Deserialize)]
pub struct UpdateSystemPromptRequest {
    /// 系统提示词ID
    pub id: String,

    /// 新标题（可选）
    #[serde(default)]
    pub title: Option<String>,

    /// 新内容（可选）
    #[serde(default)]
    pub content: Option<String>,

    /// 新分类（可选）
    #[serde(default)]
    pub category: Option<String>,

    /// 新标签（可选）
    #[serde(default)]
    pub tags: Option<String>,

    /// 是否启用（可选）
    #[serde(default)]
    pub is_enabled: Option<i8>,

    /// 排序权重（可选）
    #[serde(default)]
    pub sort_weight: Option<i32>,
}

/// 删除系统提示词请求DTO
#[derive(Debug, Deserialize)]
pub struct DeleteSystemPromptRequest {
    /// 系统提示词ID
    pub id: String,
}

/// 系统提示词详情查询参数DTO
#[derive(Debug, Deserialize)]
pub struct SystemPromptDetailParams {
    /// 系统提示词ID
    pub id: String,
}

/// 系统提示词列表查询参数DTO
#[derive(Debug, Deserialize)]
pub struct SystemPromptListParams {
    /// 页码，从1开始
    #[serde(default = "default_page")]
    pub page: u64,

    /// 每页大小，默认10，最大100
    #[serde(default = "default_page_size")]
    pub page_size: u64,

    /// 分类过滤（可选）
    #[serde(default)]
    pub category: Option<String>,

    /// 是否启用过滤（可选）
    #[serde(default)]
    pub is_enabled: Option<i8>,
}

/// 默认页码
fn default_page() -> u64 {
    1
}

/// 默认每页大小
fn default_page_size() -> u64 {
    10
}

/// 系统提示词列表响应DTO
#[derive(Debug, Serialize)]
pub struct SystemPromptListResponse {
    /// 系统提示词列表
    pub system_prompts: Vec<SystemPromptResponse>,

    /// 总数
    pub total: u64,

    /// 当前页码
    pub page: u64,

    /// 每页大小
    pub page_size: u64,

    /// 总页数
    pub total_pages: u64,
}

impl SystemPromptListResponse {
    /// 创建系统提示词列表响应
    pub fn new(system_prompts: Vec<SystemPromptResponse>, total: u64, page: u64, page_size: u64) -> Self {
        let total_pages = if total == 0 { 0 } else { (total + page_size - 1) / page_size };
        
        Self {
            system_prompts,
            total,
            page,
            page_size,
            total_pages,
        }
    }
}

/// 创建系统提示词响应DTO
#[derive(Debug, Serialize)]
pub struct CreateSystemPromptResponse {
    /// 系统提示词信息
    pub system_prompt: SystemPromptResponse,
}

/// 更新系统提示词响应DTO
#[derive(Debug, Serialize)]
pub struct UpdateSystemPromptResponse {
    /// 系统提示词信息
    pub system_prompt: SystemPromptResponse,
}

/// 删除系统提示词响应DTO
#[derive(Debug, Serialize)]
pub struct DeleteSystemPromptResponse {
    /// 被删除的系统提示词ID
    pub id: String,
}
