use serde::{Deserialize, Serialize};
use crate::enums::PlatformType;

/// 添加书签请求DTO
#[derive(Debug, Deserialize)]
pub struct AddBookmarkRequest {
    /// 博主名称，可选
    #[serde(default)]
    pub influencer_name: String,

    /// 博主头像，可选
    #[serde(default)]
    pub influencer_avatar: Option<String>,

    /// 封面
    pub cover: String,

    /// 标题
    pub title: String,

    /// 简介，可选
    #[serde(default)]
    pub desc: String,

    /// 原生跳转链接
    pub scheme_url: String,

    /// 收藏夹id
    pub parent_id: String,

    /// 标签列表（可选，如果不提供则自动创建默认标签）
    #[serde(default)]
    pub tags: Option<Vec<String>>,

    /// 平台类型（可选）
    #[serde(default)]
    pub platform_type: Option<PlatformType>,
}

/// 标签信息DTO
#[derive(Debug, Serialize, Clone)]
pub struct TagInfo {
    /// 标签ID
    pub id: String,

    /// 标签名称
    pub name: String,

    /// 标签背景颜色
    pub background_color: String,

    /// 标签文字颜色
    pub text_color: String,
}

/// 书签响应DTO
#[derive(Debug, Serialize, Clone)]
pub struct BookmarkResponse {
    /// 书签ID
    pub id: String,

    /// 博主名称
    pub influencer_name: String,

    /// 博主头像，可选
    #[serde(skip_serializing_if = "Option::is_none")]
    pub influencer_avatar: Option<String>,

    /// 封面
    pub cover: String,

    /// 标题
    pub title: String,

    /// 简介
    pub desc: String,

    /// 原生跳转链接
    pub scheme_url: String,

    /// 收藏夹id
    pub parent_id: String,

    /// 创建时间
    pub create_time: i64,

    /// 更新时间
    pub update_time: i64,

    /// 标签列表
    pub tags: Vec<TagInfo>,

    /// 平台类型（可选）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub platform_type: Option<PlatformType>,
}

impl BookmarkResponse {
    /// 从MySQL书签模型创建响应DTO（不包含标签）
    pub fn from_mysql_model(bookmark: &crate::models::mysql::MySqlBookmark) -> Self {
        Self {
            id: bookmark.id.to_string(),
            influencer_name: bookmark.influencer_name.clone(),
            influencer_avatar: bookmark.influencer_avatar.clone(),
            cover: bookmark.cover.clone(),
            title: bookmark.title.clone(),
            desc: bookmark.desc.clone(),
            scheme_url: bookmark.scheme_url.clone(),
            parent_id: bookmark.favorite_id.to_string(),
            create_time: bookmark.created_at.timestamp_millis(),
            update_time: bookmark.updated_at.timestamp_millis(),
            tags: Vec::new(), // 空标签列表，需要后续填充
            platform_type: bookmark.platform_type.clone(),
        }
    }

    /// 从MySQL书签模型和标签详情创建响应DTO
    pub fn from_mysql_model_with_tags(
        bookmark: &crate::models::mysql::MySqlBookmark,
        tag_details: &[crate::models::mysql::BookmarkTagDetail],
    ) -> Self {
        let bookmark_tags: Vec<TagInfo> = tag_details
            .iter()
            .filter(|detail| detail.bookmark_id == bookmark.id)
            .map(|detail| TagInfo {
                id: detail.tag_id.to_string(),
                name: detail.tag_name.clone(),
                background_color: detail.tag_background_color.clone(),
                text_color: detail.tag_text_color.clone(),
            })
            .collect();

        Self {
            id: bookmark.id.to_string(),
            influencer_name: bookmark.influencer_name.clone(),
            influencer_avatar: bookmark.influencer_avatar.clone(),
            cover: bookmark.cover.clone(),
            title: bookmark.title.clone(),
            desc: bookmark.desc.clone(),
            scheme_url: bookmark.scheme_url.clone(),
            parent_id: bookmark.favorite_id.to_string(),
            create_time: bookmark.created_at.timestamp_millis(),
            update_time: bookmark.updated_at.timestamp_millis(),
            tags: bookmark_tags,
            platform_type: bookmark.platform_type.clone(),
        }
    }
}

/// 更新书签请求DTO
#[derive(Debug, Deserialize)]
pub struct UpdateBookmarkRequest {
    /// 书签ID
    pub id: String,

    /// 标题
    pub title: String,

    /// 简介
    pub desc: String,

    /// 标签列表（可选，如果提供则更新标签）
    #[serde(default)]
    pub tags: Option<Vec<String>>,
}

/// 更新书签响应DTO
#[derive(Debug, Serialize)]
pub struct UpdateBookmarkResponse {
    /// 书签ID
    pub id: String,
}

/// 删除书签请求DTO
#[derive(Debug, Deserialize)]
pub struct DeleteBookmarkRequest {
    /// 书签ID
    pub id: String,
}

/// 删除书签响应DTO
#[derive(Debug, Serialize)]
pub struct DeleteBookmarkResponse {
    /// 被删除的书签ID
    pub id: String,
}

/// 给书签添加标签请求DTO
#[derive(Debug, Deserialize)]
pub struct AddBookmarkTagsRequest {
    /// 书签ID
    pub bookmark_id: String,

    /// 标签名称列表
    pub tag_names: Vec<String>,
}

/// 给书签添加标签响应DTO
#[derive(Debug, Serialize)]
pub struct AddBookmarkTagsResponse {
    /// 书签ID
    pub bookmark_id: String,

    /// 成功添加的标签数量
    pub added_count: usize,
}

/// 从书签移除标签请求DTO
#[derive(Debug, Deserialize)]
pub struct RemoveBookmarkTagsRequest {
    /// 书签ID
    pub bookmark_id: String,

    /// 标签ID列表
    pub tag_ids: Vec<String>,
}

/// 从书签移除标签响应DTO
#[derive(Debug, Serialize)]
pub struct RemoveBookmarkTagsResponse {
    /// 书签ID
    pub bookmark_id: String,

    /// 成功移除的标签数量
    pub removed_count: usize,
}

/// 获取书签标签请求参数
#[derive(Debug, Deserialize)]
pub struct GetBookmarkTagsParams {
    /// 书签ID
    pub bookmark_id: String,
}

/// 获取书签标签响应DTO
#[derive(Debug, Serialize)]
pub struct GetBookmarkTagsResponse {
    /// 书签ID
    pub bookmark_id: String,

    /// 标签列表
    pub tags: Vec<TagInfo>,
}