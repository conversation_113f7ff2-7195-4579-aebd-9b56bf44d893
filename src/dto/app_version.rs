use serde::{Deserialize, Serialize};

/// 版本检查响应数据
#[derive(Debug, Serialize, Deserialize)]
pub struct VersionCheckResponse {
    /// 版本号
    pub version: String,
    /// 更新描述
    pub update_description: Vec<String>,
}

/// 版本存储请求数据
#[derive(Debug, Serialize, Deserialize)]
pub struct VersionStoreRequest {
    /// 版本号
    pub version: String,
    /// 更新描述
    pub update_description: Vec<String>,
    /// 是否设置为当前版本
    #[serde(default)]
    pub is_current: bool,
}

/// 版本存储响应数据
#[derive(Debug, Serialize, Deserialize)]
pub struct VersionStoreResponse {
    /// 版本ID
    pub version_id: u64,
    /// 版本号
    pub version: String,
    /// 是否为当前版本
    pub is_current: bool,
}
