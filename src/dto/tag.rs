use serde::{Deserialize, Serialize};

/// 创建标签请求DTO
#[derive(Debug, Deserialize)]
pub struct CreateTagRequest {
    /// 标签名称
    pub name: String,

    /// 标签背景颜色（可选，默认为蓝色）
    #[serde(default)]
    pub background_color: Option<String>,

    /// 标签文字颜色（可选，默认为白色）
    #[serde(default)]
    pub text_color: Option<String>,
}

/// 标签响应DTO
#[derive(Debug, Serialize, Clone)]
pub struct TagResponse {
    /// 标签ID
    pub id: String,

    /// 标签名称
    pub name: String,

    /// 标签背景颜色
    pub background_color: String,

    /// 标签文字颜色
    pub text_color: String,

    /// 创建时间
    pub create_time: i64,

    /// 更新时间
    pub update_time: i64,
}

impl TagResponse {
    /// 从MySQL标签模型创建响应DTO
    pub fn from_mysql_model(tag: &crate::models::mysql::MySqlTag) -> Self {
        Self {
            id: tag.id.to_string(),
            name: tag.name.clone(),
            background_color: tag.background_color.clone(),
            text_color: tag.text_color.clone(),
            create_time: tag.created_at.timestamp_millis(),
            update_time: tag.updated_at.timestamp_millis(),
        }
    }
}

/// 标签统计响应DTO
#[derive(Debug, Serialize, Clone)]
pub struct TagWithCountResponse {
    /// 标签信息
    #[serde(flatten)]
    pub tag: TagResponse,

    /// 使用次数
    pub count: u64,
}

impl TagWithCountResponse {
    /// 从MySQL标签统计模型创建响应DTO
    pub fn from_mysql_model(tag_with_count: &crate::models::mysql::TagWithCount) -> Self {
        Self {
            tag: TagResponse::from_mysql_model(&tag_with_count.tag),
            count: tag_with_count.count,
        }
    }
}

/// 更新标签请求DTO
#[derive(Debug, Deserialize)]
pub struct UpdateTagRequest {
    /// 标签ID
    pub id: String,

    /// 新标签名称（可选）
    #[serde(default)]
    pub name: Option<String>,

    /// 新标签背景颜色（可选）
    #[serde(default)]
    pub background_color: Option<String>,

    /// 新标签文字颜色（可选）
    #[serde(default)]
    pub text_color: Option<String>,
}

/// 删除标签请求DTO
#[derive(Debug, Deserialize)]
pub struct DeleteTagRequest {
    /// 标签ID
    pub id: String,
}

/// 标签搜索请求参数
#[derive(Debug, Deserialize)]
pub struct TagSearchParams {
    /// 搜索关键词
    pub keyword: String,
}

/// 标签列表响应DTO
#[derive(Debug, Serialize)]
pub struct TagListResponse {
    /// 标签列表
    pub tags: Vec<TagWithCountResponse>,
}
