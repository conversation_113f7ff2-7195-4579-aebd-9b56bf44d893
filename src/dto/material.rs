use serde::{Deserialize, Serialize};

/// 添加素材请求DTO
#[derive(Debug, Deserialize)]
pub struct AddMaterialRequest {
    /// OSS地址
    pub url: String,

    /// 素材类型：1图片 2语音 3视频
    pub r#type: i8,
}

/// 素材响应DTO
#[derive(Debug, Serialize, Clone)]
pub struct MaterialResponse {
    /// 素材ID
    pub id: String,

    /// 用户ID
    pub user_id: String,

    /// OSS地址
    pub url: String,

    /// 创建时间（时间戳）
    pub create_time: i64,

    /// 更新时间（时间戳）
    pub update_time: i64,

    /// 状态：1正常 2已删除
    pub status: i8,

    /// 类型：1图片 2语音 3视频
    pub r#type: i8,
}

impl MaterialResponse {
    /// 从MySQL素材模型创建响应DTO
    pub fn from_mysql_model(material: &crate::models::mysql::MySqlMaterial) -> Self {
        Self {
            id: material.id.to_string(),
            user_id: material.user_id.to_string(),
            url: material.url.clone(),
            create_time: material.create_time.timestamp_millis(),
            update_time: material.update_time.timestamp_millis(),
            status: material.status.into(),
            r#type: material.r#type.into(),
        }
    }
}

/// 添加素材响应DTO
#[derive(Debug, Serialize)]
pub struct AddMaterialResponse {
    /// 素材信息
    pub material: MaterialResponse,
}

/// 删除素材请求DTO
#[derive(Debug, Deserialize)]
pub struct DeleteMaterialRequest {
    /// 素材ID
    pub id: String,
}

/// 删除素材响应DTO
#[derive(Debug, Serialize)]
pub struct DeleteMaterialResponse {
    /// 素材ID
    pub id: String,
}

/// 素材列表请求参数
#[derive(Debug, Deserialize)]
pub struct MaterialListParams {
    /// 页码，从1开始
    #[serde(default = "default_page")]
    pub page: u32,

    /// 每页数量
    #[serde(default = "default_page_size")]
    pub page_size: u32,

    /// 素材类型过滤（可选）：1图片 2语音 3视频
    pub r#type: Option<i8>,
}

/// 默认页码
#[inline]
fn default_page() -> u32 {
    1
}

/// 默认每页数量
#[inline]
fn default_page_size() -> u32 {
    20
}

/// 素材列表响应DTO
#[derive(Debug, Serialize)]
pub struct MaterialListResponse {
    /// 素材列表
    pub materials: Vec<MaterialResponse>,

    /// 总数量
    pub total: u64,

    /// 当前页码
    pub page: u32,

    /// 每页数量
    pub page_size: u32,
}
