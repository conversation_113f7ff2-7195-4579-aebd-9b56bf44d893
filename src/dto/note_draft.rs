use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// 笔记草稿详情响应DTO
#[derive(Debug, Serialize, Clone)]
pub struct NoteDraftDetailResponse {
    /// 草稿ID
    pub id: String,
    
    /// 笔记ID
    pub note_id: String,
    
    /// 草稿内容
    pub content: Option<String>,

    /// 创建时间
    pub create_time: DateTime<Utc>,
    
    /// 更新时间
    pub update_time: DateTime<Utc>,
}

impl NoteDraftDetailResponse {
    /// 从MySQL笔记草稿模型创建响应DTO
    pub fn from_mysql_model(draft: &crate::models::mysql::MySqlNoteDraft) -> Self {
        Self {
            id: draft.id.to_string(),
            note_id: draft.note_id.to_string(),
            content: draft.content.clone(),
            create_time: draft.create_time,
            update_time: draft.update_time,
        }
    }
}

/// 创建笔记草稿请求DTO
#[derive(Debug, Deserialize)]
pub struct CreateNoteDraftRequest {
    /// 笔记ID
    pub note_id: String,

    /// 草稿内容
    #[serde(default)]
    pub content: Option<String>,
}

/// 创建笔记草稿响应DTO
#[derive(Debug, Serialize)]
pub struct CreateNoteDraftResponse {
    /// 草稿ID
    pub id: String,
}

/// 更新笔记草稿请求DTO
#[derive(Debug, Deserialize)]
pub struct UpdateNoteDraftRequest {
    /// 笔记ID
    pub note_id: String,

    /// 草稿内容
    #[serde(default)]
    pub content: Option<String>,
}

/// 更新笔记草稿响应DTO
#[derive(Debug, Serialize)]
pub struct UpdateNoteDraftResponse {
    /// 草稿ID
    pub id: String,
}

/// 获取笔记草稿请求参数
#[derive(Debug, Deserialize)]
pub struct NoteDraftDetailParams {
    /// 笔记ID
    pub note_id: String,
}

/// 删除笔记草稿请求参数
#[derive(Debug, Deserialize)]
pub struct DeleteNoteDraftParams {
    /// 笔记ID
    pub note_id: String,
}

/// 笔记草稿列表请求参数
#[derive(Debug, Deserialize)]
pub struct NoteDraftListParams {
    /// 页码，从1开始
    #[serde(default = "default_page")]
    pub page: u32,

    /// 每页数量
    #[serde(default = "default_page_size")]
    pub page_size: u32,
}

/// 默认页码
#[inline]
fn default_page() -> u32 {
    1
}

/// 默认每页数量
#[inline]
fn default_page_size() -> u32 {
    10
}

/// 笔记草稿列表项响应DTO
#[derive(Debug, Serialize, Clone)]
pub struct NoteDraftListItem {
    /// 草稿ID
    pub id: String,

    /// 笔记ID
    pub note_id: String,

    /// 草稿内容
    pub content: Option<String>,

    /// 创建时间
    pub create_time: DateTime<Utc>,

    /// 更新时间
    pub update_time: DateTime<Utc>,

    /// 笔记标题
    pub note_title: String,

    /// 笔记封面
    pub note_cover: String,

    /// 笔记描述
    pub note_desc: String,
}

/// 笔记草稿列表响应DTO
#[derive(Debug, Serialize)]
pub struct NoteDraftListResponse {
    /// 草稿列表
    pub drafts: Vec<NoteDraftListItem>,

    /// 总数量
    pub total: u64,

    /// 当前页码
    pub page: u32,

    /// 每页数量
    pub page_size: u32,
}
