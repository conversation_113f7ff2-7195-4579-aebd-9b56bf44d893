use std::fmt;

/// 错误码枚举
///
/// 错误码规则：
/// - 0: 成功
/// - 1xx: 系统级错误
/// - 2xx: 用户相关错误
/// - 3xx: 业务逻辑错误
/// - 4xx: 数据校验错误
/// - 5xx: 外部服务错误
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
pub enum ErrorCode {
    // 成功
    Success = 0,

    // 系统级错误 (1xx)
    SystemError = 100,
    ServiceUnavailable = 101,
    DatabaseError = 102,
    ExternalServiceError = 103,

    // 用户相关错误 (2xx)
    UserAlreadyExists = 200,
    UserNotFound = 201,
    PasswordMismatch = 202,
    TokenInvalid = 203,
    TokenExpired = 204,

    // 数据校验错误 (4xx)
    ValidationError = 400,
    InvalidPhoneFormat = 401,
    PasswordTooShort = 402,
    InvalidParameter = 403,
    DataAlreadyExists = 404,
    InvalidInput = 405,
    DuplicateData = 406,
    LimitExceeded = 407,
    DataNotFound = 408,
    AccessDenied = 409,

    // 收藏夹错误 (3xx)
    FavoriteNameEmpty = 300,
    FavoriteNameTooLong = 301,
    FavoriteNotFound = 302,
    FavoriteCoverInvalidFormat = 303,
    FavoriteAlreadyExists = 304,
    InvalidUserId = 305,
    InvalidFavoriteId = 306,

    // 标签错误 (31x)
    TagNameEmpty = 310,
    TagNameTooLong = 311,
    TagAlreadyExists = 312,
    TagNotFound = 313,
    TagColorInvalid = 314,
    TagLimitExceeded = 315,

    // 任务错误 (32x)
    TaskNotFound = 320,
    PermissionDenied = 321,
}

impl ErrorCode {
    /// 获取错误码对应的错误消息
    pub fn message(&self) -> &'static str {
        match self {
            Self::Success => "成功",
            Self::SystemError => "系统错误",
            Self::ServiceUnavailable => "服务不可用",
            Self::DatabaseError => "数据库错误",
            Self::UserAlreadyExists => "用户已存在",
            Self::UserNotFound => "用户不存在",
            Self::PasswordMismatch => "密码不匹配",
            Self::TokenInvalid => "登录信息缺失",
            Self::TokenExpired => "登录信息过期",
            Self::ValidationError => "数据验证错误",
            Self::InvalidPhoneFormat => "手机号码格式不正确",
            Self::PasswordTooShort => "密码长度不足",
            Self::InvalidParameter => "参数无效",
            Self::DataAlreadyExists => "数据已存在",
            Self::FavoriteNameEmpty => "收藏夹名称不能为空",
            Self::FavoriteNameTooLong => "收藏夹名称不能超过12个字符",
            Self::FavoriteNotFound => "收藏夹不存在",
            Self::FavoriteCoverInvalidFormat => "收藏夹封面URL格式不正确，必须以http://或https://开头",
            Self::FavoriteAlreadyExists => "收藏夹名称已存在",
            Self::InvalidUserId => "用户ID格式无效",
            Self::InvalidFavoriteId => "收藏夹ID格式无效",
            Self::ExternalServiceError => "外部服务调用失败",
            Self::TagNameEmpty => "标签名称不能为空",
            Self::TagNameTooLong => "标签名称不能超过50个字符",
            Self::TagAlreadyExists => "标签名称已存在",
            Self::TagNotFound => "标签不存在",
            Self::TagColorInvalid => "标签颜色格式无效",
            Self::TagLimitExceeded => "标签数量已达上限，每个用户最多创建50个标签",
            Self::TaskNotFound => "任务不存在",
            Self::PermissionDenied => "权限不足",
            Self::InvalidInput => "输入数据无效",
            Self::DuplicateData => "数据重复",
            Self::LimitExceeded => "数量超过限制",
            Self::DataNotFound => "数据不存在",
            Self::AccessDenied => "访问被拒绝",
        }
    }

    /// 获取错误码的数值
    pub fn code(&self) -> i32 {
        *self as i32
    }
}

impl fmt::Display for ErrorCode {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}: {}", self.code(), self.message())
    }
}
