use serde::{Deserialize, Serialize};

/// 创建提示词请求DTO
#[derive(Debug, Deserialize)]
pub struct CreatePromptRequest {
    /// 提示词标题
    pub title: String,

    /// 提示词内容
    pub content: String,

    /// 提示词分类（可选）
    #[serde(default)]
    pub category: Option<String>,

    /// 提示词标签（可选，多个标签用逗号分隔）
    #[serde(default)]
    pub tags: Option<String>,

    /// 是否公开（可选，0私有 1公开，默认私有）
    #[serde(default)]
    pub is_public: Option<i8>,
}

/// 提示词响应DTO
#[derive(Debug, Serialize, Clone)]
pub struct PromptResponse {
    /// 提示词ID
    pub id: String,

    /// 用户ID
    pub user_id: String,

    /// 提示词标题
    pub title: String,

    /// 提示词内容
    pub content: String,

    /// 提示词分类
    pub category: Option<String>,

    /// 提示词标签
    pub tags: Option<String>,

    /// 是否公开
    pub is_public: i8,

    /// 使用次数
    pub usage_count: u64,

    /// 创建时间
    pub created_at: i64,

    /// 更新时间
    pub updated_at: i64,
}

impl PromptResponse {
    /// 从MySQL提示词模型创建响应DTO
    pub fn from_mysql_model(prompt: &crate::models::mysql::MySqlPrompt) -> Self {
        Self {
            id: prompt.id.to_string(),
            user_id: prompt.user_id.to_string(),
            title: prompt.title.clone(),
            content: prompt.content.clone(),
            category: prompt.category.clone(),
            tags: prompt.tags.clone(),
            is_public: prompt.is_public,
            usage_count: prompt.usage_count,
            created_at: prompt.created_at.timestamp_millis(),
            updated_at: prompt.updated_at.timestamp_millis(),
        }
    }
}

/// 更新提示词请求DTO
#[derive(Debug, Deserialize)]
pub struct UpdatePromptRequest {
    /// 提示词ID
    pub id: String,

    /// 新标题（可选）
    #[serde(default)]
    pub title: Option<String>,

    /// 新内容（可选）
    #[serde(default)]
    pub content: Option<String>,

    /// 新分类（可选）
    #[serde(default)]
    pub category: Option<String>,

    /// 新标签（可选）
    #[serde(default)]
    pub tags: Option<String>,

    /// 是否公开（可选）
    #[serde(default)]
    pub is_public: Option<i8>,
}

/// 删除提示词请求DTO
#[derive(Debug, Deserialize)]
pub struct DeletePromptRequest {
    /// 提示词ID
    pub id: String,
}

/// 提示词列表查询参数
#[derive(Debug, Deserialize)]
pub struct PromptListParams {
    /// 页码（从1开始，默认1）
    #[serde(default = "default_page")]
    pub page: u64,

    /// 每页大小（默认10，最大100）
    #[serde(default = "default_page_size")]
    pub page_size: u64,

    /// 分类过滤（可选）
    pub category: Option<String>,

    /// 关键词搜索（可选）
    pub keyword: Option<String>,
}

fn default_page() -> u64 {
    1
}

fn default_page_size() -> u64 {
    10
}

impl PromptListParams {
    /// 验证并调整参数
    pub fn validate(&mut self) {
        if self.page == 0 {
            self.page = 1;
        }
        if self.page_size == 0 || self.page_size > 100 {
            self.page_size = 10;
        }
    }
}

/// 提示词列表响应DTO
#[derive(Debug, Serialize)]
pub struct PromptListResponse {
    /// 提示词列表
    pub prompts: Vec<PromptResponse>,

    /// 总数
    pub total: u64,

    /// 当前页码
    pub page: u64,

    /// 每页大小
    pub page_size: u64,

    /// 总页数
    pub total_pages: u64,
}

impl PromptListResponse {
    /// 创建提示词列表响应
    pub fn new(prompts: Vec<PromptResponse>, total: u64, page: u64, page_size: u64) -> Self {
        let total_pages = if total == 0 { 0 } else { (total + page_size - 1) / page_size };
        
        Self {
            prompts,
            total,
            page,
            page_size,
            total_pages,
        }
    }
}

/// 创建提示词响应DTO
#[derive(Debug, Serialize)]
pub struct CreatePromptResponse {
    /// 提示词信息
    pub prompt: PromptResponse,
}

/// 更新提示词响应DTO
#[derive(Debug, Serialize)]
pub struct UpdatePromptResponse {
    /// 提示词信息
    pub prompt: PromptResponse,
}

/// 删除提示词响应DTO
#[derive(Debug, Serialize)]
pub struct DeletePromptResponse {
    /// 提示词ID
    pub id: String,
}
