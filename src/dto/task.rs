use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

use crate::models::mysql::{MySqlTask, TaskType, TaskStatus};

/// 创建任务请求DTO
#[derive(Debug, Deserialize)]
pub struct CreateTaskRequest {
    /// 任务类型：1提取文案 2下载视频 3创建笔记 4更新笔记
    pub task_type: i8,

    /// 任务标题
    pub title: String,

    /// 平台信息
    pub platform: String,

    /// 提取的地址URL
    pub url: String,

    /// 笔记ID（可选，当task_type为4时需要提供）
    pub note_id: Option<String>,
}

/// 创建任务响应DTO
#[derive(Debug, Serialize)]
pub struct CreateTaskResponse {
    /// 任务ID
    pub id: String,

    /// 任务类型
    pub task_type: i8,

    /// 任务标题
    pub title: String,

    /// 平台信息
    pub platform: String,

    /// 提取的地址URL
    pub url: String,

    /// 任务状态
    pub status: i8,

    /// 任务结果
    pub result: Option<String>,

    /// 笔记ID（可选）
    pub note_id: Option<String>,

    /// 创建时间
    pub create_time: DateTime<Utc>,

    /// 更新时间
    pub update_time: DateTime<Utc>,
}

/// 任务响应DTO
#[derive(Debug, Serialize)]
pub struct TaskResponse {
    /// 任务ID
    pub id: String,

    /// 任务类型
    pub task_type: i8,

    /// 任务标题
    pub title: String,

    /// 平台信息
    pub platform: String,

    /// 提取的地址URL
    pub url: String,

    /// 任务状态
    pub status: i8,

    /// 任务结果
    pub result: Option<String>,

    /// 笔记ID（可选）
    pub note_id: Option<String>,

    /// 创建时间
    pub create_time: DateTime<Utc>,

    /// 更新时间
    pub update_time: DateTime<Utc>,
}

/// 更新任务状态请求DTO
#[derive(Debug, Deserialize)]
pub struct UpdateTaskStatusRequest {
    /// 任务ID
    pub id: String,
    
    /// 新状态：1新创建 2已完成 3失败
    pub status: i8,
}

/// 任务查询参数DTO
#[derive(Debug, Deserialize)]
pub struct TaskQueryParams {
    /// 任务类型过滤（可选）
    pub task_type: Option<i8>,

    /// 平台过滤（可选）
    pub platform: Option<String>,

    /// 状态过滤（可选）
    pub status: Option<i8>,
}

/// 任务列表查询参数DTO
#[derive(Debug, Deserialize)]
pub struct TaskListParams {
    /// 页码，从1开始
    pub page: u64,

    /// 每页数量，最大100
    pub page_size: u64,

    /// 任务类型过滤（可选）
    pub task_type: Option<i8>,

    /// 平台过滤（可选）
    pub platform: Option<String>,

    /// 状态过滤（可选）
    pub status: Option<i8>,
}

/// 任务列表响应DTO
#[derive(Debug, Serialize)]
pub struct TaskListResponse {
    /// 任务列表
    pub tasks: Vec<TaskResponse>,

    /// 总数量
    pub total: u64,

    /// 当前页码
    pub page: u64,

    /// 每页数量
    pub page_size: u64,
}

impl CreateTaskResponse {
    /// 从MySQL任务模型创建响应DTO
    pub fn from_mysql_model(task: &MySqlTask) -> Self {
        Self {
            id: task.id.to_string(),
            task_type: i8::from(task.task_type),
            title: task.title.clone(),
            platform: task.platform.clone(),
            url: task.url.clone(),
            status: i8::from(task.status),
            result: task.result.clone(),
            note_id: task.note_id.map(|id| id.to_string()),
            create_time: task.create_time,
            update_time: task.update_time,
        }
    }
}

impl TaskResponse {
    /// 从MySQL任务模型创建响应DTO
    pub fn from_mysql_model(task: &MySqlTask) -> Self {
        Self {
            id: task.id.to_string(),
            task_type: i8::from(task.task_type),
            title: task.title.clone(),
            platform: task.platform.clone(),
            url: task.url.clone(),
            status: i8::from(task.status),
            result: task.result.clone(),
            note_id: task.note_id.map(|id| id.to_string()),
            create_time: task.create_time,
            update_time: task.update_time,
        }
    }
}

impl CreateTaskRequest {
    /// 验证请求参数
    pub fn validate(&self) -> Result<(), String> {
        // 验证任务类型
        if self.task_type < 1 || self.task_type > 4 {
            return Err("任务类型必须是1(提取文案)、2(下载视频)、3(创建笔记)或4(更新笔记)".to_string());
        }

        // 验证标题
        if self.title.trim().is_empty() {
            return Err("任务标题不能为空".to_string());
        }

        if self.title.len() > 255 {
            return Err("任务标题长度不能超过255个字符".to_string());
        }

        // 验证平台
        if self.platform.trim().is_empty() {
            return Err("平台信息不能为空".to_string());
        }

        if self.platform.len() > 50 {
            return Err("平台信息长度不能超过50个字符".to_string());
        }

        // 验证URL
        if self.url.trim().is_empty() {
            return Err("URL不能为空".to_string());
        }

        // 简单的URL格式验证
        if !self.url.starts_with("http://") && !self.url.starts_with("https://") {
            return Err("URL必须以http://或https://开头".to_string());
        }

        // 验证note_id（当task_type为4时，note_id是必需的）
        if self.task_type == 4 {
            if let Some(ref note_id_str) = self.note_id {
                if note_id_str.trim().is_empty() {
                    return Err("更新笔记任务必须提供笔记ID".to_string());
                }
                // 验证note_id格式
                if note_id_str.parse::<u64>().is_err() {
                    return Err("笔记ID格式无效".to_string());
                }
            } else {
                return Err("更新笔记任务必须提供笔记ID".to_string());
            }
        }

        Ok(())
    }
    
    /// 转换为任务类型枚举
    pub fn get_task_type(&self) -> TaskType {
        TaskType::from(self.task_type)
    }
}

impl UpdateTaskStatusRequest {
    /// 验证请求参数
    pub fn validate(&self) -> Result<(), String> {
        // 验证任务ID
        if self.id.trim().is_empty() {
            return Err("任务ID不能为空".to_string());
        }
        
        // 验证状态
        if self.status < 1 || self.status > 3 {
            return Err("任务状态必须是1(新创建)、2(已完成)或3(失败)".to_string());
        }
        
        Ok(())
    }
    
    /// 获取任务ID的u64值
    pub fn get_task_id(&self) -> Result<u64, String> {
        self.id.parse::<u64>()
            .map_err(|_| "无效的任务ID格式".to_string())
    }
    
    /// 转换为任务状态枚举
    pub fn get_task_status(&self) -> TaskStatus {
        TaskStatus::from(self.status)
    }
}
