use serde::{Deserialize, Serialize};

/// 用户注册请求数据结构
#[derive(Debug, Serialize, Deserialize)]
pub struct RegisterRequest {
    pub phone: String,
    pub password: String,
}

/// 用户注册响应数据
#[derive(Debug, Serialize, Deserialize)]
pub struct RegisterData {
    pub user_id: Option<String>,
    pub token: Option<String>,
}

/// 用户登录请求数据结构
#[derive(Debug, Serialize, Deserialize)]
pub struct LoginRequest {
    pub phone: String,
    pub password: String,
}

/// 用户登录响应数据
#[derive(Debug, Serialize, Deserialize)]
pub struct LoginData {
    pub user_id: String,
    pub phone: String,
    pub token: String,
}
