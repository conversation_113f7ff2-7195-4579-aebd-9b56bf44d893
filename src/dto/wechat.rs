use serde::{Deserialize, Serialize};

/// 微信登录请求
#[derive(Debug, Deserialize)]
pub struct WechatLoginRequest {
    /// 微信授权码
    pub code: String,
}

/// 微信登录响应数据
#[derive(Debug, Serialize)]
pub struct WechatLoginData {
    /// 用户ID
    pub user_id: String,
    
    /// 用户手机号（可能为空）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub phone: Option<String>,
    
    /// 用户昵称
    #[serde(skip_serializing_if = "Option::is_none")]
    pub nickname: Option<String>,
    
    /// 用户头像
    #[serde(skip_serializing_if = "Option::is_none")]
    pub avatar: Option<String>,
    
    /// 认证令牌
    pub token: String,
}
