use std::sync::Arc;
use redis::{self, Client, Connection, Commands, RedisResult};
use crate::config::RedisConfig;

/// Redis客户端包装器，提供简单的接口
#[derive(Clone)]
pub struct RedisClient {
    client: Arc<Client>,
}

impl RedisClient {
    /// 创建新的Redis客户端
    pub fn new(config: &RedisConfig) -> RedisResult<Self> {
        let redis_url = format!("redis://:{}@{}:{}", 
            config.password, config.host, config.port);
        
        // 创建客户端
        let client = Client::open(redis_url)?;
        
        // 检查连接
        let mut conn = client.get_connection()?;
        redis::cmd("PING").query::<String>(&mut conn)?;
        
        Ok(Self {
            client: Arc::new(client),
        })
    }
    
    /// 获取连接
    pub fn get_conn(&self) -> RedisResult<Connection> {
        self.client.get_connection()
    }
    
    /// 检查连接是否正常
    pub fn check_connection(&self) -> RedisResult<()> {
        let mut conn = self.get_conn()?;
        let result: String = redis::cmd("PING").query(&mut conn)?;
        
        if result == "PONG" {
            Ok(())
        } else {
            Err(redis::RedisError::from((redis::ErrorKind::ResponseError, "PING命令返回异常", result)))
        }
    }
    
    // 常用Redis操作的便捷方法
    
    /// 设置键值对
    pub fn set<K: AsRef<str>, V: redis::ToRedisArgs>(&self, key: K, value: V) -> RedisResult<()> {
        let mut conn = self.get_conn()?;
        conn.set(key.as_ref(), value)
    }
    
    /// 设置键值对，带过期时间（秒）
    pub fn set_ex<K: AsRef<str>, V: redis::ToRedisArgs>(&self, key: K, value: V, seconds: usize) -> RedisResult<()> {
        let mut conn = self.get_conn()?;
        conn.set_ex(key.as_ref(), value, seconds as u64)
    }
    
    /// 获取值
    pub fn get<K: AsRef<str>, T: redis::FromRedisValue>(&self, key: K) -> RedisResult<T> {
        let mut conn = self.get_conn()?;
        conn.get(key.as_ref())
    }
    
    /// 删除键
    pub fn del<K: AsRef<str>>(&self, key: K) -> RedisResult<()> {
        let mut conn = self.get_conn()?;
        let _: () = conn.del(key.as_ref())?;
        Ok(())
    }
    
    /// 检查键是否存在
    pub fn exists<K: AsRef<str>>(&self, key: K) -> RedisResult<bool> {
        let mut conn = self.get_conn()?;
        conn.exists(key.as_ref())
    }
    
    /// 设置过期时间（秒）
    pub fn expire<K: AsRef<str>>(&self, key: K, seconds: usize) -> RedisResult<bool> {
        let mut conn = self.get_conn()?;
        conn.expire(key.as_ref(), seconds as i64)
    }
    
    /// 执行自定义Redis命令
    pub fn execute_command<T: redis::FromRedisValue>(&self, cmd: &str, args: &[&str]) -> RedisResult<T> {
        let mut conn = self.get_conn()?;
        let mut command = redis::cmd(cmd);
        for arg in args {
            command.arg(*arg);
        }
        command.query(&mut conn)
    }
}
