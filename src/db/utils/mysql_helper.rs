use std::sync::Arc;
use sqlx::{MySql, Pool};
use crate::config::AppConfig;
use crate::db::DbConnections;

/// 获取MySQL连接池
pub fn get_mysql_pool(db_connections: &DbConnections) -> Option<&Pool<MySql>> {
    db_connections.mysql.as_ref().map(|client| client.pool())
}

/// 检查MySQL连接是否可用
pub fn is_mysql_available(db_connections: &DbConnections) -> bool {
    db_connections.mysql.is_some()
}

/// 获取MySQL数据库名称
pub fn get_mysql_database_name(config: &Arc<AppConfig>) -> &str {
    &config.mysql.database
}
