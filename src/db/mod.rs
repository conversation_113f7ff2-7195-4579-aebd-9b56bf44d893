use std::sync::Arc;
use redis::RedisError;
use sqlx::Error as SqlxError;
use crate::config::{AppConfig, MySqlConfig};

// 导出Redis客户端模块
mod redis_pool;
pub use redis_pool::RedisClient;

// 导出MySQL客户端模块
mod mysql_pool;
pub use mysql_pool::MySqlClient;

// 导出数据库表结构管理模块
mod schema_manager;

// 导出数据库工具模块
pub mod utils;

/// 数据库连接管理器
#[derive(Clone)]
pub struct DbConnections {
    /// Redis客户端（带连接池）
    pub redis: RedisClient,
    /// MySQL客户端（带连接池）
    pub mysql: Option<MySqlClient>,
}

impl DbConnections {
    /// 创建新的数据库连接
    pub async fn new(config: &Arc<AppConfig>) -> Result<Self, DbError> {
        // 初始化Redis连接池
        let redis_client = RedisClient::new(&config.redis)
            .map_err(|e| DbError::RedisError(e))?;

        // 初始化MySQL连接
        let mysql_client = match Self::init_mysql(&config.mysql).await {
            Ok(client) => Some(client),
            Err(e) => {
                log::warn!("MySQL连接初始化失败: {}", e);
                None
            }
        };

        Ok(Self {
            redis: redis_client,
            mysql: mysql_client,
        })
    }

    /// 初始化MySQL连接
    async fn init_mysql(config: &MySqlConfig) -> Result<MySqlClient, SqlxError> {
        MySqlClient::new(config).await
    }

    /// 检查所有数据库连接是否正常
    pub async fn check_connections(&self) -> Result<(), DbError> {
        // 检查Redis连接
        self.redis.check_connection()
            .map_err(|e| DbError::RedisError(e))?;

        // 检查MySQL连接（如果存在）
        if let Some(mysql) = &self.mysql {
            mysql.check_connection().await
                .map_err(|e| DbError::MySqlError(e))?;
        }

        Ok(())
    }

    /// 初始化数据库表结构
    pub async fn initialize_schema(&self, config: &Arc<AppConfig>) -> Result<(), DbError> {
        // 如果MySQL连接不可用，直接返回
        if self.mysql.is_none() {
            log::warn!("MySQL连接不可用，跳过表结构初始化");
            return Ok(());
        }

        // 获取MySQL连接池和数据库名称
        let pool = self.mysql.as_ref().unwrap().pool().clone();
        let database = config.mysql.database.clone();

        // 创建表结构管理器
        let schema_manager = schema_manager::SchemaManager::new(pool, database);

        // 初始化表结构
        schema_manager.initialize_schema().await
            .map_err(|e| DbError::SchemaManagerError(e))
    }
}

/// 数据库错误类型
#[derive(Debug, thiserror::Error)]
pub enum DbError {
    #[error("Redis error: {0}")]
    RedisError(#[from] RedisError),

    #[error("MySQL error: {0}")]
    MySqlError(#[from] SqlxError),

    #[error("Schema manager error: {0}")]
    SchemaManagerError(#[from] schema_manager::SchemaManagerError),
}
