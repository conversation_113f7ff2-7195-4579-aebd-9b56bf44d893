use std::sync::Arc;
use sqlx::{
    mysql::MySqlPoolOptions,
    MySql, Pool, Error as SqlxError,
};
use crate::config::MySqlConfig;

/// MySQL客户端包装器，提供连接池和简单的接口
#[derive(Clone)]
pub struct MySqlClient {
    /// 连接池
    pool: Arc<Pool<MySql>>,
}

impl MySqlClient {
    /// 创建新的MySQL客户端
    pub async fn new(config: &MySqlConfig) -> Result<Self, SqlxError> {
        // 构建连接字符串
        let connection_string = format!(
            "mysql://{}:{}@{}:{}/{}",
            config.username, config.password, config.host, config.port, config.database
        );
        
        // 创建连接池
        let pool = MySqlPoolOptions::new()
            .max_connections(config.max_connections)
            .connect(&connection_string)
            .await?;
        
        Ok(Self {
            pool: Arc::new(pool),
        })
    }
    
    /// 获取连接池引用
    pub fn pool(&self) -> &Pool<MySql> {
        &self.pool
    }
    
    /// 检查连接是否正常
    pub async fn check_connection(&self) -> Result<(), SqlxError> {
        // 执行简单查询以验证连接
        sqlx::query("SELECT 1")
            .execute(self.pool())
            .await?;
        
        Ok(())
    }
}
