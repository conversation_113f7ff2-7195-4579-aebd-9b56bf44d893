#[cfg(test)]
mod tests {
    use crate::models::mysql::{TaskType, TaskStatus};
    use crate::dto::task::{CreateTaskRequest, CreateTaskResponse};

    /// 测试CreateTaskRequest的验证功能
    #[test]
    fn test_create_task_request_validation() {
        // 测试有效请求
        let valid_request = CreateTaskRequest {
            task_type: 1,
            title: "测试任务".to_string(),
            platform: "bilibili".to_string(),
            url: "https://www.bilibili.com/video/BV1234567890".to_string(),
            note_id: None,
        };
        assert!(valid_request.validate().is_ok());

        // 测试无效任务类型
        let invalid_type_request = CreateTaskRequest {
            task_type: 0,
            title: "测试任务".to_string(),
            platform: "bilibili".to_string(),
            url: "https://www.bilibili.com/video/BV1234567890".to_string(),
            note_id: None,
        };
        assert!(invalid_type_request.validate().is_err());

        // 测试空标题
        let empty_title_request = CreateTaskRequest {
            task_type: 1,
            title: "".to_string(),
            platform: "bilibili".to_string(),
            url: "https://www.bilibili.com/video/BV1234567890".to_string(),
            note_id: None,
        };
        assert!(empty_title_request.validate().is_err());

        // 测试标题过长
        let long_title_request = CreateTaskRequest {
            task_type: 1,
            title: "a".repeat(256),
            platform: "bilibili".to_string(),
            url: "https://www.bilibili.com/video/BV1234567890".to_string(),
            note_id: None,
        };
        assert!(long_title_request.validate().is_err());

        // 测试空平台
        let empty_platform_request = CreateTaskRequest {
            task_type: 1,
            title: "测试任务".to_string(),
            platform: "".to_string(),
            url: "https://www.bilibili.com/video/BV1234567890".to_string(),
            note_id: None,
        };
        assert!(empty_platform_request.validate().is_err());

        // 测试空URL
        let empty_url_request = CreateTaskRequest {
            task_type: 1,
            title: "测试任务".to_string(),
            platform: "bilibili".to_string(),
            url: "".to_string(),
            note_id: None,
        };
        assert!(empty_url_request.validate().is_err());

        // 测试无效URL格式
        let invalid_url_request = CreateTaskRequest {
            task_type: 1,
            title: "测试任务".to_string(),
            platform: "bilibili".to_string(),
            url: "invalid-url".to_string(),
            note_id: None,
        };
        assert!(invalid_url_request.validate().is_err());
    }

    /// 测试任务类型转换
    #[test]
    fn test_task_type_conversion() {
        let request = CreateTaskRequest {
            task_type: 1,
            title: "测试任务".to_string(),
            platform: "bilibili".to_string(),
            url: "https://www.bilibili.com/video/BV1234567890".to_string(),
            note_id: None,
        };
        assert_eq!(request.get_task_type(), TaskType::ExtractContent);

        let request2 = CreateTaskRequest {
            task_type: 2,
            title: "测试任务".to_string(),
            platform: "bilibili".to_string(),
            url: "https://www.bilibili.com/video/BV1234567890".to_string(),
            note_id: None,
        };
        assert_eq!(request2.get_task_type(), TaskType::DownloadVideo);
    }

    /// 测试CreateTaskResponse的创建
    #[test]
    fn test_create_task_response_from_model() {
        use crate::models::mysql::MySqlTask;
        use chrono::Utc;

        let task = MySqlTask {
            id: 1,
            user_id: 1,
            task_type: TaskType::ExtractContent,
            title: "测试任务".to_string(),
            platform: "bilibili".to_string(),
            url: "https://www.bilibili.com/video/BV1234567890".to_string(),
            status: TaskStatus::Created,
            result: None,
            note_id: None,
            create_time: Utc::now(),
            update_time: Utc::now(),
        };

        let response = CreateTaskResponse::from_mysql_model(&task);
        assert_eq!(response.id, "1");
        assert_eq!(response.task_type, 1);
        assert_eq!(response.title, "测试任务");
        assert_eq!(response.platform, "bilibili");
        assert_eq!(response.url, "https://www.bilibili.com/video/BV1234567890");
        assert_eq!(response.status, 1);
    }

    /// 测试任务状态和类型枚举转换
    #[test]
    fn test_enum_conversions() {
        // 测试TaskType转换
        assert_eq!(TaskType::from(1), TaskType::ExtractContent);
        assert_eq!(TaskType::from(2), TaskType::DownloadVideo);
        assert_eq!(i8::from(TaskType::ExtractContent), 1);
        assert_eq!(i8::from(TaskType::DownloadVideo), 2);

        // 测试TaskStatus转换
        assert_eq!(TaskStatus::from(1), TaskStatus::Created);
        assert_eq!(TaskStatus::from(2), TaskStatus::Completed);
        assert_eq!(TaskStatus::from(3), TaskStatus::Failed);
        assert_eq!(i8::from(TaskStatus::Created), 1);
        assert_eq!(i8::from(TaskStatus::Completed), 2);
        assert_eq!(i8::from(TaskStatus::Failed), 3);
    }

    /// 测试任务模型的创建
    #[test]
    fn test_mysql_task_creation() {
        use crate::models::mysql::MySqlTask;

        let task = MySqlTask::new(
            1, // user_id
            TaskType::ExtractContent,
            "测试任务".to_string(),
            "bilibili".to_string(),
            "https://www.bilibili.com/video/BV1234567890".to_string(),
        );

        assert_eq!(task.id, 0); // 数据库会自动分配ID
        assert_eq!(task.task_type, TaskType::ExtractContent);
        assert_eq!(task.title, "测试任务");
        assert_eq!(task.platform, "bilibili");
        assert_eq!(task.url, "https://www.bilibili.com/video/BV1234567890");
        assert_eq!(task.status, TaskStatus::Created);
        assert_eq!(task.note_id, None);
        assert_eq!(MySqlTask::table_name(), "tasks");
    }

    /// 测试带笔记ID的任务创建
    #[test]
    fn test_mysql_task_creation_with_note_id() {
        use crate::models::mysql::MySqlTask;

        let task = MySqlTask::new_with_note_id(
            1, // user_id
            TaskType::UpdateNote,
            "更新笔记任务".to_string(),
            "bilibili".to_string(),
            "https://www.bilibili.com/video/BV1234567890".to_string(),
            Some(123),
        );

        assert_eq!(task.id, 0); // 数据库会自动分配ID
        assert_eq!(task.user_id, 1);
        assert_eq!(task.task_type, TaskType::UpdateNote);
        assert_eq!(task.title, "更新笔记任务");
        assert_eq!(task.platform, "bilibili");
        assert_eq!(task.url, "https://www.bilibili.com/video/BV1234567890");
        assert_eq!(task.status, TaskStatus::Created);
        assert_eq!(task.note_id, Some(123));
    }

    /// 测试任务类型4的验证
    #[test]
    fn test_update_note_task_validation() {
        // 测试有效的更新笔记任务
        let valid_request = CreateTaskRequest {
            task_type: 4,
            title: "更新笔记任务".to_string(),
            platform: "bilibili".to_string(),
            url: "https://www.bilibili.com/video/BV1234567890".to_string(),
            note_id: Some("123".to_string()),
        };
        assert!(valid_request.validate().is_ok());

        // 测试缺少note_id的更新笔记任务
        let invalid_request = CreateTaskRequest {
            task_type: 4,
            title: "更新笔记任务".to_string(),
            platform: "bilibili".to_string(),
            url: "https://www.bilibili.com/video/BV1234567890".to_string(),
            note_id: None,
        };
        assert!(invalid_request.validate().is_err());

        // 测试空note_id的更新笔记任务
        let empty_note_id_request = CreateTaskRequest {
            task_type: 4,
            title: "更新笔记任务".to_string(),
            platform: "bilibili".to_string(),
            url: "https://www.bilibili.com/video/BV1234567890".to_string(),
            note_id: Some("".to_string()),
        };
        assert!(empty_note_id_request.validate().is_err());

        // 测试无效格式note_id的更新笔记任务
        let invalid_format_request = CreateTaskRequest {
            task_type: 4,
            title: "更新笔记任务".to_string(),
            platform: "bilibili".to_string(),
            url: "https://www.bilibili.com/video/BV1234567890".to_string(),
            note_id: Some("invalid".to_string()),
        };
        assert!(invalid_format_request.validate().is_err());
    }
}
