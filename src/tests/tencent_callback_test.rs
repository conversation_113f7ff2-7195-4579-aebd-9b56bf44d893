#[cfg(test)]
mod tests {
    use crate::handlers::tencent_cloud::callback::get_error_message_by_code;

    #[test]
    fn test_get_error_message_by_code() {
        // 测试所有已知的错误码
        assert_eq!(get_error_message_by_code(10000), "转码失败，请确认音频格式是否符合标准");
        assert_eq!(get_error_message_by_code(10001), "识别失败");
        assert_eq!(get_error_message_by_code(10002), "语音时长太短");
        assert_eq!(get_error_message_by_code(10003), "语音时长太长");
        assert_eq!(get_error_message_by_code(10004), "无效的语音文件");
        assert_eq!(get_error_message_by_code(10005), "其他失败");
        assert_eq!(get_error_message_by_code(10006), "音轨个数不匹配");
        assert_eq!(get_error_message_by_code(10007), "音频下载失败");
        
        // 测试未知错误码
        assert_eq!(get_error_message_by_code(99999), "未知错误，错误码: 99999");
        assert_eq!(get_error_message_by_code(0), "未知错误，错误码: 0");
        assert_eq!(get_error_message_by_code(-1), "未知错误，错误码: -1");
    }
}
