#!/bin/bash

# 腾讯云语音识别API测试脚本

echo "🚀 开始测试腾讯云语音识别API..."

# 服务器地址
SERVER_URL="http://localhost:8080"

# 测试1: 配置测试
echo ""
echo "📋 测试1: 腾讯云配置测试"
curl -s -X GET "${SERVER_URL}/test/tencent_cloud_config" | jq '.'

# 测试2: 服务测试
echo ""
echo "🔧 测试2: 腾讯云语音识别服务测试"
curl -s -X GET "${SERVER_URL}/test/tencent_cloud_asr" | jq '.'

# 测试3: 创建语音识别任务（完整参数）
echo ""
echo "🎯 测试3: 创建语音识别任务（完整参数）"
curl -s -X POST "${SERVER_URL}/tencent_cloud/asr/create_task" \
  -H "Content-Type: application/json" \
  -d '{
    "version": "2019-06-14",
    "source_type": 0,
    "need_times": "1",
    "engine_model_type": "16k_zh_video",
    "channel_num": 1,
    "skey": "p8HLH5rmtphUTLsmc2kvAkHsTc1GltWkKcSjESeYsbs_",
    "customization_id": "",
    "hotword_id": "",
    "urls": [
      "https://v5-ali-colda.douyinvod.com/5f82c4d37a0858aafdac4bf1a515dc9c/685269f1/video/tos/cn/tos-cn-ve-15/ooZcaBaEQIAh8i2QlimTNWTI9PBFvAFCGwP6E/?a=1128&ch=0&cr=0&dr=0&lr=aweme_search_suffix&cd=0%7C0%7C1%7C0&cv=1&br=1157&bt=1157&cs=0&ds=3&ft=3pbegxTqRR0s~hC32D12Nc0iPMgzbLZt4idU_4C~nKV9Nv7TGW&mime_type=video_mp4&qs=0&rc=ZTQ1OTtoaTw0NTg3NmU0O0BpM21vNnc5cjRwNDMzNGkzM0BhXzAvYTExNV8xMzQ1MWMvYSNvM3AuMmRrYS1hLS1kLTBzcw%3D%3D&btag=80010e00098000&cquery=100y&dy_q=1750227902&feature_id=dc6e471fd69cf67451806384e04f2b47&l=20250618142502DCF87D1746077458E56B"
    ],
    "speaker_number": 0,
    "callback_url": null
  }' | jq '.'

# 测试4: 创建语音识别任务（最简参数）
echo ""
echo "⚡ 测试4: 创建语音识别任务（最简参数）"
curl -s -X POST "${SERVER_URL}/tencent_cloud/asr/create_task" \
  -H "Content-Type: application/json" \
  -d '{
    "urls": [
      "https://v5-ali-colda.douyinvod.com/5f82c4d37a0858aafdac4bf1a515dc9c/685269f1/video/tos/cn/tos-cn-ve-15/ooZcaBaEQIAh8i2QlimTNWTI9PBFvAFCGwP6E/?a=1128&ch=0&cr=0&dr=0&lr=aweme_search_suffix&cd=0%7C0%7C1%7C0&cv=1&br=1157&bt=1157&cs=0&ds=3&ft=3pbegxTqRR0s~hC32D12Nc0iPMgzbLZt4idU_4C~nKV9Nv7TGW&mime_type=video_mp4&qs=0&rc=ZTQ1OTtoaTw0NTg3NmU0O0BpM21vNnc5cjRwNDMzNGkzM0BhXzAvYTExNV8xMzQ1MWMvYSNvM3AuMmRrYS1hLS1kLTBzcw%3D%3D&btag=80010e00098000&cquery=100y&dy_q=1750227902&feature_id=dc6e471fd69cf67451806384e04f2b47&l=20250618142502DCF87D1746077458E56B"
    ]
  }' | jq '.'

echo ""
echo "✅ 测试完成！"
echo ""
echo "📝 使用说明："
echo "1. 确保服务器在 http://localhost:8080 运行"
echo "2. 安装 jq 工具用于格式化JSON输出: brew install jq"
echo "3. 运行此脚本: chmod +x test_tencent_asr.sh && ./test_tencent_asr.sh"
echo ""
echo "🔗 API端点："
echo "- 配置测试: GET /test/tencent_cloud_config"
echo "- 服务测试: GET /test/tencent_cloud_asr"
echo "- 创建任务: POST /tencent_cloud/asr/create_task"
