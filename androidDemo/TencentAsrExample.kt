package com.aishoucang.tencent.asr.example

import android.content.Context
import android.util.Log
import com.aishoucang.tencent.asr.*
import kotlinx.coroutines.*

/**
 * 腾讯云ASR使用示例
 */
class TencentAsrExample(private val context: Context) {
    
    companion object {
        private const val TAG = "TencentAsrExample"
        
        // 测试配置 - 实际使用时请替换为真实的密钥
        private const val SECRET_ID = "你的腾讯云SecretId"
        private const val SECRET_KEY = "你的腾讯云SecretKey"
        
        // 测试音频URL
        private const val TEST_AUDIO_URL = "https://example.com/test-audio.wav"
        
        // 回调URL（可选）
        private const val CALLBACK_URL = "https://your-server.com/tencent-asr-callback"
    }
    
    private val config = TencentCloudConfig(
        secretId = SECRET_ID,
        secretKey = SECRET_KEY
    )
    
    private val asrManager = TencentAsrManager(context, config)
    
    /**
     * 示例1：简单的语音识别任务（回调方式）
     */
    fun example1SimpleAsrTask() {
        Log.i(TAG, "=== 示例1：简单的语音识别任务 ===")
        
        asrManager.createSimpleAsrTask(TEST_AUDIO_URL, object : TencentAsrManager.AsrTaskCallback {
            override fun onSuccess(taskId: Long, requestId: String) {
                Log.i(TAG, "✅ 语音识别任务创建成功")
                Log.i(TAG, "任务ID: $taskId")
                Log.i(TAG, "请求ID: $requestId")
            }
            
            override fun onError(error: TencentAsrServiceError) {
                Log.e(TAG, "❌ 语音识别任务创建失败: ${error.message}")
                handleError(error)
            }
        })
    }
    
    /**
     * 示例2：带回调URL的语音识别任务
     */
    fun example2AsrTaskWithCallback() {
        Log.i(TAG, "=== 示例2：带回调URL的语音识别任务 ===")
        
        asrManager.createAsrTaskWithCallback(
            audioUrl = TEST_AUDIO_URL,
            callbackUrl = CALLBACK_URL,
            callback = object : TencentAsrManager.AsrTaskCallback {
                override fun onSuccess(taskId: Long, requestId: String) {
                    Log.i(TAG, "✅ 带回调的语音识别任务创建成功")
                    Log.i(TAG, "任务ID: $taskId")
                    Log.i(TAG, "请求ID: $requestId")
                    Log.i(TAG, "回调URL: $CALLBACK_URL")
                }
                
                override fun onError(error: TencentAsrServiceError) {
                    Log.e(TAG, "❌ 带回调的语音识别任务创建失败: ${error.message}")
                    handleError(error)
                }
            }
        )
    }
    
    /**
     * 示例3：自定义配置的语音识别任务
     */
    fun example3CustomAsrTask() {
        Log.i(TAG, "=== 示例3：自定义配置的语音识别任务 ===")
        
        val customConfig = TencentAsrManager.AsrTaskConfig(
            url = TEST_AUDIO_URL,
            sourceType = 0, // 语音URL
            engineModelType = "16k_zh", // 中文通用模型
            channelNum = 1, // 单声道
            speakerNumber = 2, // 指定2个说话人分离
            callbackUrl = CALLBACK_URL,
            resTextFormat = 3 // 详细格式
        )
        
        asrManager.createAsrTask(customConfig, object : TencentAsrManager.AsrTaskCallback {
            override fun onSuccess(taskId: Long, requestId: String) {
                Log.i(TAG, "✅ 自定义配置的语音识别任务创建成功")
                Log.i(TAG, "任务ID: $taskId")
                Log.i(TAG, "请求ID: $requestId")
                Log.i(TAG, "引擎模型: ${customConfig.engineModelType}")
                Log.i(TAG, "说话人数量: ${customConfig.speakerNumber}")
            }
            
            override fun onError(error: TencentAsrServiceError) {
                Log.e(TAG, "❌ 自定义配置的语音识别任务创建失败: ${error.message}")
                handleError(error)
            }
        })
    }
    
    /**
     * 示例4：使用协程的语音识别任务
     */
    fun example4CoroutineAsrTask() {
        Log.i(TAG, "=== 示例4：使用协程的语音识别任务 ===")
        
        // 在协程作用域中执行
        CoroutineScope(Dispatchers.Main).launch {
            try {
                val config = TencentAsrManager.AsrTaskConfig(
                    url = TEST_AUDIO_URL,
                    callbackUrl = CALLBACK_URL
                )
                
                val result = asrManager.createAsrTaskSuspend(config)
                
                when (result) {
                    is TencentAsrManager.AsrTaskResult.Success -> {
                        Log.i(TAG, "✅ 协程方式的语音识别任务创建成功")
                        Log.i(TAG, "任务ID: ${result.taskId}")
                        Log.i(TAG, "请求ID: ${result.requestId}")
                    }
                    is TencentAsrManager.AsrTaskResult.Error -> {
                        Log.e(TAG, "❌ 协程方式的语音识别任务创建失败: ${result.error.message}")
                        handleError(result.error)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "协程执行异常", e)
            }
        }
    }
    
    /**
     * 示例5：批量创建语音识别任务
     */
    fun example5BatchAsrTasks() {
        Log.i(TAG, "=== 示例5：批量创建语音识别任务 ===")
        
        val audioUrls = listOf(
            "https://example.com/audio1.wav",
            "https://example.com/audio2.wav",
            "https://example.com/audio3.wav"
        )
        
        CoroutineScope(Dispatchers.Main).launch {
            audioUrls.forEachIndexed { index, audioUrl ->
                try {
                    val config = TencentAsrManager.AsrTaskConfig(
                        url = audioUrl,
                        callbackUrl = "$CALLBACK_URL?batch_id=$index"
                    )
                    
                    val result = asrManager.createAsrTaskSuspend(config)
                    
                    when (result) {
                        is TencentAsrManager.AsrTaskResult.Success -> {
                            Log.i(TAG, "✅ 批量任务 $index 创建成功，任务ID: ${result.taskId}")
                        }
                        is TencentAsrManager.AsrTaskResult.Error -> {
                            Log.e(TAG, "❌ 批量任务 $index 创建失败: ${result.error.message}")
                        }
                    }
                    
                    // 避免请求过于频繁，添加延迟
                    delay(1000)
                    
                } catch (e: Exception) {
                    Log.e(TAG, "批量任务 $index 执行异常", e)
                }
            }
        }
    }
    
    /**
     * 处理错误的通用方法
     */
    private fun handleError(error: TencentAsrServiceError) {
        when (error) {
            is TencentAsrServiceError.HttpError -> {
                Log.e(TAG, "🔍 HTTP错误详情: ${error.message}")
                // 可能是网络问题，建议重试
            }
            is TencentAsrServiceError.ApiError -> {
                Log.e(TAG, "🔍 API错误详情: ${error.message}")
                // 可能是参数错误或配置问题
            }
            is TencentAsrServiceError.JsonError -> {
                Log.e(TAG, "🔍 JSON解析错误详情: ${error.message}")
                // 响应格式异常
            }
            is TencentAsrServiceError.SignatureError -> {
                Log.e(TAG, "🔍 签名错误详情: ${error.message}")
                // 检查密钥配置
            }
            is TencentAsrServiceError.ParameterError -> {
                Log.e(TAG, "🔍 参数错误详情: ${error.message}")
                // 检查请求参数
            }
        }
    }
    
    /**
     * 运行所有示例
     */
    fun runAllExamples() {
        Log.i(TAG, "🚀 开始运行腾讯云ASR示例")
        
        // 依次运行各个示例（实际使用时根据需要选择）
        example1SimpleAsrTask()
        
        // 添加延迟避免请求过于频繁
        CoroutineScope(Dispatchers.Main).launch {
            delay(2000)
            example2AsrTaskWithCallback()
            
            delay(2000)
            example3CustomAsrTask()
            
            delay(2000)
            example4CoroutineAsrTask()
            
            delay(2000)
            example5BatchAsrTasks()
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        asrManager.destroy()
        Log.i(TAG, "示例清理完成")
    }
}
