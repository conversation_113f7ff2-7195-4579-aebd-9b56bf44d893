package com.aishoucang.tencent.asr

import android.content.Context
import android.util.Log
import kotlinx.coroutines.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.SerialName

/**
 * 腾讯云ASR管理器，用于简化语音识别任务的创建和管理
 */
class TencentAsrManager(
    private val context: Context,
    private val config: TencentCloudConfig
) {
    
    companion object {
        private const val TAG = "TencentAsrManager"
        
        // 默认参数
        const val DEFAULT_SOURCE_TYPE = 0 // 语音URL
        const val DEFAULT_ENGINE_MODEL_TYPE = "16k_zh_video" // 视频模型
        const val DEFAULT_CHANNEL_NUM = 1 // 单声道
        const val DEFAULT_SPEAKER_NUMBER = 0 // 自动分离说话人
        const val DEFAULT_RES_TEXT_FORMAT = 3 // 详细格式
    }
    
    private val asrService = TencentAsrService(config)
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    /**
     * ASR任务配置
     */
    data class AsrTaskConfig(
        val url: String,
        val sourceType: Int = DEFAULT_SOURCE_TYPE,
        val engineModelType: String = DEFAULT_ENGINE_MODEL_TYPE,
        val channelNum: Int = DEFAULT_CHANNEL_NUM,
        val customizationId: String? = null,
        val hotwordId: String? = null,
        val speakerNumber: Int = DEFAULT_SPEAKER_NUMBER,
        val callbackUrl: String? = null,
        val resTextFormat: Int = DEFAULT_RES_TEXT_FORMAT
    )
    
    /**
     * ASR任务结果
     */
    sealed class AsrTaskResult {
        data class Success(val taskId: Long, val requestId: String) : AsrTaskResult()
        data class Error(val error: TencentAsrServiceError) : AsrTaskResult()
    }
    
    /**
     * ASR任务回调接口
     */
    interface AsrTaskCallback {
        fun onSuccess(taskId: Long, requestId: String)
        fun onError(error: TencentAsrServiceError)
    }
    
    /**
     * 创建语音识别任务（异步回调方式）
     */
    fun createAsrTask(config: AsrTaskConfig, callback: AsrTaskCallback) {
        scope.launch {
            try {
                val result = createAsrTaskSuspend(config)
                when (result) {
                    is AsrTaskResult.Success -> {
                        callback.onSuccess(result.taskId, result.requestId)
                    }
                    is AsrTaskResult.Error -> {
                        callback.onError(result.error)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "创建ASR任务异常", e)
                val error = if (e is TencentAsrServiceError) e 
                           else TencentAsrServiceError.ParameterError(e.message ?: "未知错误")
                callback.onError(error)
            }
        }
    }
    
    /**
     * 创建语音识别任务（协程方式）
     */
    suspend fun createAsrTaskSuspend(config: AsrTaskConfig): AsrTaskResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.i(TAG, "开始创建语音识别任务，URL: ${config.url}")
                
                val request = CreateRecTaskRequest(
                    sourceType = config.sourceType,
                    engineModelType = config.engineModelType,
                    channelNum = config.channelNum,
                    customizationId = config.customizationId,
                    hotwordId = config.hotwordId,
                    url = config.url,
                    speakerNumber = config.speakerNumber,
                    callbackUrl = config.callbackUrl,
                    resTextFormat = config.resTextFormat
                )
                
                val response = asrService.createRecTask(request)
                
                Log.i(TAG, "语音识别任务创建成功 - 任务ID: ${response.data.taskId}, 请求ID: ${response.requestId}")
                AsrTaskResult.Success(response.data.taskId, response.requestId)
                
            } catch (e: TencentAsrServiceError) {
                Log.e(TAG, "创建语音识别任务失败", e)
                AsrTaskResult.Error(e)
            } catch (e: Exception) {
                Log.e(TAG, "创建语音识别任务异常", e)
                val error = TencentAsrServiceError.ParameterError(e.message ?: "未知错误")
                AsrTaskResult.Error(error)
            }
        }
    }
    
    /**
     * 创建简单的语音识别任务（只需要URL）
     */
    fun createSimpleAsrTask(audioUrl: String, callback: AsrTaskCallback) {
        val config = AsrTaskConfig(url = audioUrl)
        createAsrTask(config, callback)
    }
    
    /**
     * 创建带回调的语音识别任务
     */
    fun createAsrTaskWithCallback(audioUrl: String, callbackUrl: String, callback: AsrTaskCallback) {
        val config = AsrTaskConfig(
            url = audioUrl,
            callbackUrl = callbackUrl
        )
        createAsrTask(config, callback)
    }
    
    /**
     * 销毁管理器，取消所有正在进行的任务
     */
    fun destroy() {
        scope.cancel()
        Log.i(TAG, "TencentAsrManager已销毁")
    }
}

/**
 * 腾讯云语音识别回调数据模型
 */
@Serializable
data class TencentAsrCallbackData(
    /** 任务状态码，0为成功，其他：失败 */
    val code: Long,
    
    /** 失败原因文字描述，成功时此值为空 */
    val message: String? = null,
    
    /** 任务唯一标识，与录音识别请求中返回的 TaskId 一致 */
    @SerialName("requestId")
    val requestId: Long,
    
    /** 腾讯云应用 ID */
    val appid: Long,
    
    /** 腾讯云项目 ID */
    val projectid: Long,
    
    /** 语音 url，如创建任务时为上传数据的方式，则不包含该字段 */
    @SerialName("audioUrl")
    val audioUrl: String? = null,
    
    /** 识别出的结果文本 */
    val text: String? = null,
    
    /** 包含详细识别结果，如创建任务时 ResTextFormat 为0，则不包含该字段 */
    @SerialName("resultDetail")
    val resultDetail: String? = null,
    
    /** 语音总时长 */
    @SerialName("audioTime")
    val audioTime: Double? = null
)

/**
 * 腾讯云ASR错误码映射
 */
object TencentAsrErrorCode {
    
    /**
     * 根据错误码获取标准化的错误信息
     */
    fun getErrorMessage(code: Long): String {
        return when (code) {
            10000L -> "音频格式错误，请检查音频文件格式"
            10001L -> "音频时长超出限制，请使用较短的音频文件"
            10002L -> "音频文件损坏或无法解析"
            10003L -> "音频下载失败，请检查音频URL是否可访问"
            10004L -> "音频内容为空或静音"
            10005L -> "音频采样率不支持，请使用16kHz采样率"
            10006L -> "音频声道数不支持，请使用单声道音频"
            10007L -> "音频编码格式不支持"
            else -> "语音识别失败，错误码: $code"
        }
    }
    
    /**
     * 判断是否为成功状态
     */
    fun isSuccess(code: Long): Boolean = code == 0L
}
