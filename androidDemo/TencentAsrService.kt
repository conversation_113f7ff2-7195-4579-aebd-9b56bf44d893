package com.aishoucang.tencent.asr

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.SerialName
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import java.security.MessageDigest
import java.text.SimpleDateFormat
import java.util.*
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

/**
 * 腾讯云语音识别服务错误类型
 */
sealed class TencentAsrServiceError(message: String) : Exception(message) {
    class HttpError(message: String) : TencentAsrServiceError("HTTP请求错误: $message")
    class ApiError(message: String) : TencentAsrServiceError("腾讯云API返回错误: $message")
    class JsonError(message: String) : TencentAsrServiceError("JSON解析错误: $message")
    class SignatureError(message: String) : TencentAsrServiceError("签名计算错误: $message")
    class ParameterError(message: String) : TencentAsrServiceError("参数错误: $message")
}

/**
 * 腾讯云配置
 */
data class TencentCloudConfig(
    val secretId: String,
    val secretKey: String
)

/**
 * 创建语音识别任务请求
 */
@Serializable
data class CreateRecTaskRequest(
    @SerialName("SourceType")
    val sourceType: Int,
    
    @SerialName("EngineModelType")
    val engineModelType: String,
    
    @SerialName("ChannelNum")
    val channelNum: Int,
    
    @SerialName("CustomizationId")
    val customizationId: String? = null,
    
    @SerialName("HotwordId")
    val hotwordId: String? = null,
    
    @SerialName("Url")
    val url: String,
    
    @SerialName("SpeakerNumber")
    val speakerNumber: Int,
    
    @SerialName("CallbackUrl")
    val callbackUrl: String? = null,
    
    @SerialName("ResTextFormat")
    val resTextFormat: Int
)

/**
 * 创建语音识别任务响应数据
 */
@Serializable
data class CreateRecTaskData(
    @SerialName("TaskId")
    val taskId: Long
)

/**
 * 创建语音识别任务响应
 */
@Serializable
data class CreateRecTaskResponse(
    @SerialName("Data")
    val data: CreateRecTaskData,
    
    @SerialName("RequestId")
    val requestId: String
)

/**
 * 腾讯云语音识别服务
 */
class TencentAsrService(private val config: TencentCloudConfig) {
    
    companion object {
        private const val TAG = "TencentAsrService"
        private const val HOST = "asr.tencentcloudapi.com"
        private const val SERVICE = "asr"
        private const val REGION = ""
        private const val ACTION = "CreateRecTask"
        private const val VERSION = "2019-06-14"
        private const val ALGORITHM = "TC3-HMAC-SHA256"
        private const val SIGNED_HEADERS = "content-type;host"
        private const val HTTP_METHOD = "POST"
        private const val CANONICAL_URI = "/"
        private const val CANONICAL_QUERY_STRING = ""
        private const val CONTENT_TYPE = "application/json; charset=utf-8"
    }
    
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
        .writeTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
        .build()
    
    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    /**
     * 创建语音识别任务
     */
    suspend fun createRecTask(request: CreateRecTaskRequest): CreateRecTaskResponse {
        return withContext(Dispatchers.IO) {
            Log.i(TAG, "开始创建腾讯云语音识别任务")
            
            // 获取当前时间戳
            val timestamp = System.currentTimeMillis() / 1000
            val date = getDate(timestamp)
            
            // 序列化请求体
            val payload = json.encodeToString(request)
            Log.d(TAG, "请求体: $payload")
            
            // 计算签名
            val authorization = calculateAuthorization(
                HOST, SERVICE, REGION, ACTION, VERSION, timestamp, date, payload
            )
            
            // 构建HTTP请求
            val url = "https://$HOST"
            val requestBody = payload.toRequestBody(CONTENT_TYPE.toMediaType())
            
            val httpRequest = Request.Builder()
                .url(url)
                .post(requestBody)
                .addHeader("Authorization", authorization)
                .addHeader("Content-Type", CONTENT_TYPE)
                .addHeader("Host", HOST)
                .addHeader("X-TC-Action", ACTION)
                .addHeader("X-TC-Timestamp", timestamp.toString())
                .addHeader("X-TC-Version", VERSION)
                .build()
            
            // 发送请求
            val response = suspendCoroutine<Response> { continuation ->
                httpClient.newCall(httpRequest).enqueue(object : Callback {
                    override fun onFailure(call: Call, e: IOException) {
                        continuation.resumeWithException(TencentAsrServiceError.HttpError(e.message ?: "网络请求失败"))
                    }
                    
                    override fun onResponse(call: Call, response: Response) {
                        continuation.resume(response)
                    }
                })
            }
            
            // 处理响应
            val responseBody = response.body?.string()
                ?: throw TencentAsrServiceError.HttpError("响应体为空")
            
            Log.d(TAG, "腾讯云响应状态码: ${response.code}")
            Log.d(TAG, "腾讯云响应数据: $responseBody")
            
            if (!response.isSuccessful) {
                throw TencentAsrServiceError.HttpError("HTTP错误: ${response.code}")
            }
            
            // 解析响应
            try {
                val result = json.decodeFromString<CreateRecTaskResponse>(responseBody)
                Log.i(TAG, "语音识别任务创建成功，任务ID: ${result.data.taskId}")
                result
            } catch (e: Exception) {
                Log.e(TAG, "解析响应失败", e)
                throw TencentAsrServiceError.JsonError(e.message ?: "JSON解析失败")
            }
        }
    }
    
    /**
     * 获取日期字符串 (YYYY-MM-DD)
     */
    private fun getDate(timestamp: Long): String {
        val date = Date(timestamp * 1000)
        val formatter = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        formatter.timeZone = TimeZone.getTimeZone("UTC")
        return formatter.format(date)
    }
    
    /**
     * 计算SHA256哈希
     */
    private fun getHash(message: String): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val hashBytes = digest.digest(message.toByteArray(Charsets.UTF_8))
        return hashBytes.joinToString("") { "%02x".format(it) }
    }
    
    /**
     * 计算HMAC-SHA256
     */
    private fun sha256Hmac(message: String, secret: ByteArray): ByteArray {
        val mac = Mac.getInstance("HmacSHA256")
        val secretKeySpec = SecretKeySpec(secret, "HmacSHA256")
        mac.init(secretKeySpec)
        return mac.doFinal(message.toByteArray(Charsets.UTF_8))
    }
    
    /**
     * 计算授权头
     */
    private fun calculateAuthorization(
        host: String,
        service: String,
        region: String,
        action: String,
        version: String,
        timestamp: Long,
        date: String,
        payload: String
    ): String {
        // 步骤1：拼接规范请求串
        val hashedRequestPayload = getHash(payload)
        val canonicalHeaders = "content-type:$CONTENT_TYPE\nhost:$host\n"
        
        val canonicalRequest = listOf(
            HTTP_METHOD,
            CANONICAL_URI,
            CANONICAL_QUERY_STRING,
            canonicalHeaders,
            SIGNED_HEADERS,
            hashedRequestPayload
        ).joinToString("\n")
        
        // 步骤2：拼接待签名字符串
        val hashedCanonicalRequest = getHash(canonicalRequest)
        val credentialScope = "$date/$service/tc3_request"
        val stringToSign = listOf(
            ALGORITHM,
            timestamp.toString(),
            credentialScope,
            hashedCanonicalRequest
        ).joinToString("\n")
        
        // 步骤3：计算签名
        val secretKey = "TC3${config.secretKey}"
        val kDate = sha256Hmac(date, secretKey.toByteArray(Charsets.UTF_8))
        val kService = sha256Hmac(service, kDate)
        val kSigning = sha256Hmac("tc3_request", kService)
        val signatureBytes = sha256Hmac(stringToSign, kSigning)
        val signature = signatureBytes.joinToString("") { "%02x".format(it) }
        
        // 步骤4：拼接Authorization
        return "$ALGORITHM Credential=${config.secretId}/$credentialScope, SignedHeaders=$SIGNED_HEADERS, Signature=$signature"
    }
}
