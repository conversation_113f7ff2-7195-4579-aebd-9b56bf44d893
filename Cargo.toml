[package]
name = "aishoucang_rust"
version = "0.1.0"
edition = "2024"

[dependencies]
actix-web = "4.4.1"
actix-rt = "2.9.0"
actix-multipart = "0.6.1"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
env_logger = "0.10.1"
log = "0.4.20"
num_cpus = "1.16.0"
listenfd = "1.0.2"
redis = "0.29.5"

futures = "0.3.31"
tokio = { version = "1.44.2", features = ["macros", "rt-multi-thread"] }
thiserror = "2.0.12"
regex = "1.10.3"
once_cell = "1.19.0"
futures-util = "0.3.31"
uuid = { version = "1.16.0", features = ["v4"] }
chrono = { version = "0.4.40", features = ["serde"] }

reqwest = { version = "0.11", default-features = false, features = ["json", "rustls-tls", "stream"] }
hmac = "0.12.1"
sha1 = "0.10.6"
sha2 = "0.10.8"
hex = "0.4.3"
base64 = "0.21.7"
url = "2.5.0"
urlencoding = "2.1.3"
quick-xml = { version = "0.37.5", features = ["serialize"] }
sqlx = { version = "0.7.4", features = ["runtime-tokio", "tls-rustls", "mysql", "chrono", "uuid", "json"] }

[dev-dependencies]
tokio-test = "0.4"
