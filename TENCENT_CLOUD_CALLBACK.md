# 腾讯云语音识别回调接口

## 概述

本项目新增了腾讯云语音识别回调接口，用于接收腾讯云语音识别服务的异步回调通知。

## 接口信息

- **路径**: `/tencent_cloud/asr/callback`
- **方法**: `POST`
- **Content-Type**: `application/x-www-form-urlencoded`

## 功能说明

当用户在创建语音识别任务时设置了 `CallbackUrl` 参数，腾讯云会在识别任务完成后通过此接口发送回调通知。

## 回调参数

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | int64 | 是 | 任务状态码，0为成功，其他值表示失败 |
| message | string | 否 | 失败原因文字描述，成功时为空 |
| requestId | uint64 | 是 | 任务唯一标识，与创建任务时返回的 TaskId 一致 |
| appid | uint64 | 是 | 腾讯云应用 ID |
| projectid | int64 | 是 | 腾讯云项目 ID |
| audioUrl | string | 否 | 语音文件URL（仅当创建任务时使用URL方式） |
| text | string | 否 | 识别出的结果文本 |
| resultDetail | string | 否 | 详细识别结果（仅当ResTextFormat不为0时） |
| audioTime | double | 否 | 语音总时长（秒） |

## 回调示例

### 成功回调
```
POST /tencent_cloud/asr/callback
Content-Type: application/x-www-form-urlencoded

code=0&requestId=4000048858&appid=1251600000&projectid=0&text=%E6%88%91%E5%9C%A8%E9%A9%AC%E4%B8%8A%E4%B8%8A%E5%A4%9C%E7%8F%AD&audioTime=8.420000&message=&resultDetail=
```

### 失败回调
```
POST /tencent_cloud/asr/callback
Content-Type: application/x-www-form-urlencoded

code=4001&requestId=4000048859&appid=1251600000&projectid=0&message=音频格式不支持&audioTime=0
```

## 响应格式

接口返回JSON格式响应：

### 成功响应
```json
{
    "code": 0,
    "message": "回调处理成功"
}
```

### 失败响应
```json
{
    "code": 403,
    "message": "解析回调数据失败: 缺少requestId字段"
}
```

## 实现细节

1. **参数解析**: 接口会自动解析form-urlencoded格式的请求体
2. **数据验证**: 验证必需字段的存在和格式
3. **业务处理**: 根据回调状态执行相应的业务逻辑
4. **日志记录**: 详细记录回调处理过程和结果

## 业务逻辑扩展

当前实现提供了基础的回调处理框架，可以在 `process_callback_data` 函数中添加具体的业务逻辑：

- 将识别结果保存到数据库
- 通知相关用户或系统
- 触发后续处理流程
- 错误处理和重试机制

## 测试

项目包含了测试脚本 `test_callback.sh`，可以用于测试回调接口：

```bash
chmod +x test_callback.sh
./test_callback.sh
```

## 安全考虑

1. 建议在生产环境中添加签名验证
2. 可以添加IP白名单限制
3. 考虑添加请求频率限制
4. 敏感信息应当脱敏处理

## 监控和日志

接口会记录详细的处理日志，包括：
- 接收到的回调数据
- 解析结果
- 业务处理状态
- 错误信息

可以通过日志系统监控回调处理情况。
