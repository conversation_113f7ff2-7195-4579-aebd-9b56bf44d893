#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

# 服务器地址
SERVER_URL = "http://127.0.0.1:8080"

def test_task_list_basic():
    """测试基本任务列表获取"""
    print("\n🎯 测试: 基本任务列表获取")
    
    params = {
        "page": 1,
        "page_size": 10
    }
    
    try:
        response = requests.get(f"{SERVER_URL}/task/list", params=params)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 基本列表测试失败: {e}")
        return False

def test_task_list_with_filters():
    """测试带过滤条件的任务列表获取"""
    print("\n🎯 测试: 带过滤条件的任务列表获取")
    
    params = {
        "page": 1,
        "page_size": 5,
        "task_type": 1,  # 提取文案
        "status": 1      # 新创建
    }
    
    try:
        response = requests.get(f"{SERVER_URL}/task/list", params=params)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 过滤条件测试失败: {e}")
        return False

def test_task_list_with_platform_filter():
    """测试带平台过滤的任务列表获取"""
    print("\n🎯 测试: 带平台过滤的任务列表获取")
    
    params = {
        "page": 1,
        "page_size": 10,
        "platform": "bilibili"
    }
    
    try:
        response = requests.get(f"{SERVER_URL}/task/list", params=params)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 平台过滤测试失败: {e}")
        return False

def test_task_list_pagination():
    """测试分页功能"""
    print("\n🎯 测试: 分页功能")
    
    # 测试第2页
    params = {
        "page": 2,
        "page_size": 3
    }
    
    try:
        response = requests.get(f"{SERVER_URL}/task/list", params=params)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 分页测试失败: {e}")
        return False

def test_task_list_invalid_params():
    """测试无效参数"""
    print("\n🎯 测试: 无效参数")
    
    # 测试无效页码
    params = {
        "page": 0,
        "page_size": 10
    }
    
    try:
        response = requests.get(f"{SERVER_URL}/task/list", params=params)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        # 应该返回400错误
        if response.status_code == 400:
            print("✅ 正确处理了无效页码")
            return True
        else:
            print("❌ 未正确处理无效页码")
            return False
    except Exception as e:
        print(f"❌ 无效参数测试失败: {e}")
        return False

def test_task_list_invalid_task_type():
    """测试无效任务类型"""
    print("\n🎯 测试: 无效任务类型")
    
    params = {
        "page": 1,
        "page_size": 10,
        "task_type": 99  # 无效的任务类型
    }
    
    try:
        response = requests.get(f"{SERVER_URL}/task/list", params=params)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        # 应该返回400错误
        if response.status_code == 400:
            print("✅ 正确处理了无效任务类型")
            return True
        else:
            print("❌ 未正确处理无效任务类型")
            return False
    except Exception as e:
        print(f"❌ 无效任务类型测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试任务列表接口")
    
    tests = [
        test_task_list_basic,
        test_task_list_with_filters,
        test_task_list_with_platform_filter,
        test_task_list_pagination,
        test_task_list_invalid_params,
        test_task_list_invalid_task_type
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过！任务列表接口工作正常")
    else:
        print("❌ 部分测试失败！")
