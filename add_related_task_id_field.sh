#!/bin/bash

# 为腾讯云语音识别任务表添加 related_task_id 字段的脚本

# 数据库配置
DB_HOST="rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"
DB_PORT="3306"
DB_USER="aishoucang_mysql"
DB_PASSWORD="Qtt\$123456"
DB_NAME="aishoucang_develop"

echo "🚀 开始为腾讯云语音识别任务表添加 related_task_id 字段..."

# 执行SQL命令
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" << 'EOF'

-- 检查字段是否已存在
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'tencent_asr_tasks'
    AND COLUMN_NAME = 'related_task_id'
);

-- 如果字段不存在则添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE tencent_asr_tasks ADD COLUMN related_task_id BIGINT UNSIGNED NULL COMMENT ''关联的任务表ID（外键，可选）'' AFTER request_id',
    'SELECT ''Column related_task_id already exists'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'tencent_asr_tasks'
    AND INDEX_NAME = 'idx_related_task_id'
);

SET @sql = IF(@index_exists = 0,
    'ALTER TABLE tencent_asr_tasks ADD KEY idx_related_task_id (related_task_id)',
    'SELECT ''Index idx_related_task_id already exists'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加外键约束
SET @fk_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'tencent_asr_tasks'
    AND COLUMN_NAME = 'related_task_id'
    AND REFERENCED_TABLE_NAME = 'tasks'
);

SET @tasks_table_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'tasks'
);

SET @sql = IF(@fk_exists = 0 AND @tasks_table_exists > 0,
    'ALTER TABLE tencent_asr_tasks ADD FOREIGN KEY (related_task_id) REFERENCES tasks(id) ON DELETE SET NULL',
    'SELECT ''Foreign key constraint already exists or tasks table not found'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示表结构确认
DESCRIBE tencent_asr_tasks;

EOF

if [ $? -eq 0 ]; then
    echo "✅ related_task_id 字段添加成功！"
else
    echo "❌ 字段添加失败，请检查数据库连接和权限"
    exit 1
fi

echo "🎉 数据库更新完成！"
