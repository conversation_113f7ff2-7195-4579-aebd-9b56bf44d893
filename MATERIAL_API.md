# 素材管理 API 文档

## 概述

素材管理系统提供了添加、删除和查询素材的功能。素材包括图片、语音和视频三种类型。删除操作为软删除，不会真正删除数据库中的记录。

## 数据库表结构

```sql
CREATE TABLE materials (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    user_id BIGINT UNSIGNED NOT NULL,
    url TEXT NOT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status TINYINT NOT NULL DEFAULT 1 COMMENT '1正常 2已删除',
    type TINYINT NOT NULL COMMENT '1图片 2语音 3视频',
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_user_status (user_id, status),
    KEY idx_user_type (user_id, type),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
```

## API 接口

### 1. 添加素材

**接口地址：** `POST /material/add`

**请求头：**
```
Authorization: Bearer your_token_here
Content-Type: application/json
```

**请求参数：**
```json
{
    "url": "https://assets-xunhe.oss-cn-qingdao.aliyuncs.com/example.jpg",
    "type": 1
}
```

**参数说明：**
- `url`: 素材的OSS地址，必填
- `type`: 素材类型，必填
  - `1`: 图片
  - `2`: 语音
  - `3`: 视频

**成功响应：**
```json
{
    "code": 0,
    "message": "添加素材成功",
    "data": {
        "material": {
            "id": "123",
            "user_id": "456",
            "url": "https://assets-xunhe.oss-cn-qingdao.aliyuncs.com/example.jpg",
            "create_time": 1714392123000,
            "update_time": 1714392123000,
            "status": 1,
            "type": 1
        }
    }
}
```

**错误响应：**
```json
{
    "code": 403,
    "message": "无效的素材类型，支持的类型：1图片 2语音 3视频",
    "data": null
}
```

### 2. 删除素材

**接口地址：** `POST /material/delete`

**请求头：**
```
Authorization: Bearer your_token_here
Content-Type: application/json
```

**请求参数：**
```json
{
    "id": "123"
}
```

**参数说明：**
- `id`: 素材ID，必填

**成功响应：**
```json
{
    "code": 0,
    "message": "删除素材成功",
    "data": {
        "id": "123"
    }
}
```

**错误响应：**
```json
{
    "code": 101,
    "message": "素材不存在或无权限删除",
    "data": null
}
```

### 3. 获取素材列表

**接口地址：** `GET /material/list`

**请求头：**
```
Authorization: Bearer your_token_here
```

**请求参数：**
- `page`: 页码，从1开始，默认为1
- `page_size`: 每页数量，默认为20，最大为100
- `type`: 素材类型过滤（可选）
  - `1`: 图片
  - `2`: 语音
  - `3`: 视频

**请求示例：**
```
GET /material/list?page=1&page_size=20&type=1
```

**成功响应：**
```json
{
    "code": 0,
    "message": "获取素材列表成功",
    "data": {
        "materials": [
            {
                "id": "123",
                "user_id": "456",
                "url": "https://assets-xunhe.oss-cn-qingdao.aliyuncs.com/example1.jpg",
                "create_time": 1714392123000,
                "update_time": 1714392123000,
                "status": 1,
                "type": 1
            },
            {
                "id": "124",
                "user_id": "456",
                "url": "https://assets-xunhe.oss-cn-qingdao.aliyuncs.com/example2.mp3",
                "create_time": 1714392124000,
                "update_time": 1714392124000,
                "status": 1,
                "type": 2
            }
        ],
        "total": 25,
        "page": 1,
        "page_size": 20
    }
}
```

## 错误码说明

- `0`: 成功
- `101`: 系统错误
- `203`: 未登录或身份验证失败
- `403`: 无效参数
- `503`: 服务不可用

## 注意事项

1. 所有接口都需要用户登录，需要在请求头中携带有效的 Authorization token
2. 删除操作为软删除，只是将状态设置为已删除（status=2），不会真正删除数据
3. 用户只能操作自己的素材，无法访问其他用户的素材
4. 素材类型必须是1、2、3中的一个，分别对应图片、语音、视频
5. URL应该是有效的OSS地址，不能为空
6. 分页查询中，页码从1开始，每页最大数量为100
