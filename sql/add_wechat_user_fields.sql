-- 为users表添加微信用户信息相关字段
ALTER TABLE users
ADD COLUMN openid VARCHAR(64) DEFAULT NULL COMMENT '微信OpenID',
ADD COLUMN unionid VARCHAR(64) DEFAULT NULL COMMENT '微信统一ID',
ADD COLUMN nickname VARCHAR(64) DEFAULT NULL COMMENT '用户昵称',
ADD COLUMN sex TINYINT DEFAULT NULL COMMENT '用户性别：1为男性，2为女性，0为未知',
ADD COLUMN province VARCHAR(32) DEFAULT NULL COMMENT '用户所在省份',
ADD COLUMN city VARCHAR(32) DEFAULT NULL COMMENT '用户所在城市',
ADD COLUMN country VARCHAR(32) DEFAULT NULL COMMENT '用户所在国家',
ADD COLUMN headimgurl VARCHAR(255) DEFAULT NULL COMMENT '用户头像URL',
ADD COLUMN privilege JSON DEFAULT NULL COMMENT '用户特权信息，JSON格式',
ADD INDEX idx_openid (openid),
ADD INDEX idx_unionid (unionid);

-- 更新表注释
ALTER TABLE users COMMENT '用户表，包含基本用户信息和微信登录信息';
