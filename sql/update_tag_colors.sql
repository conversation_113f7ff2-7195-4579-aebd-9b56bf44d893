-- 更新标签表的颜色字段结构
-- 将单一的color字段拆分为background_color和text_color两个字段

-- 检查是否已经有新的字段结构
SET @background_color_exists = (
    SELECT COUNT(*)
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = 'tags'
    AND column_name = 'background_color'
);

SET @text_color_exists = (
    SELECT COUNT(*)
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = 'tags'
    AND column_name = 'text_color'
);

-- 如果新字段不存在，则进行迁移
SET @sql = CASE 
    WHEN @background_color_exists = 0 AND @text_color_exists = 0 THEN
        CONCAT(
            'ALTER TABLE tags ',
            'ADD COLUMN background_color VARCHAR(7) NOT NULL DEFAULT "#1890ff" AFTER name, ',
            'ADD COLUMN text_color VARCHAR(7) NOT NULL DEFAULT "#ffffff" AFTER background_color'
        )
    ELSE 
        'SELECT "标签颜色字段已存在，跳过添加" as message'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 如果是从旧的color字段迁移，复制数据
SET @old_color_exists = (
    SELECT COUNT(*)
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = 'tags'
    AND column_name = 'color'
);

-- 如果旧的color字段存在，将其值复制到background_color字段
SET @copy_sql = CASE 
    WHEN @old_color_exists > 0 AND @background_color_exists = 0 THEN
        'UPDATE tags SET background_color = color WHERE color IS NOT NULL'
    ELSE 
        'SELECT "无需复制旧颜色数据" as message'
END;

PREPARE copy_stmt FROM @copy_sql;
EXECUTE copy_stmt;
DEALLOCATE PREPARE copy_stmt;

-- 删除旧的color字段（如果存在）
SET @drop_sql = CASE 
    WHEN @old_color_exists > 0 AND @background_color_exists > 0 THEN
        'ALTER TABLE tags DROP COLUMN color'
    ELSE 
        'SELECT "无需删除旧color字段" as message'
END;

PREPARE drop_stmt FROM @drop_sql;
EXECUTE drop_stmt;
DEALLOCATE PREPARE drop_stmt;

-- 为现有数据设置合适的文字颜色
-- 根据背景颜色的亮度来决定文字颜色
UPDATE tags 
SET text_color = CASE 
    -- 浅色背景使用深色文字
    WHEN background_color IN ('#ffffff', '#f0f0f0', '#e6f7ff', '#fff2e8', '#f6ffed', '#fff1f0', '#f9f0ff') THEN '#000000'
    -- 中等亮度背景使用白色文字
    WHEN background_color IN ('#1890ff', '#52c41a', '#fa8c16', '#f5222d', '#722ed1', '#13c2c2', '#eb2f96') THEN '#ffffff'
    -- 默认使用白色文字
    ELSE '#ffffff'
END
WHERE text_color = '#ffffff' OR text_color IS NULL;

-- 更新表注释
ALTER TABLE tags COMMENT '标签表，包含标签名称、背景颜色和文字颜色';
