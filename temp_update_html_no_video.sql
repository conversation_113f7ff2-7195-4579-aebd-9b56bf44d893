-- 更新notes表中ID为2的记录的html字段，去除视频标签
UPDATE notes SET html = '<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>温柔笔记</title>
  <style>
    html {
      font-size: 16px;
    }
    @media screen and (max-width: 414px) {
      html {
        font-size: 13px;
      }
    }
    @media screen and (min-width: 768px) {
      html {
        font-size: 18px;
      }
    }

    body {
      margin: 0;
      font-family: ''Helvetica Neue'', sans-serif;
      background: #fff0f5;
      color: #333;
      padding: 1rem;
    }

    header {
      text-align: center;
      margin-bottom: 2rem;
    }

    header h1 {
      font-size: 2.2rem;
      color: #db7093;
      margin: 0;
      font-weight: bold;
      text-shadow: 1px 1px 2px rgba(255, 182, 193, 0.6);
    }

    .note-section {
      background-color: #fff;
      border-radius: 1rem;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 0.1rem 0.4rem rgba(0, 0, 0, 0.05);
    }

    .note-section h2 {
      color: #db7093;
      font-size: 1.4rem;
      margin-bottom: 1rem;
    }

    .note-section p {
      line-height: 1.6;
      color: #555;
    }

    .tag {
      display: inline-block;
      background-color: #ffe4e1;
      color: #d87093;
      padding: 0.3rem 0.6rem;
      border-radius: 999px;
      font-size: 0.8rem;
      margin-right: 0.5rem;
      margin-bottom: 0.5rem;
    }

    .timeline {
      position: relative;
      margin-left: 1rem;
      padding-left: 1.5rem;
      border-left: 3px solid #f4a7b9;
    }

    .timeline-item {
      margin-bottom: 1.5rem;
    }

    .timeline-item::before {
      content: '''';
      position: absolute;
      left: -0.6rem;
      width: 1rem;
      height: 1rem;
      background-color: #f4a7b9;
      border-radius: 50%;
      border: 2px solid white;
      box-shadow: 0 0 0 2px #f4a7b9;
    }

    .timeline-year {
      font-weight: bold;
      color: #c71585;
    }

    footer {
      text-align: center;
      font-size: 0.9rem;
      color: #aaa;
      margin-top: 2rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>🌸 我的温柔笔记 🌸</h1>
  </header>

  <section class="note-section">
    <h2>📌 提示词写作的黄金法则</h2>
    <p>写出优秀提示词的关键，在于清晰的结构、明确的意图以及合理的限制条件。你可以按照"角色 + 任务 + 输出格式"的方式组织你的提示内容。</p>
    <div>
      <span class="tag">角色</span>
      <span class="tag">任务</span>
      <span class="tag">格式</span>
      <span class="tag">风格</span>
    </div>
  </section>

  <section class="note-section">
    <h2>💡 小贴士</h2>
    <p>不要吝啬细节！用具体、明确、贴切的语言引导模型的行为。比如，"用一种温柔治愈的语气写一封鼓励信"，比"写鼓励信"更有效。</p>
  </section>

  <section class="note-section">
    <h2>🌷 常见错误提醒</h2>
    <p>❌ 不要让提示词过于模糊，比如"讲个故事"会让模型难以理解你的预期。<br>
       ✅ 改为"写一个关于勇气与成长的童话故事，适合5岁儿童阅读，控制在300字内"。</p>
  </section>

  <section class="note-section">
    <h2>🎀 我的提示词模板</h2>
    <p>✨ 角色：你是一名专业的情绪疗愈师。<br>
       ✨ 任务：请用温柔、体贴的语气，安慰一个今天被老板批评的人。<br>
       ✨ 格式：分段表达，每段不超过3句话。</p>
  </section>

  <section class="note-section">
    <h2>🕰️ AI 发展时间线</h2>
    <div class="timeline">
      <div class="timeline-item">
        <p><span class="timeline-year">1956</span> - 达特茅斯会议提出"人工智能"概念，AI研究正式诞生。</p>
      </div>
      <div class="timeline-item">
        <p><span class="timeline-year">1997</span> - IBM 的超级计算机 Deep Blue 战胜国际象棋世界冠军卡斯帕罗夫。</p>
      </div>
      <div class="timeline-item">
        <p><span class="timeline-year">2012</span> - AlexNet 在ImageNet比赛中大胜，推动深度学习革命。</p>
      </div>
      <div class="timeline-item">
        <p><span class="timeline-year">2016</span> - AlphaGo 击败围棋冠军李世石，AI走入大众视野。</p>
      </div>
      <div class="timeline-item">
        <p><span class="timeline-year">2020</span> - OpenAI 发布 GPT-3，开启通用语言模型时代。</p>
      </div>
      <div class="timeline-item">
        <p><span class="timeline-year">2023</span> - 多模态大模型兴起，ChatGPT、Claude等工具走入日常生活。</p>
      </div>
    </div>
  </section>

  <footer>
    ✨ 愿你用最温柔的方式探索世界 ✨
  </footer>
</body>
</html>' WHERE id = 2;

-- 验证更新结果
SELECT id, title, LEFT(html, 100) as html_preview FROM notes WHERE id = 2;
