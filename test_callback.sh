#!/bin/bash

# 测试腾讯云语音识别回调接口
# 模拟腾讯云发送的回调请求

echo "测试腾讯云语音识别回调接口..."

# 测试成功回调
echo "1. 测试成功回调"
curl -X POST http://localhost:8080/tencent_cloud/asr/callback \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "code=0&requestId=4000048858&appid=1251600000&projectid=0&text=%5B0%3A1.640%2C0%3A4.600%5D++%E6%88%91%E5%9C%A8%E9%A9%AC%E4%B8%8A%E4%B8%8A%E5%A4%9C%E7%8F%AD%E3%80%82%0A%5B0%3A5.420%2C0%3A7.820%5D++%E6%98%8E%E5%A4%A9%E6%97%A9%E4%B8%8A%E8%AF%B4%E5%93%88%E3%80%82%0A&audioTime=8.420000&message=&resultDetail="

echo -e "\n\n2. 测试失败回调"
curl -X POST http://localhost:8080/tencent_cloud/asr/callback \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "code=4001&requestId=4000048859&appid=1251600000&projectid=0&message=音频格式不支持&audioTime=0"

echo -e "\n\n3. 测试参数错误"
curl -X POST http://localhost:8080/tencent_cloud/asr/callback \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "code=0&appid=1251600000"

echo -e "\n\n测试完成！"
