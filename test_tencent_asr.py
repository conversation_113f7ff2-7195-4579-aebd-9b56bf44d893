#!/usr/bin/env python3
"""
腾讯云语音识别API测试脚本
"""

import requests
import json
import sys

# 服务器配置
SERVER_URL = "http://localhost:8080"

def test_config():
    """测试腾讯云配置"""
    print("📋 测试1: 腾讯云配置测试")
    try:
        response = requests.get(f"{SERVER_URL}/test/tencent_cloud_config")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_service():
    """测试腾讯云语音识别服务"""
    print("\n🔧 测试2: 腾讯云语音识别服务测试")
    try:
        response = requests.get(f"{SERVER_URL}/test/tencent_cloud_asr")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 服务测试失败: {e}")
        return False

def test_create_task_full():
    """测试创建语音识别任务（完整参数）"""
    print("\n🎯 测试3: 创建语音识别任务（完整参数）")
    
    payload = {
        "version": "2019-06-14",
        "source_type": 0,
        "need_times": "1",
        "engine_model_type": "16k_zh_video",
        "channel_num": 1,
        "skey": "p8HLH5rmtphUTLsmc2kvAkHsTc1GltWkKcSjESeYsbs_",
        "customization_id": "",
        "hotword_id": "",
        "urls": [
            "https://v5-ali-colda.douyinvod.com/5f82c4d37a0858aafdac4bf1a515dc9c/685269f1/video/tos/cn/tos-cn-ve-15/ooZcaBaEQIAh8i2QlimTNWTI9PBFvAFCGwP6E/?a=1128&ch=0&cr=0&dr=0&lr=aweme_search_suffix&cd=0%7C0%7C1%7C0&cv=1&br=1157&bt=1157&cs=0&ds=3&ft=3pbegxTqRR0s~hC32D12Nc0iPMgzbLZt4idU_4C~nKV9Nv7TGW&mime_type=video_mp4&qs=0&rc=ZTQ1OTtoaTw0NTg3NmU0O0BpM21vNnc5cjRwNDMzNGkzM0BhXzAvYTExNV8xMzQ1MWMvYSNvM3AuMmRrYS1hLS1kLTBzcw%3D%3D&btag=80010e00098000&cquery=100y&dy_q=1750227902&feature_id=dc6e471fd69cf67451806384e04f2b47&l=20250618142502DCF87D1746077458E56B"
        ],
        "speaker_number": 0,
        "callback_url": None
    }
    
    try:
        response = requests.post(
            f"{SERVER_URL}/tencent_cloud/asr/create_task",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 完整参数测试失败: {e}")
        return False

def test_create_task_minimal():
    """测试创建语音识别任务（最简参数）"""
    print("\n⚡ 测试4: 创建语音识别任务（最简参数）")
    
    payload = {
        "urls": [
            "https://v5-ali-colda.douyinvod.com/5f82c4d37a0858aafdac4bf1a515dc9c/685269f1/video/tos/cn/tos-cn-ve-15/ooZcaBaEQIAh8i2QlimTNWTI9PBFvAFCGwP6E/?a=1128&ch=0&cr=0&dr=0&lr=aweme_search_suffix&cd=0%7C0%7C1%7C0&cv=1&br=1157&bt=1157&cs=0&ds=3&ft=3pbegxTqRR0s~hC32D12Nc0iPMgzbLZt4idU_4C~nKV9Nv7TGW&mime_type=video_mp4&qs=0&rc=ZTQ1OTtoaTw0NTg3NmU0O0BpM21vNnc5cjRwNDMzNGkzM0BhXzAvYTExNV8xMzQ1MWMvYSNvM3AuMmRrYS1hLS1kLTBzcw%3D%3D&btag=80010e00098000&cquery=100y&dy_q=1750227902&feature_id=dc6e471fd69cf67451806384e04f2b47&l=20250618142502DCF87D1746077458E56B"
        ]
    }
    
    try:
        response = requests.post(
            f"{SERVER_URL}/tencent_cloud/asr/create_task",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 最简参数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试腾讯云语音识别API...")
    print(f"服务器地址: {SERVER_URL}")
    
    # 检查服务器是否运行
    try:
        response = requests.get(f"{SERVER_URL}/test/ping", timeout=5)
        if response.status_code != 200:
            print(f"❌ 服务器未运行或不可访问: {SERVER_URL}")
            print("请确保服务器已启动并在正确端口运行")
            sys.exit(1)
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请确保服务器已启动并在正确端口运行")
        sys.exit(1)
    
    print("✅ 服务器连接正常")
    
    # 运行测试
    results = []
    results.append(test_config())
    results.append(test_service())
    results.append(test_create_task_full())
    results.append(test_create_task_minimal())
    
    # 总结
    print("\n" + "="*50)
    print("📊 测试结果总结:")
    test_names = ["配置测试", "服务测试", "完整参数测试", "最简参数测试"]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    passed = sum(results)
    total = len(results)
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
    else:
        print("⚠️  部分测试失败，请检查日志")
    
    print("\n📝 使用说明:")
    print("1. 确保服务器在 http://localhost:8080 运行")
    print("2. 运行此脚本: python3 test_tencent_asr.py")
    print("\n🔗 API端点:")
    print("- 配置测试: GET /test/tencent_cloud_config")
    print("- 服务测试: GET /test/tencent_cloud_asr")
    print("- 创建任务: POST /tencent_cloud/asr/create_task")

if __name__ == "__main__":
    main()
