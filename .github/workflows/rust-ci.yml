name: Rust CI/CD

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/master'

    steps:
      - name: ✅ Checkout
        uses: actions/checkout@v3

      - name: 🔧 安装系统依赖
        run: |
          sudo apt-get update
          sudo apt-get install -y musl-tools pkg-config sshpass

      - name: 🦀 安装 Rust + musl
        run: |
          curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
          source $HOME/.cargo/env
          rustup default stable
          rustup target add x86_64-unknown-linux-musl

      - name: 🔨 构建项目（静态链接）
        run: cargo build --release --target x86_64-unknown-linux-musl
        env:
          APP_ENV: production
          DATABASE_URL: "mysql://aishoucang_mysql:Qtt$<EMAIL>:3306/aishoucang"

      - name: 🚀 部署到服务器
        run: |
          mkdir -p deploy
          cp target/x86_64-unknown-linux-musl/release/aishoucang_rust deploy/

          # 生成 systemd 服务文件（保留）
          cat > deploy/aishoucang.service << EOF
          [Unit]
          Description=AI收藏 Rust服务
          After=network.target

          [Service]
          Type=simple
          User=root
          WorkingDirectory=/root/aishoucang_rust
          Environment="APP_ENV=production"
          ExecStart=/root/aishoucang_rust/aishoucang_rust
          Restart=on-failure
          RestartSec=5

          [Install]
          WantedBy=multi-user.target
          EOF

          # 设置 SSH 密码
          export SSHPASS='Qtt$123456'

          # 先停止服务，避免文件拷贝失败
          echo "🛑 正在停止远程服务..."
          sshpass -e ssh -o StrictHostKeyChecking=no root@************* << 'ENDSSH'
            # 确保服务完全停止
            systemctl stop aishoucang.service
            sleep 2

            # 检查是否有残留进程并强制终止
            if pgrep -f "aishoucang_rust"; then
              echo "发现残留进程，正在强制终止..."
              pkill -9 -f "aishoucang_rust"
              sleep 1
            fi

            echo "✅ 服务已停止，准备上传新文件"
          ENDSSH

          # 上传程序与 systemd 服务文件
          sshpass -e ssh -o StrictHostKeyChecking=no root@************* "mkdir -p /root/aishoucang_rust"
          sshpass -e scp -o StrictHostKeyChecking=no deploy/aishoucang_rust root@*************:/root/aishoucang_rust/
          sshpass -e scp -o StrictHostKeyChecking=no deploy/aishoucang.service root@*************:/etc/systemd/system/

          # 启动服务（不再动 Nginx）
          sshpass -e ssh -o StrictHostKeyChecking=no root@************* << 'ENDSSH'
            echo "🔄 正在重启服务..."

            # 确保服务完全停止
            systemctl stop aishoucang.service
            sleep 2

            # 检查是否有残留进程并强制终止
            if pgrep -f "aishoucang_rust"; then
              echo "发现残留进程，正在强制终止..."
              pkill -9 -f "aishoucang_rust"
              sleep 1
            fi

            # 重新加载服务配置并启动
            systemctl daemon-reload
            systemctl enable aishoucang.service
            systemctl start aishoucang.service

            echo "✅ 服务已重启"
            echo "📋 Rust 服务状态:"
            systemctl status aishoucang.service --no-pager | head -n 20

            # 等待服务完全启动
            echo "⏳ 等待服务完全启动..."
            sleep 8

            # 检查服务是否正常运行（使用端口8080）
            if curl -s http://localhost:8080/test/ping | grep -q "pong"; then
              echo "✅ 服务已成功启动并响应 ping 请求"
              echo "🔍 检查服务日志（最近10条）:"
              journalctl -u aishoucang.service -n 10 --no-pager
            else
              echo "⚠️ 警告：服务可能未正常启动"
              echo "🔍 检查错误日志:"
              journalctl -u aishoucang.service -n 50 --no-pager

              # 检查端口是否被占用
              echo "🔍 检查端口8080状态:"
              netstat -tulpn | grep 8080 || echo "端口8080未被占用"

              # 尝试再次启动
              echo "🔄 尝试再次启动服务..."
              systemctl restart aishoucang.service
              sleep 5

              # 再次检查
              if curl -s http://localhost:8080/test/ping | grep -q "pong"; then
                echo "✅ 第二次尝试成功：服务已启动"
              else
                echo "❌ 服务启动失败，请手动检查问题"
                exit 1
              fi
            fi
          ENDSSH

          echo "✅ 部署完成"
