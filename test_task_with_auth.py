#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

# 服务器地址
SERVER_URL = "http://127.0.0.1:8080"

def test_task_without_token():
    """测试未登录访问任务接口"""
    print("\n🎯 测试: 未登录访问任务接口")
    
    # 测试创建任务
    create_payload = {
        "task_type": 1,
        "title": "测试任务",
        "platform": "bilibili",
        "url": "https://www.bilibili.com/video/BV1234567890"
    }
    
    try:
        response = requests.post(f"{SERVER_URL}/task/create", json=create_payload)
        print(f"创建任务状态码: {response.status_code}")
        print(f"创建任务响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        # 应该返回401未授权
        create_success = response.status_code == 401
    except Exception as e:
        print(f"❌ 创建任务测试失败: {e}")
        create_success = False
    
    # 测试获取任务列表
    try:
        response = requests.get(f"{SERVER_URL}/task/list", params={"page": 1, "page_size": 10})
        print(f"获取列表状态码: {response.status_code}")
        print(f"获取列表响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        # 应该返回401未授权
        list_success = response.status_code == 401
    except Exception as e:
        print(f"❌ 获取列表测试失败: {e}")
        list_success = False
    
    return create_success and list_success

def test_task_with_invalid_token():
    """测试使用无效token访问任务接口"""
    print("\n🎯 测试: 使用无效token访问任务接口")
    
    headers = {
        "Authorization": "Bearer invalid_token_12345"
    }
    
    # 测试创建任务
    create_payload = {
        "task_type": 1,
        "title": "测试任务",
        "platform": "bilibili",
        "url": "https://www.bilibili.com/video/BV1234567890"
    }
    
    try:
        response = requests.post(f"{SERVER_URL}/task/create", json=create_payload, headers=headers)
        print(f"创建任务状态码: {response.status_code}")
        print(f"创建任务响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        # 应该返回401未授权
        create_success = response.status_code == 401
    except Exception as e:
        print(f"❌ 创建任务测试失败: {e}")
        create_success = False
    
    # 测试获取任务列表
    try:
        response = requests.get(f"{SERVER_URL}/task/list", params={"page": 1, "page_size": 10}, headers=headers)
        print(f"获取列表状态码: {response.status_code}")
        print(f"获取列表响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        # 应该返回401未授权
        list_success = response.status_code == 401
    except Exception as e:
        print(f"❌ 获取列表测试失败: {e}")
        list_success = False
    
    return create_success and list_success

def test_login_and_task_operations():
    """测试登录后的任务操作"""
    print("\n🎯 测试: 登录后的任务操作")
    
    # 先尝试登录（这里需要有效的用户凭据）
    login_payload = {
        "username": "test_user",
        "password": "test_password"
    }
    
    try:
        login_response = requests.post(f"{SERVER_URL}/users/login", json=login_payload)
        print(f"登录状态码: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print("⚠️  登录失败，跳过后续测试")
            return False
        
        login_data = login_response.json()
        token = login_data.get("data", {}).get("token")
        
        if not token:
            print("⚠️  未获取到token，跳过后续测试")
            return False
        
        headers = {
            "Authorization": f"Bearer {token}"
        }
        
        # 测试创建任务
        create_payload = {
            "task_type": 1,
            "title": "用户任务测试",
            "platform": "bilibili",
            "url": "https://www.bilibili.com/video/BV1234567890"
        }
        
        create_response = requests.post(f"{SERVER_URL}/task/create", json=create_payload, headers=headers)
        print(f"创建任务状态码: {create_response.status_code}")
        print(f"创建任务响应: {json.dumps(create_response.json(), indent=2, ensure_ascii=False)}")
        
        # 测试获取任务列表
        list_response = requests.get(f"{SERVER_URL}/task/list", params={"page": 1, "page_size": 10}, headers=headers)
        print(f"获取列表状态码: {list_response.status_code}")
        print(f"获取列表响应: {json.dumps(list_response.json(), indent=2, ensure_ascii=False)}")
        
        return create_response.status_code == 200 and list_response.status_code == 200
        
    except Exception as e:
        print(f"❌ 登录后任务操作测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试任务接口用户身份验证")
    
    tests = [
        ("未登录访问", test_task_without_token),
        ("无效token访问", test_task_with_invalid_token),
        ("登录后操作", test_login_and_task_operations)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print(f"{'='*50}")
        
        if test_func():
            print(f"✅ {test_name} 测试通过")
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed >= 2:  # 前两个测试应该通过（身份验证正常工作）
        print("✅ 用户身份验证功能工作正常！")
    else:
        print("❌ 用户身份验证功能存在问题！")
