#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

# 服务器地址
SERVER_URL = "http://127.0.0.1:8080"

def test_create_asr_task_with_related_task_id():
    """测试创建语音识别任务（带关联任务ID）"""
    print("\n🎯 测试: 创建语音识别任务（带关联任务ID）")
    
    payload = {
        "related_task_id": 1,  # 关联任务表的ID
        "source_type": 0,
        "engine_model_type": "16k_zh_video",
        "channel_num": 1,
        "customization_id": "",
        "hotword_id": "",
        "url": "https://example.com/test.mp3",
        "speaker_number": 0,
        "res_text_format": 3
    }
    
    try:
        response = requests.post(f"{SERVER_URL}/tencent_cloud/asr/create_task", json=payload)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 创建任务失败: {e}")
        return False

def test_create_asr_task_without_related_task_id():
    """测试创建语音识别任务（不带关联任务ID）"""
    print("\n🎯 测试: 创建语音识别任务（不带关联任务ID）")
    
    payload = {
        "source_type": 0,
        "engine_model_type": "16k_zh_video", 
        "channel_num": 1,
        "customization_id": "",
        "hotword_id": "",
        "url": "https://example.com/test2.mp3",
        "speaker_number": 0,
        "res_text_format": 3
    }
    
    try:
        response = requests.post(f"{SERVER_URL}/tencent_cloud/asr/create_task", json=payload)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 创建任务失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试腾讯云语音识别任务创建（带关联任务ID功能）")
    
    # 测试带关联任务ID的情况
    success1 = test_create_asr_task_with_related_task_id()
    
    # 测试不带关联任务ID的情况  
    success2 = test_create_asr_task_without_related_task_id()
    
    if success1 and success2:
        print("\n✅ 所有测试通过！")
    else:
        print("\n❌ 部分测试失败！")
