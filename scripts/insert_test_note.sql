-- 插入测试笔记数据
-- 注意：执行前请确保连接到测试数据库 aishoucang_develop

-- 插入测试笔记数据
INSERT INTO notes (
    parent_id,
    user_id, 
    title,
    cover,
    `desc`,
    content,
    html,
    create_time,
    update_time
) VALUES (
    NULL, -- parent_id: 笔记可以单独存在，不属于任何合集
    1, -- user_id: 假设用户ID为1，请根据实际情况调整
    '温柔笔记 - 提示词写作指南',
    'https://example.com/cover.jpg',
    '这是一份关于提示词写作的温柔指南，包含黄金法则和实用小贴士。',
    '提示词写作的黄金法则：写出优秀提示词的关键，在于清晰的结构、明确的意图以及合理的限制条件。你可以按照"角色 + 任务 + 输出格式"的方式组织你的提示内容。小贴士：不要吝啬细节！用具体、明确、贴切的语言引导模型的行为。比如，"用一种温柔治愈的语气写一封鼓励信"，比"写鼓励信"更有效。',
    '<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>温柔笔记</title>
  <style>
    /* 使用 rem 方案 */
    html {
      font-size: 16px;
    }
    @media screen and (max-width: 414px) {
      html {
        font-size: 13px;
      }
    }
    @media screen and (min-width: 768px) {
      html {
        font-size: 18px;
      }
    }

    body {
      margin: 0;
      font-family: ''Helvetica Neue'', sans-serif;
      background: #fff0f5; /* 淡粉底色 */
      color: #333;
      padding: 1rem;
    }

    header {
      background: linear-gradient(90deg, #ffc0cb, #ffb6c1);
      padding: 1rem 1.5rem;
      border-radius: 1rem;
      text-align: center;
      color: white;
      font-size: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 0.2rem 0.5rem rgba(255, 182, 193, 0.4);
    }

    .note-section {
      background-color: #fff;
      border-radius: 1rem;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 0.1rem 0.4rem rgba(0, 0, 0, 0.05);
    }

    .note-section h2 {
      color: #db7093; /* 浅玫瑰红 */
      font-size: 1.2rem;
      margin-bottom: 1rem;
    }

    .note-section p {
      line-height: 1.6;
      color: #555;
    }

    .tag {
      display: inline-block;
      background-color: #ffe4e1;
      color: #d87093;
      padding: 0.3rem 0.6rem;
      border-radius: 999px;
      font-size: 0.8rem;
      margin-right: 0.5rem;
      margin-bottom: 0.5rem;
    }

    footer {
      text-align: center;
      font-size: 0.9rem;
      color: #aaa;
      margin-top: 2rem;
    }
  </style>
</head>
<body>
  <header>
    🌸 我的温柔笔记 🌸
  </header>

  <section class="note-section">
    <h2>📌 提示词写作的黄金法则</h2>
    <p>写出优秀提示词的关键，在于清晰的结构、明确的意图以及合理的限制条件。你可以按照"角色 + 任务 + 输出格式"的方式组织你的提示内容。</p>
    <div>
      <span class="tag">角色</span>
      <span class="tag">任务</span>
      <span class="tag">格式</span>
      <span class="tag">风格</span>
    </div>
  </section>

  <section class="note-section">
    <h2>💡 小贴士</h2>
    <p>不要吝啬细节！用具体、明确、贴切的语言引导模型的行为。比如，"用一种温柔治愈的语气写一封鼓励信"，比"写鼓励信"更有效。</p>
  </section>

  <footer>
    ✨ 愿你用最温柔的方式探索世界 ✨
  </footer>
</body>
</html>',
    NOW(),
    NOW()
);

-- 查询插入的数据
SELECT id, title, `desc`, create_time FROM notes WHERE title = '温柔笔记 - 提示词写作指南';

-- 显示插入结果
SELECT '测试笔记数据插入完成！' AS result;
