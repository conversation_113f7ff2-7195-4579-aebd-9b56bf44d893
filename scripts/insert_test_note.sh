#!/bin/bash

# 插入测试笔记数据脚本
# 使用方法：./scripts/insert_test_note.sh [环境]
# 环境参数：testing（默认）、pre、production

set -e  # 遇到错误立即退出

# 数据库连接配置
DB_HOST="rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"
DB_PORT="3306"
DB_USER="aishoucang_mysql"
DB_PASSWORD="Qtt\$123456"

# 根据环境参数选择数据库
ENVIRONMENT=${1:-testing}

case $ENVIRONMENT in
    "testing")
        DB_NAME="aishoucang_develop"
        ;;
    "pre")
        DB_NAME="aishoucang_pre"
        ;;
    "production")
        DB_NAME="aishoucang"
        ;;
    *)
        echo "错误：无效的环境参数。请使用 testing、pre 或 production"
        exit 1
        ;;
esac

echo "开始向 $ENVIRONMENT 环境的数据库 $DB_NAME 插入测试笔记数据..."

# 检查mysql命令是否可用
if ! command -v mysql &> /dev/null; then
    echo "错误：未找到mysql命令。请确保已安装MySQL客户端。"
    exit 1
fi

# 执行插入操作
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" < scripts/insert_test_note.sql

if [ $? -eq 0 ]; then
    echo "成功向 $ENVIRONMENT 环境插入测试笔记数据！"
    
    # 获取插入的笔记ID
    NOTE_ID=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -se "
    SELECT id FROM notes WHERE title = '温柔笔记 - 提示词写作指南' ORDER BY id DESC LIMIT 1
    ")
    
    echo "插入的笔记ID: $NOTE_ID"
    echo "您可以通过以下接口测试："
    echo "GET /note/detail?id=$NOTE_ID"
else
    echo "插入测试笔记数据失败！"
    exit 1
fi
