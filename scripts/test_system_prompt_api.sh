#!/bin/bash

# 测试系统提示词API接口
# 使用方法: ./scripts/test_system_prompt_api.sh [server_url]

# 默认配置
SERVER_URL=${1:-"http://localhost:8080"}

echo "🚀 测试系统提示词API接口"
echo "服务器地址: $SERVER_URL"
echo "================================"

# 测试获取系统提示词列表
echo "📝 测试获取系统提示词列表..."
echo ""
echo "📡 发送请求: GET $SERVER_URL/system-prompt/list"

LIST_RESPONSE=$(curl -s -w "\n%{http_code}" \
    -X GET \
    -H "Content-Type: application/json" \
    "$SERVER_URL/system-prompt/list")

LIST_HTTP_CODE=$(echo "$LIST_RESPONSE" | tail -n1)
LIST_BODY=$(echo "$LIST_RESPONSE" | head -n -1)

echo "HTTP状态码: $LIST_HTTP_CODE"
echo "响应内容:"
echo "$LIST_BODY" | jq . 2>/dev/null || echo "$LIST_BODY"

echo ""
echo "================================"

# 测试获取特定系统提示词详情
PROMPT_ID=1
echo "🔍 测试获取系统提示词详情 (ID: $PROMPT_ID)..."
echo ""
echo "📡 发送请求: GET $SERVER_URL/system-prompt/detail?id=$PROMPT_ID"

DETAIL_RESPONSE=$(curl -s -w "\n%{http_code}" \
    -X GET \
    -H "Content-Type: application/json" \
    "$SERVER_URL/system-prompt/detail?id=$PROMPT_ID")

DETAIL_HTTP_CODE=$(echo "$DETAIL_RESPONSE" | tail -n1)
DETAIL_BODY=$(echo "$DETAIL_RESPONSE" | head -n -1)

echo "HTTP状态码: $DETAIL_HTTP_CODE"
echo "响应内容:"
echo "$DETAIL_BODY" | jq . 2>/dev/null || echo "$DETAIL_BODY"

echo ""
echo "================================"

# 检查结果
if [ "$LIST_HTTP_CODE" = "200" ] && [ "$DETAIL_HTTP_CODE" = "200" ]; then
    echo "✅ 所有测试通过！系统提示词API工作正常"
    
    # 尝试提取并显示我们创建的测试提示词
    TEST_PROMPT=$(echo "$DETAIL_BODY" | jq -r '.data.content // empty' 2>/dev/null)
    if [ -n "$TEST_PROMPT" ]; then
        echo ""
        echo "📋 测试提示词内容: $TEST_PROMPT"
        echo "🎯 这个提示词包含模板变量 {{content}}，可以用于动态内容替换"
    fi
else
    echo "❌ 测试失败！"
    if [ "$LIST_HTTP_CODE" != "200" ]; then
        echo "   - 获取列表失败，HTTP状态码: $LIST_HTTP_CODE"
    fi
    if [ "$DETAIL_HTTP_CODE" != "200" ]; then
        echo "   - 获取详情失败，HTTP状态码: $DETAIL_HTTP_CODE"
    fi
fi

echo ""
echo "🏁 测试完成"
