#!/bin/bash

# 修改notes表parent_id字段为可空的脚本
# 使用方法：./scripts/modify_notes_parent_id_nullable.sh [环境]
# 环境参数：testing（默认）、pre、production

set -e  # 遇到错误立即退出

# 数据库连接配置
DB_HOST="rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"
DB_PORT="3306"
DB_USER="aishoucang_mysql"
DB_PASSWORD="Qtt\$123456"

# 根据环境参数选择数据库
ENVIRONMENT=${1:-testing}

case $ENVIRONMENT in
    "testing")
        DB_NAME="aishoucang_develop"
        ;;
    "pre")
        DB_NAME="aishoucang_pre"
        ;;
    "production")
        DB_NAME="aishoucang"
        ;;
    *)
        echo "错误：无效的环境参数。请使用 testing、pre 或 production"
        exit 1
        ;;
esac

echo "开始修改 $ENVIRONMENT 环境的数据库 $DB_NAME 中notes表的parent_id字段..."

# 检查mysql命令是否可用
if ! command -v mysql &> /dev/null; then
    echo "错误：未找到mysql命令。请确保已安装MySQL客户端。"
    exit 1
fi

# 检查当前parent_id字段的约束
echo "检查当前parent_id字段的约束..."
CURRENT_NULLABLE=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -se "
SELECT IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = '$DB_NAME' 
AND TABLE_NAME = 'notes' 
AND COLUMN_NAME = 'parent_id'
")

if [ "$CURRENT_NULLABLE" = "YES" ]; then
    echo "parent_id字段已经是可空的，无需修改。"
    exit 0
fi

echo "parent_id字段当前不可空，开始修改..."

# 执行数据库修改
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" << EOF
-- 修改parent_id字段为可空
ALTER TABLE notes MODIFY COLUMN parent_id BIGINT UNSIGNED NULL;
EOF

if [ $? -eq 0 ]; then
    echo "成功修改 $ENVIRONMENT 环境的notes表parent_id字段为可空！"
else
    echo "修改parent_id字段失败！"
    exit 1
fi

# 验证修改是否成功
echo "验证修改是否成功..."
UPDATED_NULLABLE=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -se "
SELECT IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = '$DB_NAME' 
AND TABLE_NAME = 'notes' 
AND COLUMN_NAME = 'parent_id'
")

if [ "$UPDATED_NULLABLE" = "YES" ]; then
    echo "验证成功：parent_id字段已成功修改为可空。"
else
    echo "验证失败：parent_id字段未能正确修改。"
    exit 1
fi

echo "数据库修改完成！"
