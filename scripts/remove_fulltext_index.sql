-- 删除bookmarks表的全文索引idx_bookmarks_search（如果存在）
-- 这个脚本是可选的，用于清理之前创建的全文索引

-- 检查索引是否存在，如果存在则删除
SET @index_exists = (
    SELECT COUNT(*)
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = 'bookmarks'
    AND index_name = 'idx_bookmarks_search'
);

-- 如果索引存在，则删除它
SET @sql = IF(@index_exists > 0, 
    'ALTER TABLE bookmarks DROP INDEX idx_bookmarks_search', 
    'SELECT "索引 idx_bookmarks_search 不存在，无需删除" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
