#!/bin/bash

# 创建测试环境系统提示词脚本
# 使用方法：./scripts/create_test_system_prompt.sh [环境]
# 环境参数：testing（默认）、pre、production

set -e  # 遇到错误立即退出

# 数据库连接配置
DB_HOST="rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"
DB_PORT="3306"
DB_USER="aishoucang_mysql"
DB_PASSWORD="Qtt\$123456"

# 根据环境参数选择数据库
ENVIRONMENT=${1:-testing}

case $ENVIRONMENT in
    "testing")
        DB_NAME="aishoucang_develop"
        ;;
    "pre")
        DB_NAME="aishoucang_pre"
        ;;
    "production")
        DB_NAME="aishoucang"
        ;;
    *)
        echo "错误：无效的环境参数。请使用 testing、pre 或 production"
        exit 1
        ;;
esac

echo "开始向 $ENVIRONMENT 环境的数据库 $DB_NAME 创建测试系统提示词..."

# 检查mysql命令是否可用
if ! command -v mysql &> /dev/null; then
    echo "错误：未找到mysql命令。请确保已安装MySQL客户端。"
    exit 1
fi

# 系统提示词数据
TITLE="测试问候提示词"
CONTENT="你好， {{content}}"
CATEGORY="测试"
TAGS="测试,问候,模板"

# 检查是否已存在相同标题的系统提示词
EXISTING_COUNT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -se "
SELECT COUNT(*) FROM system_prompts WHERE title = '$TITLE'
")

if [ "$EXISTING_COUNT" -gt 0 ]; then
    echo "警告：已存在标题为 '$TITLE' 的系统提示词，跳过创建。"
    
    # 获取现有的系统提示词ID
    PROMPT_ID=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -se "
    SELECT id FROM system_prompts WHERE title = '$TITLE' ORDER BY id DESC LIMIT 1
    ")
    
    echo "现有系统提示词ID: $PROMPT_ID"
else
    # 执行插入操作
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -e "
    INSERT INTO system_prompts (title, content, category, tags, is_enabled, sort_weight, created_at, updated_at) 
    VALUES ('$TITLE', '$CONTENT', '$CATEGORY', '$TAGS', 1, 0, NOW(), NOW())
    "
    
    if [ $? -eq 0 ]; then
        echo "成功向 $ENVIRONMENT 环境创建测试系统提示词！"
        
        # 获取插入的系统提示词ID
        PROMPT_ID=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -se "
        SELECT id FROM system_prompts WHERE title = '$TITLE' ORDER BY id DESC LIMIT 1
        ")
        
        echo "创建的系统提示词ID: $PROMPT_ID"
    else
        echo "创建测试系统提示词失败！"
        exit 1
    fi
fi

echo ""
echo "📋 系统提示词信息："
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "环境:     $ENVIRONMENT"
echo "ID:       $PROMPT_ID"
echo "标题:     $TITLE"
echo "内容:     $CONTENT"
echo "分类:     $CATEGORY"
echo "标签:     $TAGS"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "您可以通过以下接口测试："
echo "GET /system-prompt/list"
echo "GET /system-prompt/detail?id=$PROMPT_ID"
