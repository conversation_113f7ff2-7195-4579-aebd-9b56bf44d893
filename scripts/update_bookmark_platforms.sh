#!/bin/bash

# 书签平台类型更新脚本
# 使用方法：./scripts/update_bookmark_platforms.sh [environment]
# environment: testing, pre, production

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== 书签平台类型更新脚本 ===${NC}"
echo ""

# 获取环境参数
ENVIRONMENT=${1:-""}

if [ -z "$ENVIRONMENT" ]; then
    echo -e "${YELLOW}请选择要更新的环境:${NC}"
    echo "1) 测试环境 (testing)"
    echo "2) 预发布环境 (pre)"
    echo "3) 生产环境 (production)"
    echo ""
    read -p "请输入选择 (1-3): " choice
    
    case $choice in
        1)
            ENVIRONMENT="testing"
            ;;
        2)
            ENVIRONMENT="pre"
            ;;
        3)
            ENVIRONMENT="production"
            ;;
        *)
            echo -e "${RED}无效选择${NC}"
            exit 1
            ;;
    esac
fi

# 验证环境参数
case $ENVIRONMENT in
    testing|pre|production)
        ;;
    *)
        echo -e "${RED}错误: 无效的环境参数 '$ENVIRONMENT'${NC}"
        echo "支持的环境: testing, pre, production"
        exit 1
        ;;
esac

echo -e "${BLUE}目标环境: $ENVIRONMENT${NC}"

# 根据环境设置数据库名称
case $ENVIRONMENT in
    testing)
        DB_NAME="aishoucang_develop"
        ;;
    pre)
        DB_NAME="aishoucang_pre"
        ;;
    production)
        DB_NAME="aishoucang"
        ;;
esac

echo -e "${BLUE}目标数据库: $DB_NAME${NC}"
echo ""

# 生产环境额外确认
if [ "$ENVIRONMENT" = "production" ]; then
    echo -e "${RED}⚠️  警告: 您即将在生产环境执行更新操作！${NC}"
    echo -e "${RED}这将直接修改生产数据库中的书签数据${NC}"
    echo ""
    read -p "确认要在生产环境执行更新吗？(yes/no): " confirm
    if [ "$confirm" != "yes" ]; then
        echo -e "${YELLOW}操作已取消${NC}"
        exit 0
    fi
    echo ""
fi

# 检查Rust项目是否存在
if [ ! -f "Cargo.toml" ]; then
    echo -e "${RED}错误: 当前目录不是Rust项目根目录${NC}"
    echo "请在项目根目录执行此脚本"
    exit 1
fi

# 检查更新脚本是否存在
if [ ! -f "src/bin/update_bookmark_platforms.rs" ]; then
    echo -e "${RED}错误: 更新脚本不存在: src/bin/update_bookmark_platforms.rs${NC}"
    exit 1
fi

echo -e "${YELLOW}开始编译和执行更新脚本...${NC}"
echo ""

# 编译并运行更新脚本
cargo run --bin update_bookmark_platforms "$ENVIRONMENT"

# 检查执行结果
if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 书签平台类型更新完成！${NC}"
    echo ""
    echo -e "${BLUE}更新说明:${NC}"
    echo "- 只更新了能识别平台的书签"
    echo "- 未知平台的书签保持 platform_type 为 NULL"
    echo "- 支持的平台: 小红书、抖音、B站、微信、豆瓣、拼多多、淘宝、京东"
else
    echo ""
    echo -e "${RED}❌ 更新过程中出现错误${NC}"
    exit 1
fi
