-- 添加平台类型字段到书签表
-- 执行前请确保已连接到正确的数据库
-- 此脚本可以安全地重复执行

-- 检查字段是否已存在
SET @column_exists = (
    SELECT COUNT(*)
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = 'bookmarks'
    AND column_name = 'platform_type'
);

-- 只有当字段不存在时才添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE bookmarks ADD COLUMN platform_type VARCHAR(20) NULL COMMENT ''平台类型：xiaohongshu, douyin, kuaishou, weibo, bilibili, zhihu, other''',
    'SELECT ''platform_type字段已存在，跳过添加'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查索引是否已存在
SET @index_exists = (
    SELECT COUNT(*)
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = 'bookmarks'
    AND index_name = 'idx_platform_type'
);

-- 只有当索引不存在时才添加
SET @sql = IF(@index_exists = 0,
    'ALTER TABLE bookmarks ADD INDEX idx_platform_type (platform_type)',
    'SELECT ''idx_platform_type索引已存在，跳过添加'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证结果
SELECT
    CASE
        WHEN COUNT(*) > 0 THEN '✓ platform_type字段存在'
        ELSE '✗ platform_type字段不存在'
    END as field_status
FROM information_schema.columns
WHERE table_schema = DATABASE()
AND table_name = 'bookmarks'
AND column_name = 'platform_type';

SELECT
    CASE
        WHEN COUNT(*) > 0 THEN '✓ idx_platform_type索引存在'
        ELSE '✗ idx_platform_type索引不存在'
    END as index_status
FROM information_schema.statistics
WHERE table_schema = DATABASE()
AND table_name = 'bookmarks'
AND index_name = 'idx_platform_type';

-- 显示字段详细信息
SELECT
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '可为空',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '注释'
FROM information_schema.columns
WHERE table_schema = DATABASE()
AND table_name = 'bookmarks'
AND column_name = 'platform_type';
