#!/bin/bash

# 在所有环境执行平台类型字段迁移
# 使用方法：./scripts/migrate_all_environments.sh

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== 在所有环境执行平台类型字段迁移 ===${NC}"
echo ""

# 检查 SQL 文件是否存在
if [ ! -f "scripts/add_platform_type_field.sql" ]; then
    echo -e "${RED}错误: SQL 脚本文件不存在: scripts/add_platform_type_field.sql${NC}"
    exit 1
fi

# 数据库连接信息
DB_HOST="rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"
DB_PORT="3306"
DB_USER="aishoucang_mysql"
DB_PASSWORD="Qtt\$123456"

# 环境列表
declare -a environments=("aishoucang_develop" "aishoucang_pre" "aishoucang")
declare -a env_names=("开发环境" "预发布环境" "生产环境")

echo -e "${YELLOW}将在以下环境执行迁移:${NC}"
for i in "${!environments[@]}"; do
    echo "$((i+1)). ${env_names[$i]} (${environments[$i]})"
done
echo ""

read -p "确认要在所有环境执行迁移吗？(yes/no): " confirm
if [ "$confirm" != "yes" ]; then
    echo "取消迁移"
    exit 0
fi

echo ""

# 逐个环境执行迁移
for i in "${!environments[@]}"; do
    DB_NAME="${environments[$i]}"
    ENV_NAME="${env_names[$i]}"
    
    echo -e "${BLUE}=== 正在迁移 $ENV_NAME ($DB_NAME) ===${NC}"
    
    # 执行迁移
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < scripts/add_platform_type_field.sql; then
        echo -e "${GREEN}✓ $ENV_NAME 迁移成功${NC}"
    else
        echo -e "${RED}✗ $ENV_NAME 迁移失败${NC}"
        exit 1
    fi
    
    echo ""
done

echo -e "${GREEN}🎉 所有环境迁移完成！${NC}"
echo ""
echo -e "${BLUE}已添加的字段:${NC}"
echo "- platform_type: VARCHAR(20) NULL"
echo "- 索引: idx_platform_type"
echo ""
echo -e "${BLUE}支持的平台类型:${NC}"
echo "- xiaohongshu (小红书)"
echo "- douyin (抖音)"
echo "- kuaishou (快手)"
echo "- weibo (微博)"
echo "- bilibili (B站)"
echo "- zhihu (知乎)"
echo "- other (其他)"
