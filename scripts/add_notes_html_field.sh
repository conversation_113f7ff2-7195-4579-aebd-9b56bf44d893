#!/bin/bash

# 数据库迁移脚本：为notes表添加html字段
# 使用方法：./scripts/add_notes_html_field.sh [环境]
# 环境参数：testing（默认）、pre、production

set -e  # 遇到错误立即退出

# 数据库连接配置
DB_HOST="rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"
DB_PORT="3306"
DB_USER="aishoucang_mysql"
DB_PASSWORD="Qtt\$123456"

# 根据环境参数选择数据库
ENVIRONMENT=${1:-testing}

case $ENVIRONMENT in
    "testing")
        DB_NAME="aishoucang_develop"
        ;;
    "pre")
        DB_NAME="aishoucang_pre"
        ;;
    "production")
        DB_NAME="aishoucang"
        ;;
    *)
        echo "错误：无效的环境参数。请使用 testing、pre 或 production"
        exit 1
        ;;
esac

echo "开始为 $ENVIRONMENT 环境的数据库 $DB_NAME 添加notes表的html字段..."

# 检查mysql命令是否可用
if ! command -v mysql &> /dev/null; then
    echo "错误：未找到mysql命令。请确保已安装MySQL客户端。"
    exit 1
fi

# 检查html字段是否已存在
echo "检查html字段是否已存在..."
COLUMN_EXISTS=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -se "
SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = '$DB_NAME' 
AND TABLE_NAME = 'notes' 
AND COLUMN_NAME = 'html'
")

if [ "$COLUMN_EXISTS" -eq 1 ]; then
    echo "html字段已存在，无需添加。"
    exit 0
fi

echo "html字段不存在，开始添加..."

# 执行数据库迁移
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" << EOF
-- 添加html字段（先设为可空）
ALTER TABLE notes ADD COLUMN html LONGTEXT;

-- 为现有记录设置默认值
UPDATE notes SET html = '' WHERE html IS NULL;

-- 修改字段为NOT NULL
ALTER TABLE notes MODIFY COLUMN html LONGTEXT NOT NULL;
EOF

if [ $? -eq 0 ]; then
    echo "成功为 $ENVIRONMENT 环境的notes表添加html字段！"
else
    echo "添加html字段失败！"
    exit 1
fi

# 验证字段是否添加成功
echo "验证字段是否添加成功..."
COLUMN_EXISTS_AFTER=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -se "
SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = '$DB_NAME' 
AND TABLE_NAME = 'notes' 
AND COLUMN_NAME = 'html'
")

if [ "$COLUMN_EXISTS_AFTER" -eq 1 ]; then
    echo "验证成功：html字段已成功添加到notes表。"
else
    echo "验证失败：html字段未能正确添加。"
    exit 1
fi

echo "数据库迁移完成！"
