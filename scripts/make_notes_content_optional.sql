-- 数据库迁移脚本：将notes表的content字段改为可选
-- 执行前请确保连接到正确的数据库环境

-- 检查content字段当前状态
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'notes' 
AND COLUMN_NAME = 'content';

-- 修改content字段为可空
ALTER TABLE notes MODIFY COLUMN content LONGTEXT NULL;

-- 验证字段修改是否成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'notes' 
AND COLUMN_NAME = 'content';

-- 查看notes表的完整结构
DESCRIBE notes;
