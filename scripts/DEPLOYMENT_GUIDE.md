# 书签平台类型更新部署指南

## 概述

本指南用于在生产环境部署书签平台类型更新脚本。该脚本已在测试环境验证成功。

## 测试环境验证结果

✅ **测试环境运行成功**
- 数据库: aishoucang_develop
- 找到书签: 148 个
- 成功更新: 139 个
- 未知平台: 9 个（保持NULL）

### 平台分布统计
- 豆瓣: 45 个
- 抖音: 43 个  
- 小红书: 27 个
- 拼多多: 8 个
- 微信: 8 个
- B站: 4 个
- 京东: 2 个
- 淘宝: 2 个

## 生产环境部署步骤

### 1. 准备工作

确保服务器环境具备以下条件：
- Rust 编译环境
- 网络连接到生产数据库
- 足够的磁盘空间用于编译

### 2. 上传代码

将更新后的代码上传到生产服务器：
```bash
# 上传整个项目目录到服务器
scp -r . root@*************:/root/aishoucang_rust/
```

### 3. 在生产服务器执行

```bash
# 登录生产服务器
ssh root@*************

# 进入项目目录
cd /root/aishoucang_rust

# 给脚本执行权限
chmod +x scripts/update_bookmark_platforms.sh

# 执行更新（生产环境）
./scripts/update_bookmark_platforms.sh production
```

### 4. 执行过程

脚本会：
1. 提示确认生产环境操作
2. 编译Rust代码
3. 连接生产数据库 (aishoucang)
4. 查询需要更新的书签
5. 显示平台分布统计
6. 批量更新书签平台类型
7. 显示更新结果

### 5. 预期结果

根据测试环境的结果，预计生产环境会：
- 找到大量需要更新的书签
- 成功识别并更新大部分书签的平台类型
- 少量未知平台的书签保持NULL

## 安全特性

✅ **多重安全保障**
- 生产环境需要二次确认
- 只更新platform_type为NULL的书签
- 批量处理避免长时间锁表
- 详细的执行日志
- 保守的更新策略

## 回滚方案

如果需要回滚，可以执行：
```sql
-- 将所有平台类型重置为NULL
UPDATE bookmarks SET platform_type = NULL WHERE platform_type IS NOT NULL;
```

## 监控建议

执行后建议检查：
1. 更新的书签数量是否合理
2. 各平台分布是否符合预期
3. 应用功能是否正常

## 联系信息

如有问题，请联系开发团队。

---

**重要提醒**: 
- 请在业务低峰期执行
- 建议先备份数据库
- 执行前确认数据库连接正常
