#!/bin/bash

# 数据库连接信息
DB_HOST="rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"
DB_PORT="3306"
DB_USER="aishoucang_mysql"
DB_PASS="Qtt\$123456"

# 要修改的数据库列表
DATABASES=("aishoucang" "aishoucang_develop" "aishoucang_pre")

# 脚本路径
SQL_SCRIPT="./sql/add_wechat_user_fields.sql"

# 检查SQL脚本是否存在
if [ ! -f "$SQL_SCRIPT" ]; then
    echo "错误: SQL脚本文件 $SQL_SCRIPT 不存在!"
    exit 1
fi

# 对每个数据库执行SQL脚本
for DB_NAME in "${DATABASES[@]}"; do
    echo "正在修改数据库 $DB_NAME 的表结构..."
    
    # 执行SQL脚本
    mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$SQL_SCRIPT"
    
    # 检查执行结果
    if [ $? -eq 0 ]; then
        echo "数据库 $DB_NAME 表结构修改成功!"
    else
        echo "错误: 数据库 $DB_NAME 表结构修改失败!"
    fi
    
    echo "-----------------------------------"
done

echo "所有数据库表结构修改完成!"
