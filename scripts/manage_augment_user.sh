#!/bin/bash

# Augment用户管理脚本
# 功能：
# 1. 查看用户信息
# 2. 修改用户会员状态
# 3. 支持多环境操作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 查询用户信息
query_user_info() {
    local account=$1
    local database=$2
    
    mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USERNAME" -p"$MYSQL_PASSWORD" "$database" \
        -e "SELECT id, account, is_member, member_expire_time, created_at, updated_at FROM augment_users WHERE account = '$account';" \
        2>/dev/null
}

# 更新用户会员状态
update_membership_status() {
    local account=$1
    local is_member=$2
    local database=$3
    
    local sql
    if [ "$is_member" = "false" ]; then
        # 设置为非会员，清空到期时间
        sql="UPDATE augment_users SET is_member = false, member_expire_time = NULL, updated_at = NOW() WHERE account = '$account';"
    else
        # 设置为会员，需要设置到期时间
        local expire_time
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            expire_time=$(date -v+30d '+%Y-%m-%d %H:%M:%S')
        else
            # Linux
            expire_time=$(date -d '+30 days' '+%Y-%m-%d %H:%M:%S')
        fi
        sql="UPDATE augment_users SET is_member = true, member_expire_time = '$expire_time', updated_at = NOW() WHERE account = '$account';"
    fi
    
    mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USERNAME" -p"$MYSQL_PASSWORD" "$database" -e "$sql" 2>/dev/null
}

echo "🔧 Augment用户管理工具"
echo "========================"

# 1. 选择环境
print_info "请选择要操作的环境："
echo "1) develop (开发环境)"
echo "2) pre (预发布环境)"
echo "3) production (生产环境)"
echo ""

while true; do
    read -p "请输入选项 (1-3): " env_choice
    case $env_choice in
        1)
            ENVIRONMENT="develop"
            DATABASE="aishoucang_develop"
            break
            ;;
        2)
            ENVIRONMENT="pre"
            DATABASE="aishoucang_pre"
            break
            ;;
        3)
            ENVIRONMENT="production"
            DATABASE="aishoucang"
            print_warning "您选择了生产环境，请谨慎操作！"
            read -p "确认要在生产环境操作吗？(y/N): " confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                break
            else
                print_info "已取消操作"
                exit 0
            fi
            ;;
        *)
            print_error "无效选项，请输入 1-3"
            ;;
    esac
done

print_success "已选择环境: $ENVIRONMENT"

# 2. 输入用户账号
echo ""
read -p "请输入要管理的用户账号: " account

if [[ -z "$account" ]]; then
    print_error "用户账号不能为空"
    exit 1
fi

# 数据库连接配置
MYSQL_HOST=${MYSQL_HOST:-"rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"}
MYSQL_PORT=${MYSQL_PORT:-3306}
MYSQL_USERNAME=${MYSQL_USERNAME:-"aishoucang_mysql"}
MYSQL_PASSWORD=${MYSQL_PASSWORD:-"Qtt\$123456"}

# 3. 查询用户信息
print_info "正在查询用户信息..."
user_info=$(query_user_info "$account" "$DATABASE")

if [[ -z "$user_info" ]] || [[ "$user_info" == *"Empty set"* ]]; then
    print_error "用户不存在: $account"
    exit 1
fi

echo ""
print_success "找到用户信息："
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "$user_info"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 4. 选择操作
echo ""
print_info "请选择要执行的操作："
echo "1) 设置为会员 (30天有效期)"
echo "2) 设置为非会员"
echo "3) 仅查看信息，不做修改"
echo ""

while true; do
    read -p "请输入选项 (1-3): " action_choice
    case $action_choice in
        1)
            ACTION="set_member"
            ACTION_DESC="设置为会员"
            break
            ;;
        2)
            ACTION="set_non_member"
            ACTION_DESC="设置为非会员"
            break
            ;;
        3)
            print_info "操作完成，未做任何修改"
            exit 0
            ;;
        *)
            print_error "无效选项，请输入 1-3"
            ;;
    esac
done

# 5. 确认操作
echo ""
print_warning "即将执行操作："
print_info "环境: $ENVIRONMENT"
print_info "用户: $account"
print_info "操作: $ACTION_DESC"

echo ""
read -p "确认执行此操作吗？(y/N): " final_confirm
if [[ ! $final_confirm =~ ^[Yy]$ ]]; then
    print_info "已取消操作"
    exit 0
fi

# 6. 执行操作
print_info "正在执行操作..."

if [ "$ACTION" = "set_member" ]; then
    update_membership_status "$account" "true" "$DATABASE"
elif [ "$ACTION" = "set_non_member" ]; then
    update_membership_status "$account" "false" "$DATABASE"
fi

# 7. 验证结果
print_info "正在验证操作结果..."
updated_user_info=$(query_user_info "$account" "$DATABASE")

echo ""
print_success "✨ 操作完成！"
echo ""
echo "📋 更新后的用户信息："
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "$updated_user_info"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

print_success "用户 $account 的会员状态已成功更新！"
