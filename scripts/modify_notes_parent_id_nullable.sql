-- 修改notes表的parent_id字段为可空
-- 执行前请确保连接到正确的数据库环境

-- 检查当前parent_id字段的约束
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'notes' 
AND COLUMN_NAME = 'parent_id';

-- 修改parent_id字段为可空
ALTER TABLE notes MODIFY COLUMN parent_id BIGINT UNSIGNED NULL;

-- 验证修改结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'notes' 
AND COLUMN_NAME = 'parent_id';

-- 查看notes表的完整结构
DESCRIBE notes;
