# 数据库迁移脚本

## 为notes表添加html字段

### 脚本文件
- `add_notes_html_field.sh` - Shell脚本版本（推荐）
- `add_notes_html_field.sql` - SQL脚本版本

### 使用方法

#### 方法1：使用Shell脚本（推荐）

```bash
# 在测试环境执行（默认）
./scripts/add_notes_html_field.sh

# 在测试环境执行（显式指定）
./scripts/add_notes_html_field.sh testing

# 在预发布环境执行
./scripts/add_notes_html_field.sh pre

# 在生产环境执行
./scripts/add_notes_html_field.sh production
```

#### 方法2：使用SQL脚本

```bash
# 连接到测试环境数据库
mysql -h rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com -P 3306 -u aishoucang_mysql -p aishoucang_develop < scripts/add_notes_html_field.sql

# 连接到预发布环境数据库
mysql -h rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com -P 3306 -u aishoucang_mysql -p aishoucang_pre < scripts/add_notes_html_field.sql

# 连接到生产环境数据库
mysql -h rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com -P 3306 -u aishoucang_mysql -p aishoucang < scripts/add_notes_html_field.sql
```

### 脚本功能

1. **检查字段是否存在**：避免重复添加字段
2. **安全添加字段**：
   - 先添加可空的LONGTEXT字段
   - 为现有记录设置默认值（空字符串）
   - 修改字段为NOT NULL
3. **验证结果**：确认字段添加成功

---

## 平台类型字段迁移

为书签表添加 `platform_type` 字段，支持按平台类型过滤书签。

### 文件说明

- `add_platform_type_field.sql` - SQL迁移脚本
- `migrate_platform_type.sh` - 单环境迁移脚本
- `migrate_all_environments.sh` - 所有环境迁移脚本

### 使用方法

#### 方法1：在所有环境执行迁移（推荐）

```bash
# 给脚本添加执行权限
chmod +x scripts/migrate_all_environments.sh

# 执行迁移
./scripts/migrate_all_environments.sh
```

#### 方法2：单独在某个环境执行迁移

```bash
# 给脚本添加执行权限
chmod +x scripts/migrate_platform_type.sh

# 执行迁移（会提示选择环境）
./scripts/migrate_platform_type.sh
```

#### 方法3：手动执行SQL

```bash
# 连接到数据库并执行SQL文件
mysql -h rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com \
      -P 3306 \
      -u aishoucang_mysql \
      -p \
      aishoucang_develop < scripts/add_platform_type_field.sql
```

### 迁移内容

#### 添加的字段
- **字段名**: `platform_type`
- **类型**: `VARCHAR(20) NULL`
- **注释**: 平台类型：xiaohongshu, douyin, bilibili, wechat, douban, pinduoduo, taobao, jingdong

#### 添加的索引
- **索引名**: `idx_platform_type`
- **字段**: `platform_type`

### 支持的平台类型

| 值 | 平台名称 |
|---|---|
| `xiaohongshu` | 小红书 |
| `douyin` | 抖音 |
| `bilibili` | B站 |
| `wechat` | 微信 |
| `douban` | 豆瓣 |
| `pinduoduo` | 拼多多 |
| `taobao` | 淘宝 |
| `jingdong` | 京东 |

### 安全特性

- ✅ **幂等性**: 脚本可以安全地重复执行
- ✅ **条件检查**: 只有当字段/索引不存在时才会添加
- ✅ **向后兼容**: 字段为可选，不影响现有数据
- ✅ **环境确认**: 生产环境需要额外确认

## 平台类型更新迁移

### 更新脚本
- **脚本文件**: `scripts/update_platform_type_values.sql`
- **执行脚本**: `scripts/migrate_platform_type_update.sh`

### 更新内容
- 更新字段注释以反映新的支持平台
- 将旧平台类型映射到新平台类型：
  - 快手 (`kuaishou`) → 抖音 (`douyin`)
  - 微博 (`weibo`) → 微信 (`wechat`)
  - 知乎 (`zhihu`) → 豆瓣 (`douban`)
  - 其他 (`other`) → 小红书 (`xiaohongshu`)

### 验证迁移结果

执行迁移后，可以通过以下SQL验证：

```sql
-- 检查字段是否存在
DESCRIBE bookmarks;

-- 查看字段详细信息
SELECT
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM information_schema.columns
WHERE table_schema = DATABASE()
AND table_name = 'bookmarks'
AND column_name = 'platform_type';

-- 检查索引是否存在
SHOW INDEX FROM bookmarks WHERE Key_name = 'idx_platform_type';
```

### 注意事项

1. **执行前备份**: 建议在生产环境执行前先备份数据库
2. **网络连接**: 确保能够连接到阿里云RDS实例
3. **权限检查**: 确保数据库用户有ALTER TABLE权限
4. **环境确认**: 仔细确认要迁移的数据库环境
