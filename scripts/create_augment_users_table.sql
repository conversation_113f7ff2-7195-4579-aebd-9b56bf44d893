-- 创建 augment_users 表
-- 用于存储 Augment 用户信息，包括账号、密码、会员状态等

CREATE TABLE IF NOT EXISTS augment_users (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    account VARCHAR(100) NOT NULL COMMENT '账号',
    password VARCHAR(255) NOT NULL COMMENT '密码（哈希值）',
    is_member BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否是会员',
    member_expire_time TIMESTAMP NULL COMMENT '会员到期时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY idx_account (account) COMMENT '账号唯一索引',
    KEY idx_is_member (is_member) COMMENT '会员状态索引',
    KEY idx_member_expire (member_expire_time) COMMENT '会员到期时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Augment用户表';
