#!/bin/bash

# 测试创建笔记接口
# 使用方法: ./test_create_note_api.sh [server_url] [token]

# 默认配置
SERVER_URL=${1:-"http://localhost:8080"}
TOKEN=${2:-"your_test_token_here"}

echo "🚀 测试创建笔记接口"
echo "服务器地址: $SERVER_URL"
echo "Token: $TOKEN"
echo "================================"

# 测试数据
TEST_DATA='{
    "title": "API测试笔记",
    "cover": "https://example.com/test-cover.jpg",
    "desc": "这是通过API创建的测试笔记",
    "content": "这是笔记的详细内容，包含了一些测试数据。",
    "html": "<h1>测试笔记</h1><p>这是HTML格式的内容</p>"
}'

echo "📝 发送创建笔记请求..."
echo "请求数据:"
echo "$TEST_DATA" | jq .

echo ""
echo "📡 发送请求..."

# 发送请求
RESPONSE=$(curl -s -w "\n%{http_code}" \
    -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d "$TEST_DATA" \
    "$SERVER_URL/note/create")

# 分离响应体和状态码
HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
RESPONSE_BODY=$(echo "$RESPONSE" | head -n -1)

echo "HTTP状态码: $HTTP_CODE"
echo "响应内容:"
echo "$RESPONSE_BODY" | jq .

# 检查结果
if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ 测试成功！笔记创建成功"
    
    # 尝试提取笔记ID
    NOTE_ID=$(echo "$RESPONSE_BODY" | jq -r '.data.id // empty')
    if [ -n "$NOTE_ID" ]; then
        echo "📋 创建的笔记ID: $NOTE_ID"
        
        # 测试获取笔记详情
        echo ""
        echo "🔍 测试获取笔记详情..."
        DETAIL_RESPONSE=$(curl -s -w "\n%{http_code}" \
            -H "Authorization: Bearer $TOKEN" \
            "$SERVER_URL/note/detail?id=$NOTE_ID")
        
        DETAIL_HTTP_CODE=$(echo "$DETAIL_RESPONSE" | tail -n1)
        DETAIL_BODY=$(echo "$DETAIL_RESPONSE" | head -n -1)
        
        echo "获取详情HTTP状态码: $DETAIL_HTTP_CODE"
        echo "详情响应:"
        echo "$DETAIL_BODY" | jq .
        
        if [ "$DETAIL_HTTP_CODE" = "200" ]; then
            echo "✅ 获取笔记详情成功！"
        else
            echo "❌ 获取笔记详情失败"
        fi
    fi
else
    echo "❌ 测试失败！HTTP状态码: $HTTP_CODE"
    
    # 分析错误
    ERROR_CODE=$(echo "$RESPONSE_BODY" | jq -r '.code // empty')
    ERROR_MESSAGE=$(echo "$RESPONSE_BODY" | jq -r '.message // empty')
    
    if [ -n "$ERROR_CODE" ]; then
        echo "错误码: $ERROR_CODE"
        echo "错误信息: $ERROR_MESSAGE"
        
        case $ERROR_CODE in
            203)
                echo "💡 提示: 请检查Token是否有效"
                ;;
            403)
                echo "💡 提示: 请检查请求参数是否正确"
                ;;
            101)
                echo "💡 提示: 服务可能未启动或数据库连接失败"
                ;;
            *)
                echo "💡 提示: 未知错误，请检查服务器日志"
                ;;
        esac
    fi
fi

echo ""
echo "================================"
echo "测试完成"
