#!/bin/bash

# 数据库迁移脚本：将notes表的content字段改为可选
# 使用方法: ./make_notes_content_optional.sh [environment]

# 设置环境变量
ENVIRONMENT=${1:-"development"}

echo "🚀 开始将notes表的content字段改为可选"
echo "环境: $ENVIRONMENT"
echo "================================"

# 根据环境设置数据库连接参数
case $ENVIRONMENT in
    "development")
        DB_HOST="localhost"
        DB_PORT="3306"
        DB_USER="root"
        DB_PASSWORD="123456"
        DB_NAME="aishoucang"
        ;;
    "production")
        echo "⚠️  生产环境迁移需要额外确认"
        read -p "确认要在生产环境执行迁移吗？(yes/no): " confirm
        if [ "$confirm" != "yes" ]; then
            echo "取消迁移"
            exit 0
        fi
        # 生产环境配置（请根据实际情况修改）
        DB_HOST="your_prod_host"
        DB_PORT="3306"
        DB_USER="your_prod_user"
        DB_PASSWORD="your_prod_password"
        DB_NAME="your_prod_db"
        ;;
    *)
        echo "❌ 不支持的环境: $ENVIRONMENT"
        echo "支持的环境: development, production"
        exit 1
        ;;
esac

# 检查MySQL连接
echo "检查数据库连接..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ 无法连接到数据库，请检查连接参数"
    exit 1
fi

echo "✅ 数据库连接成功"

# 检查notes表是否存在
echo "检查notes表是否存在..."
TABLE_EXISTS=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -se "
SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = '$DB_NAME' 
AND TABLE_NAME = 'notes'
")

if [ "$TABLE_EXISTS" -eq 0 ]; then
    echo "❌ notes表不存在"
    exit 1
fi

echo "✅ notes表存在"

# 检查content字段当前状态
echo "检查content字段当前状态..."
FIELD_NULLABLE=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -se "
SELECT IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = '$DB_NAME' 
AND TABLE_NAME = 'notes' 
AND COLUMN_NAME = 'content'
")

if [ "$FIELD_NULLABLE" = "YES" ]; then
    echo "✅ content字段已经是可空的，无需修改"
    exit 0
fi

echo "📝 content字段当前为NOT NULL，开始修改..."

# 执行数据库迁移
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" << EOF
-- 修改content字段为可空
ALTER TABLE notes MODIFY COLUMN content LONGTEXT NULL;
EOF

if [ $? -eq 0 ]; then
    echo "✅ 成功将 $ENVIRONMENT 环境的notes表content字段改为可选！"
    
    # 验证修改结果
    echo "验证修改结果..."
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -e "
    SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = '$DB_NAME' 
    AND TABLE_NAME = 'notes' 
    AND COLUMN_NAME = 'content';
    "
else
    echo "❌ 修改content字段失败！"
    exit 1
fi

echo "🎉 迁移完成！"
