#!/bin/bash

# 数据库迁移脚本：添加平台类型字段
# 使用方法：./scripts/migrate_platform_type.sh

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== 书签平台类型字段迁移脚本 ===${NC}"
echo ""

# 检查 SQL 文件是否存在
if [ ! -f "scripts/add_platform_type_field.sql" ]; then
    echo -e "${RED}错误: SQL 脚本文件不存在: scripts/add_platform_type_field.sql${NC}"
    exit 1
fi

# 数据库连接信息
DB_HOST="rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"
DB_PORT="3306"
DB_USER="aishoucang_mysql"
DB_PASSWORD="Qtt\$123456"

echo -e "${YELLOW}请选择要迁移的数据库环境:${NC}"
echo "1) 开发环境 (aishoucang_develop)"
echo "2) 预发布环境 (aishoucang_pre)"
echo "3) 生产环境 (aishoucang)"
echo "4) 自定义数据库"
echo ""
read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        DB_NAME="aishoucang_develop"
        ;;
    2)
        DB_NAME="aishoucang_pre"
        ;;
    3)
        DB_NAME="aishoucang"
        echo -e "${RED}警告: 您选择了生产环境！${NC}"
        read -p "确认要在生产环境执行迁移吗？(yes/no): " confirm
        if [ "$confirm" != "yes" ]; then
            echo "取消迁移"
            exit 0
        fi
        ;;
    4)
        read -p "请输入数据库主机: " DB_HOST
        read -p "请输入数据库端口: " DB_PORT
        read -p "请输入数据库用户名: " DB_USER
        read -s -p "请输入数据库密码: " DB_PASSWORD
        echo ""
        read -p "请输入数据库名: " DB_NAME
        ;;
    *)
        echo -e "${RED}无效选择${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${BLUE}连接信息:${NC}"
echo "主机: $DB_HOST:$DB_PORT"
echo "用户: $DB_USER"
echo "数据库: $DB_NAME"
echo ""

# 执行迁移
echo -e "${YELLOW}执行数据库迁移...${NC}"
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < scripts/add_platform_type_field.sql

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 迁移执行成功！${NC}"
    echo ""
    echo -e "${BLUE}已添加的字段:${NC}"
    echo "- platform_type: VARCHAR(20) NULL"
    echo "- 索引: idx_platform_type"
    echo ""
    echo -e "${BLUE}支持的平台类型:${NC}"
    echo "- xiaohongshu (小红书)"
    echo "- douyin (抖音)"
    echo "- kuaishou (快手)"
    echo "- weibo (微博)"
    echo "- bilibili (B站)"
    echo "- zhihu (知乎)"
    echo "- other (其他)"
else
    echo -e "${RED}✗ 迁移执行失败！${NC}"
    exit 1
fi
