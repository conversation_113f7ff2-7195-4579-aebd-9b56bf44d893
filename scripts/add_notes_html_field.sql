-- 数据库迁移脚本：为notes表添加html字段
-- 执行前请确保连接到正确的数据库环境

-- 检查html字段是否已存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'html字段已存在'
        ELSE 'html字段不存在，需要添加'
    END AS status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'notes' 
AND COLUMN_NAME = 'html';

-- 添加html字段（如果不存在）
-- 注意：如果字段已存在，这个语句会报错，这是正常的

-- 步骤1：添加html字段（先设为可空）
ALTER TABLE notes ADD COLUMN html LONGTEXT;

-- 步骤2：为现有记录设置默认值
UPDATE notes SET html = '' WHERE html IS NULL;

-- 步骤3：修改字段为NOT NULL
ALTER TABLE notes MODIFY COLUMN html LONGTEXT NOT NULL;

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'notes' 
AND COLUMN_NAME = 'html';

-- 查看notes表的完整结构
DESCRIBE notes;
