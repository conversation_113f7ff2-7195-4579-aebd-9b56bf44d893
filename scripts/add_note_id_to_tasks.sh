#!/bin/bash

# 为tasks表添加note_id字段的脚本
# 执行时间: 2024-12-28

# 检查参数
if [ $# -eq 0 ]; then
    echo "使用方法: $0 <environment>"
    echo "环境选项: develop, production"
    exit 1
fi

ENVIRONMENT=$1

# 根据环境设置数据库配置
case $ENVIRONMENT in
    "develop")
        DB_HOST="rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"
        DB_PORT="3306"
        DB_USER="aishoucang_mysql"
        DB_PASSWORD="Qtt\$123456"
        DB_NAME="aishoucang_develop"
        ;;
    "production")
        DB_HOST="rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"
        DB_PORT="3306"
        DB_USER="aishoucang_mysql"
        DB_PASSWORD="Qtt\$123456"
        DB_NAME="aishoucang_production"
        ;;
    *)
        echo "❌ 无效的环境: $ENVIRONMENT"
        echo "支持的环境: develop, production"
        exit 1
        ;;
esac

echo "🚀 开始为 $ENVIRONMENT 环境的tasks表添加note_id字段..."

# 检查note_id字段是否已存在
echo "检查note_id字段是否已存在..."
COLUMN_EXISTS=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -se "
SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = '$DB_NAME' 
AND TABLE_NAME = 'tasks' 
AND COLUMN_NAME = 'note_id'
")

if [ "$COLUMN_EXISTS" -eq 1 ]; then
    echo "note_id字段已存在，无需添加。"
    exit 0
fi

echo "note_id字段不存在，开始添加..."

# 执行数据库迁移
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" < migrations/add_note_id_to_tasks.sql

if [ $? -eq 0 ]; then
    echo "✅ 成功为 $ENVIRONMENT 环境的tasks表添加note_id字段！"
    
    # 验证修改结果
    echo "验证修改结果..."
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -e "
    SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT,
        COLUMN_COMMENT
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = '$DB_NAME' 
    AND TABLE_NAME = 'tasks' 
    AND COLUMN_NAME = 'note_id';
    "
else
    echo "❌ 添加note_id字段失败！"
    exit 1
fi

echo "🎉 迁移完成！"
