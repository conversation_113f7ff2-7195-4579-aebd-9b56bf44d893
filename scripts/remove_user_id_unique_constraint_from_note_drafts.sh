#!/bin/bash

# 移除笔记草稿表的用户ID唯一约束的脚本
# 执行时间: 2025-06-30
# 目的: 允许同一用户有多个草稿（针对不同的笔记），但同一笔记只能有一个草稿

# MySQL客户端路径
MYSQL_CMD="/opt/homebrew/bin/mysql"

# 检查MySQL客户端是否存在
if [ ! -f "$MYSQL_CMD" ]; then
    echo "❌ MySQL客户端未找到: $MYSQL_CMD"
    echo "请检查MySQL是否已安装或更新脚本中的路径"
    exit 1
fi

# 检查参数
if [ $# -eq 0 ]; then
    echo "使用方法: $0 <environment>"
    echo "环境选项: develop, production"
    exit 1
fi

ENVIRONMENT=$1

# 根据环境设置数据库配置
case $ENVIRONMENT in
    "develop")
        DB_HOST="rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"
        DB_PORT="3306"
        DB_USER="aishoucang_mysql"
        DB_PASSWORD="Qtt\$123456"
        DB_NAME="aishoucang_develop"
        ;;
    "production")
        DB_HOST="rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"
        DB_PORT="3306"
        DB_USER="aishoucang_mysql"
        DB_PASSWORD="Qtt\$123456"
        DB_NAME="aishoucang_production"
        ;;
    *)
        echo "❌ 无效的环境: $ENVIRONMENT"
        echo "支持的环境: develop, production"
        exit 1
        ;;
esac

echo "🚀 开始为 $ENVIRONMENT 环境的note_drafts表移除用户ID唯一约束..."

# 检查唯一约束是否存在
echo "检查用户ID唯一约束是否存在..."
UNIQUE_CONSTRAINT_EXISTS=$($MYSQL_CMD -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -se "
SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
WHERE TABLE_SCHEMA = '$DB_NAME'
AND TABLE_NAME = 'note_drafts'
AND INDEX_NAME = 'idx_user_id'
AND NON_UNIQUE = 0
")

if [ "$UNIQUE_CONSTRAINT_EXISTS" -eq 0 ]; then
    echo "用户ID唯一约束不存在，无需移除。"
    
    # 检查是否有普通索引
    INDEX_EXISTS=$($MYSQL_CMD -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -se "
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = '$DB_NAME'
    AND TABLE_NAME = 'note_drafts'
    AND INDEX_NAME = 'idx_user_id'
    ")

    if [ "$INDEX_EXISTS" -eq 0 ]; then
        echo "添加普通索引以优化查询性能..."
        $MYSQL_CMD -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -e "
        ALTER TABLE note_drafts ADD KEY idx_user_id (user_id)
        "
        if [ $? -eq 0 ]; then
            echo "✅ 成功添加普通索引！"
        else
            echo "❌ 添加普通索引失败！"
            exit 1
        fi
    else
        echo "普通索引已存在。"
    fi
    
    exit 0
fi

echo "用户ID唯一约束存在，开始移除..."

# 执行数据库迁移
$MYSQL_CMD -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" < migrations/remove_user_id_unique_constraint_from_note_drafts.sql

if [ $? -eq 0 ]; then
    echo "✅ 成功为 $ENVIRONMENT 环境的note_drafts表移除用户ID唯一约束！"
    
    # 验证修改结果
    echo "验证修改结果..."
    echo "检查索引状态:"
    $MYSQL_CMD -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -e "
    SELECT
        INDEX_NAME,
        NON_UNIQUE,
        COLUMN_NAME
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = '$DB_NAME'
    AND TABLE_NAME = 'note_drafts'
    AND INDEX_NAME = 'idx_user_id'
    ORDER BY INDEX_NAME, SEQ_IN_INDEX;
    "

    echo "检查当前表结构:"
    $MYSQL_CMD -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -e "
    SHOW CREATE TABLE note_drafts;
    "
else
    echo "❌ 移除用户ID唯一约束失败！"
    exit 1
fi

echo "🎉 迁移完成！现在同一用户可以有多个草稿（针对不同的笔记）！"
