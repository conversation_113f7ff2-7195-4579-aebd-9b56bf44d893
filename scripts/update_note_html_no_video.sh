#!/bin/bash

# 更新笔记HTML内容脚本 - 去除视频标签
# 使用方法：./scripts/update_note_html_no_video.sh [环境]
# 环境参数：testing（默认）、pre、production

set -e  # 遇到错误立即退出

# 数据库连接配置
DB_HOST="rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"
DB_PORT="3306"
DB_USER="aishoucang_mysql"
DB_PASSWORD="Qtt\$123456"

# 根据环境参数选择数据库
ENVIRONMENT=${1:-testing}

case $ENVIRONMENT in
    "testing")
        DB_NAME="aishoucang_develop"
        ;;
    "pre")
        DB_NAME="aishoucang_pre"
        ;;
    "production")
        DB_NAME="aishoucang"
        ;;
    *)
        echo "错误：无效的环境参数。请使用 testing、pre 或 production"
        exit 1
        ;;
esac

echo "开始更新 $ENVIRONMENT 环境的数据库 $DB_NAME 中的笔记HTML内容（去除视频标签）..."

# 检查mysql命令是否可用
if ! command -v mysql &> /dev/null; then
    echo "错误：未找到mysql命令。请确保已安装MySQL客户端。"
    exit 1
fi

# 执行更新操作
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < scripts/update_note_html_no_video.sql

if [ $? -eq 0 ]; then
    echo "成功更新 $ENVIRONMENT 环境的笔记HTML内容（已去除视频标签）！"
    
    # 验证更新结果
    echo "验证更新结果..."
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -D"$DB_NAME" -se "
    SELECT id, title, CHAR_LENGTH(html) as html_length FROM notes WHERE id = 2;
    "
    
    echo "HTML内容已更新，所有视频标签已移除。"
else
    echo "更新笔记HTML内容失败！"
    exit 1
fi
