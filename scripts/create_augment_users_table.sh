#!/bin/bash

# 创建 augment_users 表的脚本
# 使用方法: ./create_augment_users_table.sh [环境]
# 环境参数: develop, pre, production (默认: develop)

set -e

# 获取环境参数，默认为 develop
ENVIRONMENT=${1:-develop}

# 根据环境设置数据库名称
case $ENVIRONMENT in
    "develop")
        DATABASE="aishoucang_develop"
        ;;
    "pre")
        DATABASE="aishoucang_pre"
        ;;
    "production")
        DATABASE="aishoucang"
        ;;
    *)
        echo "错误: 不支持的环境 '$ENVIRONMENT'"
        echo "支持的环境: develop, pre, production"
        exit 1
        ;;
esac

# 数据库连接配置
MYSQL_HOST=${MYSQL_HOST:-"rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"}
MYSQL_PORT=${MYSQL_PORT:-3306}
MYSQL_USERNAME=${MYSQL_USERNAME:-"aishoucang_mysql"}
MYSQL_PASSWORD=${MYSQL_PASSWORD:-"Qtt\$123456"}

echo "正在为环境 '$ENVIRONMENT' 创建 augment_users 表..."
echo "数据库: $DATABASE"

# 执行SQL脚本
mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USERNAME" -p"$MYSQL_PASSWORD" "$DATABASE" < "$(dirname "$0")/create_augment_users_table.sql"

if [ $? -eq 0 ]; then
    echo "✅ augment_users 表创建成功！"
else
    echo "❌ augment_users 表创建失败！"
    exit 1
fi
