# 书签平台类型更新脚本

## 概述

这个脚本用于更新线上书签表中的平台类型字段。测试环境已经有了平台信息，但生产环境的存量书签缺少平台类型，需要根据 `scheme_url` 字段来判断和更新。

## 文件说明

- `src/bin/update_bookmark_platforms.rs` - Rust更新脚本
- `scripts/update_bookmark_platforms.sh` - Shell执行脚本
- `scripts/README_update_platforms.md` - 本说明文档

## 平台识别逻辑

脚本会根据 `scheme_url` 字段判断书签属于哪个平台：

| 平台 | 识别规则 |
|------|----------|
| 小红书 | `xhsdiscover://` 开头 |
| 抖音 | `snssdk1128://` 开头 |
| B站 | `bilibili://` 开头 |
| 微信 | `weixin://` 开头 或 host 为 `mp.weixin.qq.com` |
| 拼多多 | `pinduoduo://` 开头 或 包含 `yangkeduo.com` |
| 淘宝 | `taobao://` 开头 或 包含 `taobao.com` |
| 京东 | `openapp.jdmobile://` 开头 或 包含 `jd.com` |
| 豆瓣 | 包含 `douban.com` |

## 使用方法

### 方法1: 使用Shell脚本（推荐）

```bash
# 交互式选择环境
./scripts/update_bookmark_platforms.sh

# 直接指定环境
./scripts/update_bookmark_platforms.sh testing     # 测试环境
./scripts/update_bookmark_platforms.sh pre         # 预发布环境
./scripts/update_bookmark_platforms.sh production  # 生产环境
```

### 方法2: 直接运行Rust脚本

```bash
# 编译并运行
cargo run --bin update_bookmark_platforms production
```

## 环境对应关系

| 环境参数 | 数据库名称 | 说明 |
|----------|------------|------|
| testing | aishoucang_develop | 测试环境 |
| pre | aishoucang_pre | 预发布环境 |
| production | aishoucang | 生产环境 |

## 安全特性

1. **生产环境确认**: 在生产环境执行时需要二次确认
2. **只读检查**: 先统计需要更新的数据，显示平台分布
3. **批量处理**: 每次处理100条记录，避免长时间锁表
4. **保守更新**: 只更新能识别的平台，未知平台保持NULL
5. **详细日志**: 显示更新进度和结果统计

## 执行流程

1. 连接到指定环境的数据库
2. 查询所有 `platform_type` 为 NULL 的书签
3. 统计各平台的书签数量
4. 批量更新可识别平台的书签
5. 显示更新结果统计

## 注意事项

- 脚本只会更新 `platform_type` 为 NULL 的书签
- 对于无法识别的平台，不会进行更新
- 建议先在测试环境验证，再在生产环境执行
- 生产环境执行前请确保数据库备份

## 示例输出

```
🚀 开始更新书签平台类型...
📊 连接到数据库: aishoucang
📋 找到 1250 个需要更新平台类型的书签

📈 平台分布统计:
  小红书 (xiaohongshu): 450 个
  抖音 (douyin): 320 个
  B站 (bilibili): 180 个
  微信 (wechat): 150 个
  淘宝 (taobao): 100 个
  京东 (jingdong): 50 个
  未知平台: 0 个

📝 已更新 100/1250 个书签
📝 已更新 200/1250 个书签
...
📝 已更新 1250/1250 个书签

🎉 更新完成！
✅ 总共更新了 1250 个书签的平台类型
```
