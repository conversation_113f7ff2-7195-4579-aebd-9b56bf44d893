#!/bin/bash

# 数据库连接信息
DB_HOST="rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"
DB_PORT="3306"
DB_USER="aishoucang_mysql"
DB_PASS="Qtt\$123456"

# 要修改的数据库列表
DATABASES=("aishoucang" "aishoucang_develop" "aishoucang_pre")

# 脚本路径
SQL_SCRIPT="./sql/update_tag_colors.sql"

# 检查SQL脚本是否存在
if [ ! -f "$SQL_SCRIPT" ]; then
    echo "错误: SQL脚本文件 $SQL_SCRIPT 不存在!"
    exit 1
fi

echo "开始更新标签表的颜色字段结构..."
echo "将单一的color字段拆分为background_color和text_color两个字段"
echo "=================================================="

# 对每个数据库执行SQL脚本
for DB_NAME in "${DATABASES[@]}"; do
    echo "正在修改数据库 $DB_NAME 的标签表结构..."
    
    # 执行SQL脚本
    mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$SQL_SCRIPT"
    
    # 检查执行结果
    if [ $? -eq 0 ]; then
        echo "数据库 $DB_NAME 标签表结构修改成功!"
        
        # 显示修改后的表结构
        echo "修改后的标签表结构:"
        mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASS" -e "DESCRIBE tags;" "$DB_NAME"
    else
        echo "错误: 数据库 $DB_NAME 标签表结构修改失败!"
    fi
    
    echo "-----------------------------------"
done

echo "所有数据库标签表结构修改完成!"
echo ""
echo "修改内容总结:"
echo "1. 添加了 background_color 字段用于存储背景颜色"
echo "2. 添加了 text_color 字段用于存储文字颜色"
echo "3. 将旧的 color 字段数据迁移到 background_color"
echo "4. 根据背景颜色亮度自动设置合适的文字颜色"
echo "5. 删除了旧的 color 字段"
