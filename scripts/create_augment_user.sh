#!/bin/bash

# 创建Augment用户的脚本
# 功能：
# 1. 选择环境
# 2. 输入用户名
# 3. 自动生成密码
# 4. 自动设置为会员（30天）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 生成随机密码
generate_password() {
    # 生成12位随机密码，包含大小写字母、数字和特殊字符
    openssl rand -base64 12 | tr -d "=+/" | cut -c1-12
}

# 获取当前时间戳（30天后）
get_expire_timestamp() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        date -v+30d '+%Y-%m-%d %H:%M:%S'
    else
        # Linux
        date -d '+30 days' '+%Y-%m-%d %H:%M:%S'
    fi
}

echo "🚀 Augment用户创建工具"
echo "========================"

# 1. 选择环境
print_info "请选择要创建用户的环境："
echo "1) develop (开发环境)"
echo "2) pre (预发布环境)"
echo "3) production (生产环境)"
echo ""

while true; do
    read -p "请输入选项 (1-3): " env_choice
    case $env_choice in
        1)
            ENVIRONMENT="develop"
            DATABASE="aishoucang_develop"
            break
            ;;
        2)
            ENVIRONMENT="pre"
            DATABASE="aishoucang_pre"
            break
            ;;
        3)
            ENVIRONMENT="production"
            DATABASE="aishoucang"
            print_warning "您选择了生产环境，请谨慎操作！"
            read -p "确认要在生产环境创建用户吗？(y/N): " confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                break
            else
                print_info "已取消操作"
                exit 0
            fi
            ;;
        *)
            print_error "无效选项，请输入 1-3"
            ;;
    esac
done

print_success "已选择环境: $ENVIRONMENT"

# 2. 输入用户名
echo ""
while true; do
    read -p "请输入用户名 (3-50个字符): " username
    
    # 验证用户名长度
    if [[ ${#username} -lt 3 || ${#username} -gt 50 ]]; then
        print_error "用户名长度必须在3-50个字符之间"
        continue
    fi
    
    # 验证用户名格式（只允许字母、数字、下划线、中划线）
    if [[ ! $username =~ ^[a-zA-Z0-9_-]+$ ]]; then
        print_error "用户名只能包含字母、数字、下划线和中划线"
        continue
    fi
    
    break
done

# 3. 生成密码
password=$(generate_password)
print_success "已生成密码: $password"

# 4. 计算会员到期时间
expire_time=$(get_expire_timestamp)
print_success "会员到期时间: $expire_time"

# 数据库连接配置
MYSQL_HOST=${MYSQL_HOST:-"rm-bp16o24rl8r7pdkjwvo.mysql.rds.aliyuncs.com"}
MYSQL_PORT=${MYSQL_PORT:-3306}
MYSQL_USERNAME=${MYSQL_USERNAME:-"aishoucang_mysql"}
MYSQL_PASSWORD=${MYSQL_PASSWORD:-"Qtt\$123456"}

echo ""
print_info "准备创建用户..."
print_info "环境: $ENVIRONMENT"
print_info "数据库: $DATABASE"
print_info "用户名: $username"
print_info "密码: $password"
print_info "会员状态: 是"
print_info "会员到期: $expire_time"

echo ""
read -p "确认创建用户吗？(y/N): " final_confirm
if [[ ! $final_confirm =~ ^[Yy]$ ]]; then
    print_info "已取消操作"
    exit 0
fi

# 创建SQL语句
SQL="INSERT INTO augment_users (account, password, is_member, member_expire_time, created_at, updated_at) 
VALUES ('$username', '$password', true, '$expire_time', NOW(), NOW());"

print_info "正在创建用户..."

# 执行SQL
if mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USERNAME" -p"$MYSQL_PASSWORD" "$DATABASE" -e "$SQL" 2>/dev/null; then
    echo ""
    print_success "🎉 用户创建成功！"
    echo ""
    echo "📋 用户信息："
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "环境:     $ENVIRONMENT"
    echo "用户名:   $username"
    echo "密码:     $password"
    echo "会员状态: 是"
    echo "到期时间: $expire_time"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    print_warning "请妥善保存用户名和密码信息！"
    
    # 可选：将信息保存到文件
    read -p "是否将用户信息保存到文件？(y/N): " save_to_file
    if [[ $save_to_file =~ ^[Yy]$ ]]; then
        filename="augment_user_${username}_$(date +%Y%m%d_%H%M%S).txt"
        cat > "$filename" << EOF
Augment用户信息
================
创建时间: $(date '+%Y-%m-%d %H:%M:%S')
环境:     $ENVIRONMENT
用户名:   $username
密码:     $password
会员状态: 是
到期时间: $expire_time
================
EOF
        print_success "用户信息已保存到: $filename"
    fi
    
else
    print_error "用户创建失败！"
    print_error "可能的原因："
    echo "  - 用户名已存在"
    echo "  - 数据库连接失败"
    echo "  - 权限不足"
    exit 1
fi

echo ""
print_info "您现在可以使用以下信息测试登录："
echo "POST /augment/users/login"
echo "{"
echo "  \"account\": \"$username\","
echo "  \"password\": \"$password\""
echo "}"
